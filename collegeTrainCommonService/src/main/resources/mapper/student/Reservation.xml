<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.student.repository.hana.ReservationAppoint">
    <insert id="addAppointment">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
            SELECT COLLEGE_TRAIN_APPOINT_RECORD_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_APPOINT_RECORD
        ("ID" ,
        "ASSESSED_ID" ,
        "APPOINT_START_DATE" ,
        "APPOINT_END_DATE" ,
        "APPOINT_START_TIME",
        "APPOINT_END_TIME" ,
         "STATUS",
        "PASSED","TYPE","COMPANY_TYPE",APPOINT_STATUS)
        values(
        #{ID} ,
        #{assessed_id},
        #{re.aStartDate} ,
        #{re.aEndDate} ,
        #{re.aStartTime},
        #{re.aEndTime} ,
        '未考核',
        false,
        #{re.type},(select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{re.employeeNo}),'1'
        )
    </insert>
    <select id="allInformation" resultType="com.htks.domain.student.dto.AppointTime">
        SELECT PRACTICAL_OPERATION_START_TIME as pStartTime,
               PRACTICAL_OPERATION_END_TIME   as pEndTime,
               EXCEPTION_DEFENSE_START_TIME   as eStartTime,
               EXCEPTION_DEFENSE_END_TIME     as eEndTime
        from COLLEGE_TRAIN_ASSESSED a
        WHERE a.EMPLOYEE_NO = #{no}
    </select>
<!--根据时间段获取当前时间段已预约人数-->
    <select id="timeAppoitGet" resultType="integer">
        SELECT COUNT(ID) FROM COLLEGE_TRAIN_APPOINT_RECORD a WHERE a.APPOINT_START_DATE = #{startDate} and
        type=#{type} and APPOINT_STATUS='1' and COMPANY_TYPE=(select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo})
        <if test="startTime != null and startTime!=''">
            AND a.APPOINT_START_TIME = #{startTime} AND a.APPOINT_END_TIME = #{endTime}
        </if>
    </select>

<!--获取某一类型已考核的人数-->
    <select id="assessedNumber" resultType="integer">
        SELECT COUNT(distinct a.ASSESSED_ID)
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
        WHERE STATUS = '已考核'
          and type = #{type} and COMPANY_TYPE = (select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo})
          and ASSESSED_ID in (SELECT id
                              FROM COLLEGE_TRAIN_ASSESSED
                              where BATCH =
                                    (select BATCH from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo}))

    </select>

    <select id="allNumber" resultType="integer">
        SELECT COUNT(EMPLOYEE_NO)
        FROM COLLEGE_TRAIN_ASSESSED
        where BATCH = (select BATCH from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{no})  and FACTORY_FLAG = (select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{no})
    </select>

    <select id="judgePassed" resultType="integer">
        SELECT COUNT(a.ASSESSED_ID)
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
        LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON a.ASSESSED_ID = b.ID
        WHERE b.EMPLOYEE_NO = #{no}
        and a.TYPE=#{type}
        <if test="passed != null and passed!=''">
            and PASSED=#{passed}
        </if>
and APPOINT_STATUS='1'
        <!--  SELECT COUNT(ASSESSED_ID)
          FROM COLLEGE_TRAIN_APPOINT_RECORD
          where ASSESSED_ID=(select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{no})
          and TYPE=#{type}
          <if test="passed != null and passed!=''">
              and PASSED=#{passed}
          </if>-->
    </select>

    <select id="judgePassedError" resultType="integer">
        SELECT COUNT(a.ASSESSED_ID)
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
        LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON a.ASSESSED_ID = b.ID
        WHERE b.EMPLOYEE_NO = #{no}
        and a.TYPE=#{type}


        <!--  SELECT COUNT(ASSESSED_ID)
          FROM COLLEGE_TRAIN_APPOINT_RECORD
          where ASSESSED_ID=(select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{no})
          and TYPE=#{type}
          <if test="passed != null and passed!=''">
              and PASSED=#{passed}
          </if>-->
    </select>


    <select id="isCollegeIng" resultType="integer">
        SELECT COUNT(a.ASSESSED_ID)
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
        LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON a.ASSESSED_ID = b.ID
        WHERE b.EMPLOYEE_NO = #{no}
        and a.TYPE=#{type} and a.STATUS='未考核' and APPOINT_STATUS='1'
        <!--  SELECT COUNT(ASSESSED_ID)
          FROM COLLEGE_TRAIN_APPOINT_RECORD
          where ASSESSED_ID=(select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{no})
          and TYPE=#{type}
          <if test="passed != null and passed!=''">
              and PASSED=#{passed}
          </if>-->
    </select>
    <select id="showAppointment" resultType="com.htks.domain.student.dto.Reservation">

        select a.APPOINT_START_DATE as aStartDate,
               a.APPOINT_END_DATE   as aEndDate,
               a.APPOINT_START_TIME as aStartTime,
               a.APPOINT_END_TIME   as aEndTime,
               a.STATUS as status ,
               a.SCORE,
               a.TYPE as TYPE
        from COLLEGE_TRAIN_APPOINT_RECORD a
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON a.ASSESSED_ID = b.ID
        WHERE b.EMPLOYEE_NO = #{no}
          and a.TYPE = #{type}
          and APPOINT_STATUS='1'
    </select>
  <!--  获取预约时间-->
    <select id="getEmployeeInfo" resultType="com.htks.domain.school.dto.CommonEmployeeName">
        SELECT EMPLOYEE_NAME as employeeName,ENTRY_DATE as entryDate  FROM COMMON_EMPLOYEE ce WHERE ce.EMPLOYEE_NUMBER =#{employeeNo}

    </select>
</mapper>
        <!-- /*  select APPOINT_START_DATE as aStartDate,
        APPOINT_END_DATE as aEndDate,
        APPOINT_START_TIME as aStartTime,
        APPOINT_END_TIME as aEndTime,
        STATUS,
        SCORE
        from COLLEGE_TRAIN_APPOINT_RECORD
        where ASSESSED_ID = (select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{no})
        and TYPE = #{type}*/-->