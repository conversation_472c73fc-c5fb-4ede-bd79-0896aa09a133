<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.student.repository.hana.CourseStudyRepository">
    <select id="getAccessId" resultType="long">
        SELECT ID, BATCH, CHOOSE_DEPARTMENT_NAME
        FROM COLLEGE_TRAIN_ASSESSED
        WHERE EMPLOYEE_NO = #{employeeNo}
    </select>

    <select id="getCourseId" resultType="com.htks.domain.student.dto.CourseStudy">
        SELECT COURSE_ID, ASSESSED_COURSE_ID
        FROM COLLEGE_TRAIN_ASSESSED_COURSE_ITEM
        WHERE ASSESSED_ID = #{id}
    </select>
<!--获取截止时间-->
    <select id="getDeadTime" resultType="com.htks.domain.student.dto.CourseStudy">
        SELECT DEAD_TIME, BATCH
        FROM COLLEGE_TRAIN_ASSESSED_COURSE
        WHERE ID = #{assessedCourseId}
    </select>

<!--
    获取课程详情
-->
<!--    <select id="getCourseDetail" resultType="com.htks.domain.student.dto.CourseStudy">
        SELECT  DISTINCT a.ID as courseId, a.TITLE , a.ATTACHMENT_ID ,d.ATTACHMENT_PATH ,c.DEAD_TIME ,c.CREATED_TIME,b.STUDY_STATUS as studyStatus
        FROM  COLLEGE_TRAIN_ASSESSED_COURSE_ITEM b
        LEFT JOIN COLLEGE_TRAIN_COURSE
        a ON a.ID = b.COURSE_ID  LEFT JOIN COLLEGE_TRAIN_ATTACHMENT d ON d.ID = a.ATTACHMENT_ID
        LEFT JOIN COLLEGE_TRAIN_ASSESSED_COURSE c ON b.ASSESSED_COURSE_ID =c.ID
        WHERE b.ASSESSED_ID = #{id}
          and c.BATCH =(SELECT cta.BATCH FROM COLLEGE_TRAIN_ASSESSED cta WHERE ID=#{id})
          AND a.CATEGORY = #{category}
        <if test="category.equals('BU公共课')">
            AND a.DEPARTMENT_NAME = #{departmentName}
        </if>
    </select>-->
    <select id="getCourseDetail" resultType="com.htks.domain.student.dto.CourseStudy">
        SELECT a.ID AS courseId, a.TITLE, a.ATTACHMENT_ID, d.ATTACHMENT_PATH, c.DEAD_TIME, c.CREATED_TIME, b.STUDY_STATUS AS studyStatus
        FROM COLLEGE_TRAIN_ASSESSED_COURSE_ITEM b
        LEFT JOIN COLLEGE_TRAIN_COURSE a ON a.ID = b.COURSE_ID
        LEFT JOIN COLLEGE_TRAIN_ATTACHMENT d ON d.ID = a.ATTACHMENT_ID
        LEFT JOIN COLLEGE_TRAIN_ASSESSED_COURSE c ON b.ASSESSED_COURSE_ID = c.ID
        WHERE b.ASSESSED_ID = #{id}
        AND a.CATEGORY = #{category}
        <if test="category.equals('BU公共课')">
            AND a.DEPARTMENT_NAME = #{departmentName}
        </if>
        AND (b.ASSESSED_ID, b.COURSE_ID, c.CREATED_TIME) IN (
        SELECT b.ASSESSED_ID, b.COURSE_ID, MAX(c.CREATED_TIME)
        FROM COLLEGE_TRAIN_ASSESSED_COURSE_ITEM b
        LEFT JOIN COLLEGE_TRAIN_ASSESSED_COURSE c ON b.ASSESSED_COURSE_ID = c.ID
        WHERE b.ASSESSED_ID = #{id}
        GROUP BY b.ASSESSED_ID, b.COURSE_ID
        )
    </select>

    <select id="getVideoUrl" resultType="string">
        select ATTACHMENT_PATH from COLLEGE_TRAIN_ATTACHMENT where id =(select VIDEO_ID from COLLEGE_TRAIN_COURSE where id =#{id})
    </select>

    <select id="getVideoUrlList" resultType="com.htks.domain.course.dto.Attachment">
        select a.ATTACHMENT_PATH attachmentPath,b.id   from COLLEGE_TRAIN_ATTACHMENT a
        INNER  JOIN COLLEGE_TRAIN_COURSE b ON a.ID =b.VIDEO_ID AND b.CATEGORY ='HR公开课'
        where b.DELETED_FLAG =false
    </select>

    <!--更改学习状态-->
    <update id="changeStudyStatus">
        update COLLEGE_TRAIN_ASSESSED_COURSE_ITEM
        set STUDY_STATUS=#{studyStatus},
            COMPLETE_TIME=#{completeTime}
        where ASSESSED_ID = #{assessedId}
          and COURSE_ID = #{courseId}

    </update>
    <!--新增学习记录-->
    <insert id="addStudyLog" keyProperty="id" useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE"  keyProperty="id">
            SELECT COLLEGE_TRAIN_ASSESSED_COURSE_STUDY_LOG_SEQ.NEXTVAL as id FROM DUMMY
        </selectKey>
        INSERT INTO COLLEGE_TRAIN_ASSESSED_COURSE_STUDY_LOG
        (ID,COURSE_ITEM_ID)
        VALUES (#{id}, #{courseItemId})
    </insert>

    <select id="getCourseItemId" resultType="long">
        SELECT a.ID
        FROM COLLEGE_TRAIN_ASSESSED_COURSE_ITEM a
        WHERE a.ASSESSED_ID = #{assessedId}
          AND a.COURSE_ID = #{courseId}
    </select>
<!--获取课程最高考试成绩-->
    <select id="getExamScore" resultType="integer">
        SELECT MAX(a.TOTAL_SCORE)  FROM COLLEGE_TRAIN_EXAM_RECORD a  LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY b on a.EXAM_CATEGORY_ID =b.ID
        WHERE a.ASSESSED_ID =#{id} AND b.CATEGORY_ITEM=#{category}
    </select>

    <select id="getExamScoreList" resultType="Map">
        SELECT a.ASSESSED_ID, MAX(a.TOTAL_SCORE) MAX_SCORE FROM COLLEGE_TRAIN_EXAM_RECORD a  LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY b on a.EXAM_CATEGORY_ID =b.ID
        WHERE b.CATEGORY_ITEM=#{category}
        group by a.ASSESSED_ID
    </select>

    <select id="getDepartmentName" resultType="string">
        SELECT a.CHOOSE_DEPARTMENT_NAME FROM COLLEGE_TRAIN_ASSESSED a WHERE a.EMPLOYEE_NO =#{no}
    </select>
    
    <select id="getPracticeDepartment" resultType="string">
        SELECT ctpa.PRACTICE_DEPARTMENT  FROM COLLEGE_TRAIN_ASSESSED a
	    LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON a.PRACTICE_DEPARTMENT_AREA_ID = ctpa.id
	    WHERE a.EMPLOYEE_NO =#{no}
    </select>
    
    <select id="getRoleId" resultType="string">
        select ROLE_ID from COLLEGE_TRAIN_USER where EMPLOYEE_NO=#{no}
    </select>
    <select id="getJudge" resultType="com.htks.domain.school.dto.CollegeTrainUser">
        select * from COLLEGE_TRAIN_USER where ROLE_ID like '%' || #{userRoleId} || '%'
    </select>
    <select id="getEmployeeName" resultType="string">
        select EMPLOYEE_NAME from COMMON_EMPLOYEE where EMPLOYEE_NUMBER =#{employeeNo}
    </select>
</mapper>
