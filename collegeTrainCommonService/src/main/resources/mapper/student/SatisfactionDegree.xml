<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.student.repository.hana.SatisfactionDegreeRepository">
    <insert id="inserDegree" parameterType="com.htks.domain.student.dto.SatisfactionDegree" keyProperty="ID"
            useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
            SELECT COLLEGE_TRAIN_SATISFACTION_DEGREE_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_SATISFACTION_DEGREE
        (ID,ASSESSED_ID,SCORE,THEORY_COURSE_DURATION,THEORY_MAJOR,THEORY_COURSE_INTERACTION,MASTER_MAJOR,MASTER_COURSE_ARRANGE,
        MASTER_PRACTICAL_OPERATION,MASTER_PATIENCE,LEADER_QUESTION,LEADER_MEET,LEADER_CARE,STUDY_THEORY,
        STUDY_PRACTICAL_OPERATION,STUDY_REPORT)
        values
        (#{ID},#{ASSESSED_ID},#{sd.SCORE},#{sd.THEORY_COURSE_DURATION},#{sd.THEORY_MAJOR},#{sd.THEORY_COURSE_INTERACTION},#{sd.MASTER_MAJOR},#{sd.MASTER_COURSE_ARRANGE},
        #{sd.MASTER_PRACTICAL_OPERATION},#{sd.MASTER_PATIENCE},#{sd.LEADER_QUESTION},#{sd.LEADER_MEET},#{sd.LEADER_CARE},#{sd.STUDY_THEORY},
        #{sd.STUDY_PRACTICAL_OPERATION},#{sd.STUDY_REPORT})

    </insert>
    <select id="onboardingTime" resultType="string">
        select ENTRY_DATE
        from COLLEGE_TRAIN_ASSESSED
        where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo}
    </select>

    <select id="appraiseIsNull" resultType="integer">
        SELECT count(a.ID)
        FROM COLLEGE_TRAIN_SATISFACTION_DEGREE a
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON a.ASSESSED_ID = b.ID
        WHERE b.EMPLOYEE_NO = #{employeeNo}
    </select>
    <select id="newDate" resultType="string">
        select max(a.CREATED_TIME)
        from COLLEGE_TRAIN_SATISFACTION_DEGREE a
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON a.ASSESSED_ID = b.ID
        WHERE b.EMPLOYEE_NO = #{employeeNo}
    </select>

    <select id="getAccessId" resultType="long">
        select ID
        from COLLEGE_TRAIN_ASSESSED
        where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{STUDENT_ID}
    </select>
    <select id="getNewScore" resultType="integer">
        SELECT a.SCORE, a.CREATED_TIME
        FROM COLLEGE_TRAIN_SATISFACTION_DEGREE a
        WHERE a.ASSESSED_ID = (SELECT a.ID from COLLEGE_TRAIN_ASSESSED a where a.EMPLOYEE_NO = #{employeeNo})
        ORDER BY a.CREATED_TIME DESC LIMIT 1

    </select>
<select id="judgeIsfirst" resultType="integer">
select COUNT (ID) from COLLEGE_TRAIN_SATISFACTION_DEGREE where ASSESSED_ID=#{id}
</select>

</mapper>