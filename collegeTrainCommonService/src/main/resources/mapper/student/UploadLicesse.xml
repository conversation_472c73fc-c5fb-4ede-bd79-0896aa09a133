<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.student.repository.hana.UploadWorkLicense">
    <!--    获取上岗证上传开始，截止时间-->
    <select id="postUploadStartTime" resultType="string">
        select POST_CERTIFICATE_START_TIME
        from COLLEGE_TRAIN_ASSESSED
        where EMPLOYEE_NO = #{employeeNo}
    </select>
    <select id="postUploadEndTime" resultType="string">
        select POST_CERTIFICATE_END_TIME
        from COLLEGE_TRAIN_ASSESSED
        where EMPLOYEE_NO = #{employeeNo}
    </select>

    <!--    获取已经通过的上岗证-->
    <select id="getWorkLicense" resultType="string">
        select POST_NAME
        from COLLEGE_TRAIN_POST_INFO b
        where b.POST_NAME in(select POST_NAME from COLLEGE_TRAIN_POST_INFO where ID in(select a.POST_INFO_ID
                       from COLLEGE_TRAIN_EXAM_RECORD a
                       where ASSESSED_ID =
                             (select ID from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo})
                         and TOTAL_SCORE = 100
                         and EXAM_CATEGORY_ID = '8') )  and b.area=#{area}
          AND b.POST_NAME  NOT IN (select POST_NAME
                                  from COLLEGE_TRAIN_POST_CARD
                                  where ASSESSED_ID = (select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo})) GROUP BY POST_NAME


    </select>

    <select id="getWorkLicenseArea" resultType="string">
        select AREA
        from COLLEGE_TRAIN_POST_INFO b
        where b.ID in ((select a.POST_INFO_ID
                                                                                          from COLLEGE_TRAIN_EXAM_RECORD a
                                                                                          where ASSESSED_ID =
                             (select ID from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo})
                         and TOTAL_SCORE = 100
                         and EXAM_CATEGORY_ID = '8')) GROUP BY AREA


    </select>

    <!--    上传上岗证-->
    <insert id="uploadWorkLicense" parameterType="com.htks.domain.student.dto.AppointTime" keyProperty="ID"
            useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID" >
            SELECT COLLEGE_TRAIN_POST_CARD_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_POST_CARD
        (ID,ASSESSED_ID,POST_AREA,POST_NAME,BATCH,POST_ID)
        values (#{ID},#{accessId},#{postArea},#{postName},#{batch},#{postId})
    </insert>

    <select id="getPostCardId" resultType="long">
        select a.ID from COLLEGE_TRAIN_POST_INFO a  where a.POST_NAME=#{postName} and AREA=#{area} and FACTORY_FLAG =(select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED where  EMPLOYEE_NO =#{no})
    </select>

    <select id="findDepartmentCard" resultType="com.htks.domain.student.dto.PostCard">
        SELECT
a.ID,a.POST_NAME,
               a.POST_AREA,
               STRING_AGG(b.ATTACHMENT_PATH, ', ') AS path,
               a.BATCH
        from COLLEGE_TRAIN_POST_CARD a
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO c ON c.POST_NAME =a.POST_NAME
                 LEFT JOIN COLLEGE_TRAIN_ATTACHMENT b ON b.POST_CARD_ID = c.ID
        where a.POST_AREA in(
        SELECT d.AREA FROM  COLLEGE_TRAIN_POST_AREA d WHERE d.PRACTICE_DEPARTMENT =(
            SELECT e.PRACTICE_DEPARTMENT from   COLLEGE_TRAIN_POST_AREA e  WHERE e.ID =(
                select f.PRACTICE_DEPARTMENT_AREA_ID
                from COLLEGE_TRAIN_ASSESSED f
                where EMPLOYEE_NO = #{employeeNo})))
          and a.ASSESSED_ID = (select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo})
          AND b.ACCESS_ID =(select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo})
        GROUP BY a.POST_NAME, a.POST_AREA,a.BATCH,a.ID
    </select>
    <!--    获取区域已经上传的上岗证-->
    <select id="findAreaCard" resultType="com.htks.domain.student.dto.PostCard">
        SELECT a.ID, a.POST_NAME, a.POST_AREA,
               STRING_AGG(b.ATTACHMENT_PATH, ', ') AS path,
               a.BATCH, a.POST_ID
        FROM COLLEGE_TRAIN_POST_CARD a
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO c ON c.POST_NAME = a.POST_NAME
                 LEFT JOIN COLLEGE_TRAIN_ATTACHMENT b ON b.POST_CARD_ID = c.ID
        WHERE a.POST_AREA = #{area}
          AND a.ASSESSED_ID = (SELECT id FROM COLLEGE_TRAIN_ASSESSED WHERE EMPLOYEE_NO = #{employeeNo})
          AND b.ACCESS_ID = (SELECT id FROM COLLEGE_TRAIN_ASSESSED WHERE EMPLOYEE_NO = #{employeeNo})
        GROUP BY a.ID, a.POST_NAME, a.POST_AREA, a.BATCH, a.POST_ID;
    </select>


    <!--    获取已经通过的上岗证分步-->
    <select id="getWorkLicense21" resultType="long">
        select ID
        from COLLEGE_TRAIN_ASSESSED
        where EMPLOYEE_NO = #{employeeNo}
    </select>
    <select id="getWorkLicense22" resultType="string">
        select a.POST_INFO_ID
        from COLLEGE_TRAIN_EXAM_RECORD a
        where ASSESSED_ID = #{ASSESSED_ID}
          and TOTAL_SCORE > '80'
          and EXAM_CATEGORY_ID = '8'
    </select>
    <select id="getWorkLicense23" resultType="string">
        select POST_NAME
        from COLLEGE_TRAIN_POST_INFO b
        where AREA = #{area}
        <if test="list!= null and list.size() >0">
            and b.ID in
            <foreach collection="list" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>

    </select>

    <select id="getpostCardId" resultType="string">
        select ID
        from COLLEGE_TRAIN_POST_INFO b where b.AREA=#{area} and POST_NAME=#{name} and FACTORY_FLAG =(select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED where  EMPLOYEE_NO =#{no})
    </select>

<!--    <insert id="addPostCardPicture" keyProperty="ID"
            useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
            SELECT COLLEGE_TRAIN_ATTACHMENT_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        INSERT INTO COLLEGE_TRAIN_ATTACHMENT
        (ID, ATTACHMENT_PATH, ATTACHMENT_TYPE, ATTACHMENT_NAME, ATTACHMENT_MEMO, POST_CARD_ID, QUESTION_ID)
        VALUES(#{ID}, #{ATTACHMENT_PATH}, #{ATTACHMENT_TYPE}, #{ATTACHMENT_NAME}, #{ATTACHMENT_MEMO}, #{POST_CARD_ID},
        #{QUESTION_ID});
    </insert>-->

    <insert id="addPostCardPicture">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_ATTACHMENT_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_ATTACHMENT
        ("ID" ,
        "ATTACHMENT_PATH" ,
        "ATTACHMENT_TYPE" ,
        "ATTACHMENT_NAME" ,
        "ATTACHMENT_MEMO",
         "POST_CARD_ID","ACCESS_ID")
        values(
        #{id},
        #{AE.attachmentPath},
        #{AE.attachmentType},
        #{AE.attachmentName},
        #{AE.attachmentMemo},
        #{postCardId},#{accessId}
        )
    </insert>
<select id="getSopSum" resultType="integer">

    SELECT count(1)
    FROM COLLEGE_TRAIN_SOP_UPLOAD a WHERE a.ASSESSED_ID =#{id} AND a.TYPE =#{type}

</select>
    <select id="getSopScore" resultType="String">

        SELECT EVALUATE_STATUS
        FROM COLLEGE_TRAIN_SOP_UPLOAD a WHERE a.ASSESSED_ID =#{id} AND a.TYPE =#{type}

    </select>

    <insert id="insertSop">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID" keyColumn="ID">
            SELECT COLLEGE_TRAIN_SOP_UPLOAD_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        INSERT INTO COLLEGE_TRAIN_SOP_UPLOAD
        (ID, ASSESSED_ID, TYPE, SOP_NAME, ATTACHMENT_ID, EVALUATE_STATUS, DELETED_FLAG)
        VALUES(#{ID}, #{SOP.assessedId}, #{SOP.type}, #{SOP.sopName},#{SOP.attachmentId}, '未评价', false);

    </insert>
<delete id="deleteSop">
    DELETE FROM COLLEGE_TRAIN_ATTACHMENT
    WHERE ID=#{id};
</delete>

    <insert id="insertWeeklyReport" parameterType="list">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID" keyColumn="ID">
            SELECT COLLEGE_TRAIN_WEEKLY_REPORT_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        INSERT INTO COLLEGE_TRAIN_WEEKLY_REPORT ( ID, ASSESSED_ID, JUDGE_NAME, JUDGE_NO, SCORE, WEEKLY ) values
        <foreach collection="list" item="weekly" separator=",">
            (#{ID}, #{weekly.assessedId}, #{weekly.judgeName}, #{weekly.judgeNo} ,#{weekly.score}, #{weekly.weekly})
        </foreach>

    </insert>

    <update id="updateWeekly">
            <foreach collection="list" item="weekly" separator=";">
                UPDATE COLLEGE_TRAIN_WEEKLY_REPORT
                SET  JUDGE_NO=#{weekly.judgeNo}, JUDGE_NAME=#{weekly.judgeName}, SCORE=#{weekly.score}
                WHERE ASSESSED_ID=#{weekly.assessedId} and WEEKLY =#{weekly.weekly}
            </foreach>
    </update>
    <select id="getWeeklySum" resultType="integer">
        select count(1) from COLLEGE_TRAIN_WEEKLY_REPORT  WHERE ASSESSED_ID=#{assessedId} and WEEKLY=#{weekly};
    </select>
    
    <insert id="insertSUBJECTIVE">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID" keyColumn="ID">
            SELECT COLLEGE_TRAIN_SUBJECTIVE_JUDGE_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        INSERT INTO COLLEGE_TRAIN_SUBJECTIVE_JUDGE
            (ID, ASSESSED_ID, SCORE)
        VALUES
        (#{ID}, #{collegeTrainSubjectiveJudge.assessedId}, #{collegeTrainSubjectiveJudge.score})
    </insert>
<select id="getSopDetail" resultType="com.htks.domain.common.dto.SopDetailEntity">
    SELECT   SOP_FORMAT, LOGIC, CONTENT_DETAILED, CONTENT_COMPLETE, EASY_UNDERSTAND
    FROM COLLEGE_TRAIN_SOP_UPLOAD_DETAIL a
    WHERE a.SOP_UPLOAD_ID =(SELECT b.ID FROM COLLEGE_TRAIN_SOP_UPLOAD b WHERE b.ASSESSED_ID=#{id} AND TYPE='1')
</select>

    <update id="updateSubjective">
        <foreach collection="list" item="subjective" separator=";">
            UPDATE COLLEGE_TRAIN_SUBJECTIVE_JUDGE
            SET   SCORE=#{subjective.score}
            WHERE ASSESSED_ID=#{subjective.assessedId}
        </foreach>
    </update>
    <select id="getSubjectiveSum" resultType="integer">
        select count(1) from COLLEGE_TRAIN_SUBJECTIVE_JUDGE  WHERE ASSESSED_ID=#{assessedId};
    </select>

    <update id="updateReadAfter">
        <foreach collection="list" item="readAfter" separator=";">
        UPDATE COLLEGE_TRAIN_SOP_UPLOAD
        SET ASSESSED_ID=#{readAfter.assessedId},SCORE=#{readAfter.score},EVALUATE_STATUS='已评价',GRADER_NO=#{readAfter.graderNo},GRADER_NAME=#{readAfter.graderName}
        WHERE ASSESSED_ID=#{readAfter.assessedId} and TYPE='2'
        </foreach>
    </update>

    <update id="updateReadAfterByHand">
            UPDATE COLLEGE_TRAIN_SOP_UPLOAD
            SET ASSESSED_ID=#{collegeTrainSopUpload.assessedId},SCORE=#{collegeTrainSopUpload.score},EVALUATE_STATUS='已评价',
                GRADER_NO=#{collegeTrainSopUpload.graderNo},GRADER_NAME=#{collegeTrainSopUpload.graderName}
            WHERE ASSESSED_ID=#{collegeTrainSopUpload.assessedId} and TYPE='2'
    </update>



<!--获取全部上岗证-->
    <select id="findAllPostCard" resultType="com.htks.domain.common.dto.PostCardEntity">
        select a.ASSESSED_ID, a.POST_NAME as postName,
               a.POST_AREA as postArea,
               b.ATTACHMENT_PATH as path,
               cta.CHOOSE_DEPARTMENT_NAME  as department,
               a.UPLOAD_TIME as time
        from COLLEGE_TRAIN_POST_CARD a
            LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON CTA .ID =a.ASSESSED_ID
            LEFT JOIN COLLEGE_TRAIN_POST_INFO d ON d.POST_NAME =a.POST_NAME
            LEFT JOIN
            COLLEGE_TRAIN_ATTACHMENT b ON b.POST_CARD_ID =d.ID
        where  ASSESSED_ID = (select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo})
        AND b.ACCESS_ID =(select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo})

    </select>

    <select id="findAllPostCard1" resultType="com.htks.domain.common.dto.PostCardEntity">
        select a.ASSESSED_ID, a.POST_NAME as postName,
               a.POST_AREA as postArea,
               a.UPLOAD_TIME as time
        from COLLEGE_TRAIN_POST_CARD a
        where  ASSESSED_ID = (select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo})
    </select>
    <select id="findAllPostCard2" resultType="string">
        select cta.CHOOSE_DEPARTMENT_NAME  as department
        from COLLEGE_TRAIN_ASSESSED cta
        where  CTA .ID = (select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo})
    </select>
    <select id="findAllPostCard3" resultType="string">
        select d.ID  as postId
        from COLLEGE_TRAIN_POST_INFO d
        where  d.POST_NAME=#{postName} and AREA=
          #{employeeNo}
    </select>
    <select id="findAllPostCard4" resultType="string">
        select d.ATTACHMENT_PATH  as path
        from COLLEGE_TRAIN_ATTACHMENT d
        where  d.ACCESS_ID =(select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo})
        and d.POST_CARD_ID=#{postId} limit 1
    </select>
    <update id="updateSop">
        UPDATE COLLEGE_TRAIN_SOP_UPLOAD
        SET GRADER_NO=#{no},
            GRADER_NAME=#{name}, GRADING_NUMBER=#{number}
        WHERE ASSESSED_ID=#{id} and type='1';

    </update>
<select id="getFactory" resultType="string">
    select FACTORY_FLAG from COLLEGE_TRAIN_DEPARTMENT  where id=#{id}
</select>

    <select id="getDepartmentName" resultType="string">
        select DEPARTMENT from COLLEGE_TRAIN_DEPARTMENT  where id=#{id}
    </select>

    <select id="getDepartmentId" resultType="String">
        select id from COLLEGE_TRAIN_DEPARTMENT  where FACTORY_FLAG=#{factoryFlag} and DEPARTMENT=#{department}
    </select>
    <select id="getArea" resultType="string">
        select a.AREA
        FROM COLLEGE_TRAIN_POST_AREA a
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.PRACTICE_DEPARTMENT_AREA_ID = a.ID
        WHERE cta.EMPLOYEE_NO = #{employeeNo}
    </select>

    <select id="getSopEndTime" resultType="string">
        select SOP_UPLOAD_DEAD_TIME from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeeNo}
    </select>

    <select id="getReadAfterEndTime" resultType="string">
        select AFTER_READING_UPLOAD_DEAD_TIME from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeeNo}
    </select>

    <select id="getPostTime" resultType="com.htks.domain.student.dto.PostCardTime">
        select  POST_CERTIFICATE_START_TIME as startTime,POST_CERTIFICATE_END_TIME as endTime  from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeeNo}
    </select>
    <select id="getSumOne" resultType="integer">
        select count (id) from COLLEGE_TRAIN_SOP_UPLOAD where ASSESSED_ID =#{id}
    </select>

    <select id="getSumTwo" resultType="integer">
        select count (id) from COLLEGE_TRAIN_SUBJECTIVE_JUDGE where ASSESSED_ID =#{id}
    </select>
    <select id="getSumThree" resultType="integer">
        select count (id) from COLLEGE_TRAIN_WEEKLY_REPORT where ASSESSED_ID =#{id}
    </select>
    <select id="getSumFour" resultType="integer">
        select count (id) from COLLEGE_TRAIN_MEET where ASSESSED_ID =#{id}
    </select>

    <select id="judgePost" resultType="integer">
select count (id) from COLLEGE_TRAIN_ATTACHMENT where ASSESSED_ID =(select  id from COLLEGE_TRAIN_ASSESSED where ) and POST_CARD_ID=
       ( select ID from COLLEGE_TRAIN_POST_INFO where POST_NAME=#{postName})
    </select>
    <select id="getAreaList" resultType="string">
select  AREA from  COLLEGE_TRAIN_POST_AREA where PRACTICE_DEPARTMENT =(
      select CHOOSE_DEPARTMENT_NAME from COLLEGE_TRAIN_ASSESSED where   EMPLOYEE_NO=#{employeeNo})
    </select>
</mapper>