<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.student.repository.hana.ExamRepository">
    <!--根据条件查询符合条件的未考试-->
    <select id="getAllExam" resultType="com.htks.domain.student.dto.ExamRecord">
        SELECT d.ID as examCategoryId,
               a.EMPLOYEE_NAME,
               c.CATEGORY_ITEM,
               d.PAPER_TYPE,
               d.TEST_TIME,
               c.EXAM_END_TIME,
               d.SINGLE_OPTION_ANSWER,
               d.MORE_OPTION_ANSWER,
               d.JUDGE_ANSWER,
               d.FILL_ANSWER,
               d.SHORT_ANSWER,
               c.EXAM_START_TIME,
               c.EXAM_END_TIME
        FROM COLLEGE_TRAIN_ASSESSED a
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED_EXAM_ITEM b ON b.ASSESSED_ID = a.ID
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED_EXAM c ON c.ID = b.ASSESSED_EXAM_ID
                 LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY d ON b.EXAM_CATEGORY_ID = d.ID
                 LEFT JOIN COLLEGE_TRAIN_EXAM_RECORD e ON e.ID = b.EXAM_CATEGORY_ID
        WHERE a.ID = (SELECT ID FROM COLLEGE_TRAIN_ASSESSED WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{studentNo})
          AND d.ID NOT IN (SELECT cter.EXAM_CATEGORY_ID
                           FROM COLLEGE_TRAIN_EXAM_RECORD cter
                           WHERE cter.ASSESSED_ID = (SELECT ID
                                                     FROM COLLEGE_TRAIN_ASSESSED
                                                     WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{studentNo}))
          and c.EXAM_END_TIME > #{date} and b.EXAM_CATEGORY_ID !='8'
and c.FACTORY_FLAG =(select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{studentNo})

    </select>
<!--        SELECT b.ID as examCategoryId,
               a.EXAM_START_TIME,
               b.TEST_TIME,
               a.EXAM_END_TIME,
               b.PAPER_TYPE,
               b.CATEGORY_ITEM,
               d.TOTAL_SCORE,
               d.EXAM_NUMBER,
               d.EXAM_NAME,
               b.SINGLE_OPTION_ANSWER,
               b.MORE_OPTION_ANSWER,
               b.JUDGE_ANSWER,
               b.FILL_ANSWER,
               b.SHORT_ANSWER
        FROM  COLLEGE_TRAIN_EXAM_CATEGORY b
                 LEFT JOIN  COLLEGE_TRAIN_ASSESSED_EXAM a ON b.CATEGORY_ITEM = a.CATEGORY_ITEM
                 LEFT JOIN COLLEGE_TRAIN_EXAM_RECORD d  ON d.EXAM_CATEGORY_ID=b.ID
        WHERE b.id='8' or( a.ID IN (SELECT ASSESSED_EXAM_ID
                       FROM COLLEGE_TRAIN_ASSESSED_EXAM_ITEM
                       WHERE ASSESSED_ID
                                 = (SELECT ID
                                    FROM COLLEGE_TRAIN_ASSESSED
                                    WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{studentNo}))
          AND a.BATCH = (SELECT COLLEGE_TRAIN_ASSESSED.BATCH
                         FROM COLLEGE_TRAIN_ASSESSED
                         WHERE COLLEGE_TRAIN_ASSESSED.ID = (SELECT ID
                                                            FROM COLLEGE_TRAIN_ASSESSED
                                                            WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = '31975'))
          and a.EXAM_START_TIME > #{date})
    </select>-->



    <!-- 查询除了上岗证已经考试-->
    <select id="getPassAllExam" resultType="com.htks.domain.student.dto.ExamRecord">
        SELECT *
        FROM COLLEGE_TRAIN_EXAM_RECORD
        WHERE ASSESSED_ID = (SELECT ID
                             FROM COLLEGE_TRAIN_ASSESSED
                             WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
          AND EXAM_CATEGORY_ID not IN
              ('8');
    </select>

    <select id="getPaperTypeByid" resultType="string">
select PAPER_TYPE from COLLEGE_TRAIN_EXAM_CATEGORY where ID=#{id}
    </select>
    <!--获取试卷名称-->
    <select id="getPostPaperName" resultType="string">
        select PAPER_NAME
        from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER
        where EXAM_CATEGORY_ID = #{id}
    </select>

    <!-- 查询所选区域内未考试的上岗证考试-->
    <select id="getExamByArea" resultType="com.htks.domain.student.dto.ExamRecord">
        SELECT '8' as examCategoryId,
               b.AREA,
               c.PAPER_NAME,
               d.POST_CERTIFICATE_START_TIME as examStartTime,
               d.POST_CERTIFICATE_END_TIME   as examEndTime,
               b.id                          as postInfoId,
               '岗位级' as paperType,
             b.IS_CORE  as isCore
        FROM  COLLEGE_TRAIN_POST_INFO_CARD a
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO b ON b.ID = a.POST_INFO_ID
                 LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY_PAPER c
                           on TO_VARCHAR(b.ID) = c.POST_INFO_ID
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED d ON d.ID = a.ASSESSED_ID
        WHERE b.AREA = #{area} and b.FACTORY_FLAG=(SELECT FACTORY_FLAG
                                                   FROM COLLEGE_TRAIN_ASSESSED
                                                   WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
          and b.POST_NAME not in
              (select POST_NAME from COLLEGE_TRAIN_POST_INFO where id in (select POST_INFO_ID from COLLEGE_TRAIN_EXAM_RECORD
              where ASSESSED_ID =(SELECT ID       FROM COLLEGE_TRAIN_ASSESSED    WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo}) ))
          AND b.ID NOT IN (SELECT POST_INFO_ID
                           FROM COLLEGE_TRAIN_EXAM_RECORD
                           WHERE COLLEGE_TRAIN_EXAM_RECORD.EXAM_CATEGORY_ID = '8'
                             AND ASSESSED_ID
                               = (SELECT ID
                                  FROM COLLEGE_TRAIN_ASSESSED
                                  WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo}))
          AND d.ID
            = (SELECT ID
               FROM COLLEGE_TRAIN_ASSESSED
               WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
          AND a.CREATED_TIME IN (SELECT MAX(a.CREATED_TIME)
                                 FROM COLLEGE_TRAIN_POST_INFO_CARD a
                                 WHERE a.ASSESSED_ID = (SELECT ID
                                                        FROM COLLEGE_TRAIN_ASSESSED
                                                        WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
                                 group BY a.POST_INFO_ID)
    </select>
   <!--     SELECT a.PAPER_NAME                  as paperName,
               d.ID                          as postInfoId,
               b.POST_CERTIFICATE_START_TIME as examStartTime,
               b.POST_CERTIFICATE_END_TIME   as examEndTime
        FROM COLLEGE_TRAIN_EXAM_CATEGORY_PAPER a
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO d ON d.ID = a.POST_INFO_ID
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO_CARD c ON c.POST_INFO_ID = d.ID
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON b.ID = c.ASSESSED_ID
        WHERE b.EMPLOYEE_NO = #{employeeNo}
          AND d.AREA = #{area}
          AND d.ID NOT IN (SELECT POST_INFO_ID
                           FROM COLLEGE_TRAIN_POST_INFO_CARD
                           WHERE ASSESSED_ID
                                     = (SELECT ID
                                        FROM COLLEGE_TRAIN_ASSESSED
                                        WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo}))
    </select>-->

    <!--
        查询已经考试的上岗证考试
    -->
    <select id="getPassExamByArea" resultType="com.htks.domain.student.dto.ExamRecord">
        SELECT COLLEGE_TRAIN_EXAM_RECORD.*,COLLEGE_TRAIN_POST_INFO.IS_CORE as isCore
        FROM COLLEGE_TRAIN_EXAM_RECORD left join COLLEGE_TRAIN_POST_INFO on COLLEGE_TRAIN_POST_INFO.ID=COLLEGE_TRAIN_EXAM_RECORD.POST_INFO_ID
        where COLLEGE_TRAIN_EXAM_RECORD.EXAM_CATEGORY_ID='8'
        <if test="area!= null and area !=''">
              AND COLLEGE_TRAIN_EXAM_RECORD.POST_INFO_ID
              IN (SELECT ID FROM COLLEGE_TRAIN_POST_INFO WHERE COLLEGE_TRAIN_POST_INFO.AREA=#{area})
            and COLLEGE_TRAIN_POST_INFO.POST_NAME not in (select POST_NAME from COLLEGE_TRAIN_POST_INFO where id in
                            (select POST_INFO_ID from COLLEGE_TRAIN_EXAM_RECORD where ASSESSED_ID =(SELECT ID
            FROM COLLEGE_TRAIN_ASSESSED
            WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo} and TO_NUMBER(TOTAL_SCORE) >='100' AND EXAM_CATEGORY_ID='8')))
        </if>

and COLLEGE_TRAIN_EXAM_RECORD.ASSESSED_ID=(SELECT ID
                                           FROM COLLEGE_TRAIN_ASSESSED
                                           WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
order by COLLEGE_TRAIN_EXAM_RECORD.Id desc
    </select>

    <!--查看已经考试的试卷信息-->
    <select id="passedExam" resultType="com.htks.domain.student.dto.ExamRecord">
        select a.EXAM_NAME, a.TOTAL_SCORE, b.TEST_TIME, b.PAPER_TYPE
        from COLLEGE_TRAIN_EXAM_RECORD a
                 left join COLLEGE_TRAIN_EXAM_CATEGORY b on a.EXAM_CATEGORY_ID = b.ID
        where EXAM_NUMBER = #{examNumber}
    </select>

    <!--新增考试记录-->
    <insert id="addExamRecord" keyProperty="ID"
            useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
            SELECT COLLEGE_TRAIN_EXAM_RECORD_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_EXAM_RECORD
        (ID,ASSESSED_ID,EXAM_NUMBER,EXAM_NAME,EXAM_START_TIME,EXAM_END_TIME,OBJECTIVE_SCORE,
        SUBJECTIVE_SCORE,TOTAL_SCORE,EXAM_CATEGORY_ID,PAPER_NAME,)
        values
        (#{ID},(select  a.ID  from COLLEGE_TRAIN_ASSESSED a WHERE a.EMPLOYEE_NO =#{employeeNo}),#{ER.examNumber},#{ER.examName},#{ER.examStartTime},#{ER.examEndTime},#{ER.objectiveScore},
        #{ER.subjectiveScore},#{ER.totalScore},#{ER.examCategoryId},'正式考试')
    </insert>
<update id="changeExamRecord">
    UPDATE COLLEGE_TRAIN_EXAM_RECORD
    SET ASSESSED_ID = (SELECT a.ID FROM COLLEGE_TRAIN_ASSESSED a WHERE a.EMPLOYEE_NO = #{employeeNo}),
        EXAM_NUMBER = #{ER.examNumber},
        EXAM_NAME = #{ER.examName},
        OBJECTIVE_SCORE = #{ER.objectiveScore},
        SUBJECTIVE_SCORE = #{ER.subjectiveScore},
        TOTAL_SCORE = #{ER.totalScore},
        EXAM_CATEGORY_ID =  #{ER.examCategoryId},
        PAPER_NAME = '正式考试',
        EXAM_TYPE=#{ER.examType},
        EXAM_END_TIME=#{ER.examEndTime},
        UPDATED_TIME=CURRENT_TIMESTAMP
    WHERE ID = #{id}

</update>
    <!--    新增··考试答案以及附件记录-->
    <insert id="addExamAnswer" keyProperty="ID"
            useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
            SELECT COLLEGE_TRAIN_EXAM_RECORD_ANSWER_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_EXAM_RECORD_ANSWER
        (ID, EXAM_ID, QUESTION_ID, ANSWER_TEXT, SCORE, ATTACHMENT_ID)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{ID},
            #{item.examId},
            #{item.questionId},
            #{item.answerText},
            #{item.totalScore},
            #{item.attachmentId}
            )
        </foreach>

    </insert>
    <!--获取某个问题的答案附件-->
    <select id="getAttachmentIdByQuestionId" resultType="Long" >
        SELECT ATTACHMENT_ID
        FROM COLLEGE_TRAIN_EXAM_RECORD_ANSWER
        where QUESTION_ID = #{questionId}
    </select>
    <!--useGeneratedKeys="true"  keyProperty = "ID"-->
    <!--新增附件记录 <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID" keyColumn="ID"> -->


<!--    <insert id="inserDegree" parameterType="com.htks.domain.student.dto.SatisfactionDegree" keyProperty="ID"
            useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
        SELECT COLLEGE_TRAIN_SATISFACTION_DEGREE_SEQ.NEXTVAL FROM DUMMY
    </selectKey>-->
    <!--新增附件记录-->
    <insert id="addAttachmentId" keyProperty="id" parameterType="com.htks.domain.school.dto.CollegeTrainAttachment" >
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_ATTACHMENT_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        INSERT INTO COLLEGE_TRAIN_ATTACHMENT
        (ID, ATTACHMENT_PATH, ATTACHMENT_TYPE, ATTACHMENT_NAME, ATTACHMENT_MEMO, POST_CARD_ID, QUESTION_ID,EMPLOEE_NO)
        VALUES(#{id}, #{CTA.attachmentPath}, #{CTA.attachmentType}, #{CTA.attachmentName}, #{CTA.attachmentMemo}, #{CTA.postCardId},
        #{CTA.questionId},#{employeeNo});
    </insert>
    <update id="updateAttachmentById" parameterType="com.htks.domain.school.dto.CollegeTrainAttachment">
        UPDATE COLLEGE_TRAIN_ATTACHMENT
        SET ATTACHMENT_PATH = #{CTA.attachmentPath},
            ATTACHMENT_TYPE = #{CTA.attachmentType},
            ATTACHMENT_NAME = #{CTA.attachmentName},
            ATTACHMENT_MEMO = #{CTA.attachmentMemo},
            POST_CARD_ID = #{CTA.postCardId},
            QUESTION_ID = #{CTA.questionId}
        WHERE ID = #{id}
    </update>
    <select id="getAttachmentIdSum" resultType="integer" >
       select  count (ID) from COLLEGE_TRAIN_ATTACHMENT where POST_CARD_ID=#{CTA.postCardId}and QUESTION_ID=#{CTA.questionId}
                                                        and EMPLOEE_NO=#{employeeNo} AND CREATED_TIME LIKE  CONCAT(#{time},'%')
    </select>
    <select id="getAttachmentId" resultType="java.lang.Long" >
        select  ID from COLLEGE_TRAIN_ATTACHMENT where POST_CARD_ID=#{CTA.postCardId}and QUESTION_ID=#{CTA.questionId}
                                                           and EMPLOEE_NO=#{employeeNo} AND CREATED_TIME LIKE  CONCAT(#{time},'%')
    </select>
    <!--获取附件id-->
    <select id="getAttachmentIdByPath"  resultType="java.lang.Long" >
        SELECT a.ID from COLLEGE_TRAIN_ATTACHMENT a WHERE a.ATTACHMENT_PATH =#{path} ORDER BY a.CREATED_TIME DESC LIMIT 1
    </select>
    <!--获取考试id-->
<select id="getExamId" resultType="java.lang.Long" >
    SELECT a.ID from COLLEGE_TRAIN_EXAM_RECORD a WHERE a.ASSESSED_ID =#{id} ORDER BY a.CREATED_TIME DESC LIMIT 1
</select>
    <select id="getPostExamId" resultType="long">
        SELECT a.ID from COLLEGE_TRAIN_EXAM_RECORD a WHERE a.ASSESSED_ID =#{id}  AND a.POST_INFO_ID =#{postId}ORDER BY a.CREATED_TIME DESC LIMIT 1
    </select>

    <select id="getExamIdByName" resultType="java.lang.Long" >
        SELECT a.ID from COLLEGE_TRAIN_EXAM_RECORD a WHERE a.ASSESSED_ID =#{id} AND a.EXAM_NAME =#{examName} ORDER BY a.CREATED_TIME DESC LIMIT 1
    </select>
<!--判断是否已经生成考试记录-->
    <select id="getexamSum" resultType="integer">
        SELECT COUNT(1) from COLLEGE_TRAIN_EXAM_RECORD a WHERE a.ASSESSED_ID =#{id} AND a.EXAM_NAME =#{examName}
    </select>

    <!--获取当前学生所在部门下的所有区域-->
    <select id="getAreaByDepartment" resultType="string">
        select AREA
        FROM COLLEGE_TRAIN_POST_AREA
        where ID =
              (SELECT PRACTICE_DEPARTMENT_AREA_ID FROM COLLEGE_TRAIN_ASSESSED a WHERE a.EMPLOYEE_NO = #{employeeNo})
 </select>
    <!--   /*  and ID =
               (SELECT PRACTICE_DEPARTMENT_AREA_ID FROM COLLEGE_TRAIN_ASSESSED a WHERE a.EMPLOYEE_NO = #{employeeNo})*/-->
 <!--获取其他考试题目-->
    <select id="getExamInformation1" resultType="com.htks.domain.student.dto.PaperQuestionType">
        SELECT id as  examCategoryId,
               SINGLE_OPTION_NUMBER,
               MORE_OPTION_NUMBER,
               JUDGE_NUMBER as judgeNumbers,
               FILL_NUMBER,
               SHORT_NUMBER from COLLEGE_TRAIN_EXAM_CATEGORY a WHERE a.CATEGORY_ITEM =#{categoryItem}
    </select>
    <select id="examInformation2" resultType="Long">
        SELECT id from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER WHERE EXAM_CATEGORY_ID =#{id}
    </select>
    <select id="examInformationByExamName" resultType="Long">
        SELECT id from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER WHERE PAPER_NAME =#{name}  limit 1
    </select>
    <select id="examInformationByExamNameBydept" resultType="Long">
        SELECT id from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER WHERE PAPER_NAME =#{name}
        and DEPARTMENT=(select PRACTICE_DEPARTMENT from COLLEGE_TRAIN_POST_AREA where id=(select PRACTICE_DEPARTMENT_AREA_ID from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeeNo} ) ) limit 1

    </select>
    <select id="examInformation3" resultType="com.htks.domain.student.dto.ExamQuestion">
        SELECT a.ID as questionId,QUESTION_CONTENT,a.QUESTION_TYPE questionType,a.STANDARD_ANSWER,b.ATTACHMENT_PATH
        FROM COLLEGE_TRAIN_QUESTION a LEFT JOIN COLLEGE_TRAIN_ATTACHMENT b on a.ID =b.QUESTION_ID
        WHERE QUESTION_TYPE = #{questionType}
        <if test="list!= null and list.size() >0">
            AND PAPER_ID in
            <foreach collection="list" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        order by rand() limit #{number}
    </select>
    <select id="examInformationByPaperId" resultType="com.htks.domain.student.dto.ExamQuestion">
        SELECT a.ID as questionId,QUESTION_CONTENT,a.QUESTION_TYPE questionType,a.STANDARD_ANSWER,b.ATTACHMENT_PATH,paper.PAPER_NAME
        FROM COLLEGE_TRAIN_QUESTION a LEFT JOIN COLLEGE_TRAIN_ATTACHMENT b on a.ID =b.QUESTION_ID
        left join COLLEGE_TRAIN_EXAM_CATEGORY_PAPER paper on paper.id=a.PAPER_ID
        WHERE QUESTION_TYPE = #{questionType}
            AND PAPER_ID =
                #{paperId}
        order by rand() limit #{number}
    </select>
    <select id="getFullScore" resultType="string">

       select <if test="">
        a.SINGLE_OPTION_ANSWER
    </if>
        <if test="">
            a.MORE_OPTION_ANSWER
        </if>
        <if test="">
            a.JUDGE_ANSWER
        </if>
        <if test="">
            a.FILL_ANSWER
        </if>
        <if test="">
            a.SHORT_ANSWER
        </if>
       from COLLEGE_TRAIN_EXAM_CATEGORY a
        <if test="type==0">
       where a.CATEGORY_ITEM='HR公开课理论考试①'
</if>
        <if test="type==0">
            where a.CATEGORY_ITEM='工艺公开课理论考试①'
        </if>
        <if test="type==0">
            where a.CATEGORY_ITEM='BU公共课理论课题库'
        </if>
        <if test="type==0">
            where a.CATEGORY_ITEM='工程结业理论考试'
        </if>
    </select>
<!--    <select id="getQuestionContent" resultType="com.htks.domain.question.dto.QuestionItemEntity">
        select ITEM_CONTENT, ITEM_OPTION
        from COLLEGE_TRAIN_QUESTION_ITEM
        where QUESTION_ID = #{questionId}
    </select>-->

    <!--获取上岗证考试题目-->
    <select id="getPostExam1" resultType="Long">
        SELECT ID
        FROM COLLEGE_TRAIN_EXAM_CATEGORY_PAPER
        WHERE POST_INFO_ID = #{postInfoId}
    </select>
    <select id="getPostExam2" resultType="com.htks.domain.student.dto.ExamQuestion">
        SELECT a.ID as questionId,
               PAPER_ID,
               QUESTION_TYPE,
               QUESTION_CONTENT,
               STANDARD_ANSWER,
               b.ATTACHMENT_PATH
        FROM COLLEGE_TRAIN_QUESTION a LEFT JOIN COLLEGE_TRAIN_ATTACHMENT b on a.ID =b.QUESTION_ID
            where PAPER_ID = #{id}
    </select>
    <select id="getPostExamByList" resultType="com.htks.domain.student.dto.ExamQuestion">
        SELECT a.ID as questionId,
        PAPER_ID,
        QUESTION_TYPE,
        QUESTION_CONTENT,
        STANDARD_ANSWER,
        b.ATTACHMENT_PATH
        FROM COLLEGE_TRAIN_QUESTION a LEFT JOIN COLLEGE_TRAIN_ATTACHMENT b on a.ID =b.QUESTION_ID
        where PAPER_ID in
        <foreach collection="list" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </select>
 <!--获取选项以及对应的答案-->
    <select id="getPostQuestionContent" resultType="com.htks.domain.question.dto.QuestionItemEntity">
        select ITEM_CONTENT, ITEM_OPTION
        from COLLEGE_TRAIN_QUESTION_ITEM
        where QUESTION_ID = #{questionId}
        order by case when ITEM_OPTION='A' THEN 1
                  when ITEM_OPTION='B' then 2
                  when ITEM_OPTION='C' then 3
                  when ITEM_OPTION='D' then 4
                  when ITEM_OPTION='E' then 5
                  when ITEM_OPTION='F' then 6
                ELSE 7 END ASC
    </select>
    <select id="getAttendPath" resultType="string">
        select ATTACHMENT_PATH from COLLEGE_TRAIN_ATTACHMENT where QUESTION_ID=#{id}
    </select>
    <!--获取某个题目的答案-->
    <select id="getAnswerById" resultType="string">
        select STANDARD_ANSWER
        from COLLEGE_TRAIN_QUESTION
        where id = #{questionId}
    </select>

    <!-- 获取试卷题目分布-->
    <select id="getQuestionNumber" resultType="com.htks.domain.student.dto.PaperQuestionType">
        SELECT
               a.EXAM_START_TIME,
               a.EXAM_END_TIME,
               b.TEST_TIME,
               b.SINGLE_OPTION_ANSWER,
               b.MORE_OPTION_ANSWER,
               b.JUDGE_ANSWER,
               b.FILL_ANSWER,
               b.SHORT_ANSWER,
               b.SINGLE_OPTION_NUMBER,
               b.MORE_OPTION_NUMBER,
               b.JUDGE_NUMBER,
               b.FILL_NUMBER,
               b.SHORT_NUMBER
        FROM COLLEGE_TRAIN_ASSESSED_EXAM a
                 LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY b ON b.CATEGORY_ITEM = a.CATEGORY_ITEM
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED C ON C.BATCH = a.BATCH
        WHERE C.EMPLOYEE_NO = #{employeeNo}
          AND b.CATEGORY_ITEM = #{categoryItem} order by a.CREATED_TIME desc limit 1

    </select>
    <select id="getPostExam" resultType="com.htks.domain.student.dto.PaperQuestionType">
        SELECT
            a.CREATED_TIME ,
            b.TEST_TIME,
            b.SINGLE_OPTION_ANSWER,
            b.MORE_OPTION_ANSWER,
            b.JUDGE_ANSWER,
            b.FILL_ANSWER,
            b.SHORT_ANSWER,
            b.SINGLE_OPTION_NUMBER,
            b.MORE_OPTION_NUMBER,
            b.JUDGE_NUMBER,
            b.FILL_NUMBER,
            b.SHORT_NUMBER
        FROM COLLEGE_TRAIN_POST_INFO_CARD a
                 LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY b ON b.CATEGORY_ITEM ='上岗证考核'
        WHERE a.ASSESSED_ID =(SELECT ID
                              FROM COLLEGE_TRAIN_ASSESSED
                              WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
          AND a.POST_INFO_ID =#{postInfo}
    </select>
<!--    获取考试时间以添加其他考试记录-->
    <select id="afterAddExamLog" resultType="com.htks.domain.student.dto.ExamRecord">
        SELECT a.ID,
               a.EXAM_START_TIME,
               a.EXAM_END_TIME,
               a.CATEGORY_ITEM,
               b.ID as assessedId
        FROM COLLEGE_TRAIN_ASSESSED_EXAM a
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON b.BATCH =a.BATCH
        WHERE a.CATEGORY_ITEM=#{categoryItem} AND b.BATCH =
                                     (SELECT batch FROM COLLEGE_TRAIN_ASSESSED WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO=#{employeeNo})
          AND b.ID = (SELECT ID
               FROM COLLEGE_TRAIN_ASSESSED
               WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo}) order by a.CREATED_TIME desc limit 1
    </select>

    <!--    获取考试时间以添加上岗证考试记录-->
    <select id="afterAddPostExamLog" resultType="com.htks.domain.student.dto.ExamRecord">
        SELECT a.PAPER_NAME                  as paperName,
               d.ID                          as postInfoId,
               b.POST_CERTIFICATE_START_TIME as examStartTime,
               b.POST_CERTIFICATE_END_TIME   as examEndTime,
               c.ASSESSED_ID
        FROM COLLEGE_TRAIN_EXAM_CATEGORY_PAPER a
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO d ON d.ID = a.POST_INFO_ID
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO_CARD c ON c.POST_INFO_ID = d.ID
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON b.ID = c.ASSESSED_ID
        WHERE b.EMPLOYEE_NO = #{employeeNo}
          AND a.POST_INFO_ID =#{postInfoId}

        order by a.CREATED_TIME desc limit 1

    </select>
<!--获取考试时间-->
    <select id="getTestTime">
       select  TEST_TIME from COLLEGE_TRAIN_EXAM_CATEGORY where CATEGORY_ITEM=#{categoryItem}
    </select>

<!--获取学生批次号-->
    <select id="getCollegeBatch" resultType="string">
        select batch from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeeNo}
    </select>
    <!--获取IQ考试名-->
    <select id="getIQExam" resultType="long">
        SELECT ID
        FROM COLLEGE_TRAIN_EXAM_CATEGORY_PAPER
        WHERE PAPER_NAME = #{paperName}
    </select>

    <select id="getFirstBatch" resultType="string">
        SELECT MIN(a.BATCH) FROM  COLLEGE_TRAIN_ASSESSED a WHERE a.BATCH LIKE  concat(#{value},'%')
    </select>

    <select id="getPaperIdByPaperName" resultType="long">
        select ID from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER where PAPER_NAME=#{paperName}
    </select>


    <!--新增考试记录-->
    <insert id="addExamRecord1" keyProperty="ID"
            useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
            SELECT COLLEGE_TRAIN_EXAM_RECORD_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_EXAM_RECORD
        (ID,ASSESSED_ID,EXAM_NAME,ALLOCATION_STATUS,POST_INFO_ID,EXAM_CATEGORY_ID,EXAM_START_TIME)
        values
        (#{ID},#{ER.assessedId},#{ER.examName},'未分配',#{postInfoId},#{ER.examCategoryId},#{ER.examStartTime})
    </insert>
<insert id="insertDemo" keyProperty="ID"
        useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
        SELECT COLLEGE_TRAIN_EXAM_RECORD_ANSWER_SEQ.NEXTVAL FROM DUMMY
    </selectKey>
    insert into COLLEGE_TRAIN_EXAM_RECORD_ANSWER
    (ID, EXAM_ID, QUESTION_ID, ANSWER_TEXT, SCORE, ATTACHMENT_ID)
    VALUES
    (#{ID}, #{CTER.examId}, #{CTER.questionId}, #{CTER.answerText}, 0, #{CTER.attachmentId});
</insert>

    <select id="getExamCatyId" resultType="long">
        SELECT a.ID from    COLLEGE_TRAIN_EXAM_CATEGORY a WHERE a.CATEGORY_ITEM =#{categoryItem}
    </select>


    <select id="getExamType" resultType="string">
        SELECT a.PAPER_TYPE FROM COLLEGE_TRAIN_EXAM_CATEGORY a WHERE a.CATEGORY_ITEM =#{categoryItem}
    </select>

    <select id="getPostCount" resultType="integer">
        SELECT count(1)
        FROM COLLEGE_TRAIN_EXAM_RECORD where ASSESSED_ID=#{id} and POST_INFO_ID=#{postId};

    </select>
    <select id="getPostCountById" resultType="integer">
        SELECT count(1)
        FROM COLLEGE_TRAIN_EXAM_RECORD where ASSESSED_ID=#{id} and POST_INFO_ID=#{postId}
and #{examID} >=ID
    </select>
<select id="getId" resultType="Long">
    select  ID from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo}
</select>
    <select id="getQuestDisposition" resultType="com.htks.domain.student.dto.QuestDisposition">
        SELECT * FROM COLLEGE_TRAIN_QUEST_DISPOSITION where EXAM_NAME =#{examName} and FACTORY_FLAG=(select FACTORY_FLAG from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeNo} )
                                                        and BATCH=(select BATCH from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeNo} )

    </select>
    <select id="getQuestDispositionByDepartment" resultType="com.htks.domain.student.dto.QuestDisposition">
        SELECT * FROM COLLEGE_TRAIN_QUEST_DISPOSITION where EXAM_NAME =#{examName} and FACTORY_FLAG=(select FACTORY_FLAG from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeNo} )
                                                        and BATCH=(select BATCH from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeNo} )
and EXAM_OBJECT=(select PRACTICE_DEPARTMENT from COLLEGE_TRAIN_POST_AREA where id=(select PRACTICE_DEPARTMENT_AREA_ID from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeNo} ) )

    </select>

    <select id="getcollegeTrainAssessed" resultType="com.htks.domain.school.dto.CollegeTrainAssessed">
        select * from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{no}
    </select>

    <select id="getArea" resultType="string">
        select AREA from COLLEGE_TRAIN_POST_AREA   where ID = (select PRACTICE_DEPARTMENT_AREA_ID from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeeNo})
    </select>

    <update id="updateScore">
        update COLLEGE_TRAIN_EXAM_RECORD set SUBJECTIVE_SCORE=#{score1},TOTAL_SCORE=#{score2} where ASSESSED_ID=(SELECT ID FROM COLLEGE_TRAIN_ASSESSED WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO=#{employeeNo})
and EXAM_CATEGORY_ID=#{examId}
    </update>

    <select id="getScore" resultType="string">
        select  OBJECTIVE_SCORE from   COLLEGE_TRAIN_EXAM_RECORD where ASSESSED_ID=(SELECT ID FROM COLLEGE_TRAIN_ASSESSED WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO=#{employeeNo})
                and EXAM_CATEGORY_ID=#{examId}
    </select>
    <select id="paperName" resultType="string">
select PAPER_NAME from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER where POST_INFO_ID=#{id} order by ID desc limit 1
    </select>
    <select id="getDep" resultType="string">
select CHOOSE_DEPARTMENT_NAME from COLLEGE_TRAIN_ASSESSED WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO=#{employeeNo}
    </select>
    <select id="getCompany" resultType="string">
        select FACTORY_FLAG from COLLEGE_TRAIN_ASSESSED WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO=#{employeeNo}

    </select>
</mapper>

