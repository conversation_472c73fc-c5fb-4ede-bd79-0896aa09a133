<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.examiner.repository.hana.ExaminerRepository">
<!--输入学号，自动匹配其他信息-->
    <select id="startApproval" resultType="com.htks.domain.examiner.dto.Colleger">

        select EMPLOYEE_NAME                 as StudentName,
               PRACTICE_DEPARTMENT           as StudentDepartment,
               AREA                          as StudentArea,
               a.PRACTICE_DEPARTMENT_AREA_ID AS StudentAreaId,
               a.EMPLOYEE_NO                 as StudentNo
        from COLLEGE_TRAIN_ASSESSED a
                 left join COLLEGE_TRAIN_POST_AREA b on a.PRACTICE_DEPARTMENT_AREA_ID = b.id
        where a.EMPLOYEE_NO = #{studentID}
    </select>

    <insert id="approvalComplete" parameterType="com.htks.domain.examiner.dto.Examiner" keyProperty="ID"
            useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="ID">
            SELECT COLLEGE_TRAIN_APPOINT_RECORD_DETAIL_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_APPOINT_RECORD_DETAIL
        (ID,APPOINT_RECORD_ID,TYPE,SCORE,QUESTION,ANSWER,EVALUATOR_NO,EVALUATOR_NAME,EVALUATOR_TYPE)
        values
        (#{ID},#{AppointRecordId},#{ex.type},#{ex.score},#{ex.question},#{ex.answer},#{ex.evaluatorNo},#{EVALUATOR_NAME},#{EVALUATOR_TYPE})
    </insert>

    <select id="checkApprovalFirst" resultType="string">

        select COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.EVALUATOR_NO
        from COLLEGE_TRAIN_APPOINT_RECORD_DETAIL
        WHERE COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.APPOINT_RECORD_ID = (SELECT COLLEGE_TRAIN_APPOINT_RECORD.ID
                                                                       FROM COLLEGE_TRAIN_APPOINT_RECORD
                                                                       where   COLLEGE_TRAIN_APPOINT_RECORD.ASSESSED_ID =
                                                                             (SELECT COLLEGE_TRAIN_ASSESSED.ID
                                                                              FROM COLLEGE_TRAIN_ASSESSED
                                                                              where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
                                                                         and COLLEGE_TRAIN_APPOINT_RECORD.TYPE =  #{type}  order by COLLEGE_TRAIN_APPOINT_RECORD.CREATED_TIME DESC LIMIT 1)

    </select>
    <select id="checkApprovalNew" resultType="string">

        select COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.EVALUATOR_TYPE
        from COLLEGE_TRAIN_APPOINT_RECORD_DETAIL
        WHERE COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.APPOINT_RECORD_ID = (SELECT COLLEGE_TRAIN_APPOINT_RECORD.ID
                                                                       FROM COLLEGE_TRAIN_APPOINT_RECORD
                                                                       where   COLLEGE_TRAIN_APPOINT_RECORD.ASSESSED_ID =
                                                                               (SELECT COLLEGE_TRAIN_ASSESSED.ID
                                                                                FROM COLLEGE_TRAIN_ASSESSED
                                                                                where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
                                                                         and COLLEGE_TRAIN_APPOINT_RECORD.TYPE =  #{type}  order by COLLEGE_TRAIN_APPOINT_RECORD.CREATED_TIME DESC LIMIT 1)

    </select>

    <select id="getCheckApprovalNo" resultType="string">

        select COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.EVALUATOR_TYPE
        from COLLEGE_TRAIN_APPOINT_RECORD_DETAIL
        WHERE EVALUATOR_NO=#{evaluatorNo} and COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.APPOINT_RECORD_ID = (SELECT COLLEGE_TRAIN_APPOINT_RECORD.ID
                                                                       FROM COLLEGE_TRAIN_APPOINT_RECORD
                                                                       where   COLLEGE_TRAIN_APPOINT_RECORD.ASSESSED_ID =
                                                                               (SELECT COLLEGE_TRAIN_ASSESSED.ID
                                                                                FROM COLLEGE_TRAIN_ASSESSED
                                                                                where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
                                                                         and COLLEGE_TRAIN_APPOINT_RECORD.TYPE =  #{type}  order by COLLEGE_TRAIN_APPOINT_RECORD.CREATED_TIME DESC LIMIT 1)

    </select>

    <select id="checkApprovalSecond" resultType="string">
        SELECT DEPARTMENT_NAME
        FROM COMMON_EMPLOYEE ce
        WHERE EMPLOYEE_NUMBER = #{EVALUATOR_NO}
    </select>
<!--修改前先去查询是否三个部门都已经完成评价，都完成再去更新预约表-->
    <update id="updateAPPOINT">
        update COLLEGE_TRAIN_APPOINT_RECORD
        set SCORE=#{ex.score},
            status=#{ex.status},
            PASSED_DATE=#{ex.passedDate},
            PASSED=#{ex.passed},
            ADDITIONAL_POINTS= #{ex.additionalPoints}
        WHERE COLLEGE_TRAIN_APPOINT_RECORD.ASSESSED_ID = (SELECT COLLEGE_TRAIN_ASSESSED.ID
                                                          FROM COLLEGE_TRAIN_ASSESSED
                                                          WHERE COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{ex.employeeNo})
and COLLEGE_TRAIN_APPOINT_RECORD.TYPE=#{type}  and COLLEGE_TRAIN_APPOINT_RECORD.STATUS='未考核'   and APPOINT_STATUS='1'
    </update>
<!--检查是否三个部门都已经完成评价-->
    <select id="checkApprovalThird" resultType="int">
        select COUNT(COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.EVALUATOR_NO)
        from COLLEGE_TRAIN_APPOINT_RECORD_DETAIL
        WHERE COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.APPOINT_RECORD_ID = (SELECT COLLEGE_TRAIN_APPOINT_RECORD.ID
                                                                       FROM COLLEGE_TRAIN_APPOINT_RECORD
                                                                       WHERE STATUS='未考核'       and APPOINT_STATUS='1' and COLLEGE_TRAIN_APPOINT_RECORD.ASSESSED_ID =
                                                                             (SELECT COLLEGE_TRAIN_ASSESSED.ID
                                                                              FROM COLLEGE_TRAIN_ASSESSED
                                                                              where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
                                                                         AND TYPE = #{type})
          AND COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.TYPE = #{type}


    </select>
<!--查询各个部门的评价分数-->
    <select id="calculateEachGrades" resultType="double">

        select COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.SCORE from COLLEGE_TRAIN_APPOINT_RECORD_DETAIL
        WHERE COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.EVALUATOR_NO =#{evaluatorNo} AND COLLEGE_TRAIN_APPOINT_RECORD_DETAIL.TYPE =#{type}
          and APPOINT_RECORD_ID=
              (SELECT COLLEGE_TRAIN_APPOINT_RECORD.ID FROM COLLEGE_TRAIN_APPOINT_RECORD WHERE COLLEGE_TRAIN_APPOINT_RECORD.ASSESSED_ID =
             (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
          AND TYPE = #{type} order by CREATED_TIME desc limit 1)
    </select>
<!--    获取预约表里的预约id-->
    <select id="sureAppointRecordId" resultType="long">
        SELECT a.id
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
        WHERE a.ASSESSED_ID=(select  ID from COLLEGE_TRAIN_ASSESSED where COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO = #{employeeNo})
                 and TYPE=#{type} and STATUS='未考核'
          and APPOINT_STATUS='1'
    </select>
<!-- 获取姓名-->
    <select id="collegeName" resultType="string">

        SELECT EMPLOYEE_NAME FROM COMMON_EMPLOYEE   WHERE EMPLOYEE_NUMBER =  #{employeeNo};
    </select>

    <select id="getApprovalSum" resultType="integer">
        select count (id) from COLLEGE_TRAIN_APPOINT_RECORD where ASSESSED_ID=(SELECT ID  FROM COLLEGE_TRAIN_ASSESSED   WHERE EMPLOYEE_NO =  #{employeeNo})and TYPE=#{type} and STATUS='未考核'
    </select>
    <select id="getApprovalDate" resultType="string">
        select APPOINT_START_DATE from COLLEGE_TRAIN_APPOINT_RECORD where ASSESSED_ID=(SELECT ID  FROM COLLEGE_TRAIN_ASSESSED   WHERE EMPLOYEE_NO =  #{employeeNo})and TYPE=#{type} and STATUS='未考核'
and APPOINT_STATUS='1'
    </select>
    <!--//1:异常答辩评分标准 2：满意度评分表-->
    <select id="getSatisfactionOrAbnormalDefenseCriteria" resultType="com.htks.domain.examiner.dto.SatisfactionOrAbnormalDefenseCriteria">
        SELECT ID, TYPE, CATEGORY, EVALUATE_STANDARD, SCORE, STANDARD1, STANDARD2, STANDARD3, STANDARD4, STANDARD5
        FROM COLLEGE_TRAIN_SATISFACTION_EVALUATION where type=#{type}

    </select>
    <!--  //1:SOP评分标准 2：异常实操评分-->
    <select id="getSopOrHandsOnCriteria" resultType="com.htks.domain.examiner.dto.SopOrHandsOnCriteria">
        SELECT ID, "TYPE", POST, CATEGORY, TOTAL_SCORE, STANDARD1, SCORE1, STANDARD2, SCORE2, STANDARD3, SCORE3, STANDARD4, SCORE4
        FROM COLLEGE_TRAIN_SOP_EVALUATION
        where type=#{type}

    </select>
    <select id="getAsssNo" resultType="string">
        select CHOOSE_DEPARTMENT_NAME from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{no}
    </select>
<select id="getSum" resultType="integer">
     select count(id) from  COLLEGE_TRAIN_APPOINT_RECORD a where ASSESSED_ID=(select id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo}) and a.type=#{type} and APPOINT_STATUS='1'
</select>
</mapper>