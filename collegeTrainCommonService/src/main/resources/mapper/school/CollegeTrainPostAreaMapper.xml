<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.school.repository.hana.CollegeTrainPostAreaMapper">

    <select id="getAllDepartment" resultType="java.lang.String">
        select distinct PRACTICE_DEPARTMENT from COLLEGE_TRAIN_POST_AREA
        where 1=1
        <if test="factoryFlag !=null and factoryFlag !='' ">
            and FACTORY_FLAG = #{factoryFlag}
        </if>

    </select>
<select id="getFactoryFlag" resultType="string">
    select FACTORY_FLAG from  COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{employeeNo}
</select>

    <select id="getCollegeTrainDepartment" resultType="com.htks.domain.school.dto.CollegeTrainDepartment">
        select ID as id , DEPARTMENT  as practiceDepartment ,FACTORY_FLAG as factoryFlag from COLLEGE_TRAIN_DEPARTMENT
    </select>
</mapper>