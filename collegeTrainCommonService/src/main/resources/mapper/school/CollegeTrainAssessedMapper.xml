<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.school.repository.hana.CollegeTrainAssessedMapper">

    <select id="updateEnabled">
        update  COLLEGE_TRAIN_ASSESSED set enabled=#{enabled} where EMPLOYEE_NO=#{employeeNo}
    </select>
<select id="getDepartment" resultType="string">
select PRACTICE_DEPARTMENT from  COLLEGE_TRAIN_POST_AREA  where ID=(select PRACTICE_DEPARTMENT_AREA_ID from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO=#{employeeNo} )
</select>
    <select id="getArea" resultType="string">
  select AREA from  COLLEGE_TRAIN_POST_AREA  where ID=(select PRACTICE_DEPARTMENT_AREA_ID from COLLEGE_TRAIN_ASSESSED where EMPL<PERSON>YEE_NO=#{employeeNo} )

    </select>
</mapper>