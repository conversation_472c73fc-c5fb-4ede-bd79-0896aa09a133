<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.htks.domain.sys.repository.hana.SysLogRepository">

  <!-- 保存系统日志 -->
  <insert id="saveSysLog" parameterType="com.htks.domain.sys.dto.SysLogEntity" keyProperty="id" useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
      SELECT SEQ_COMMON.NEXTVAL FROM DUMMY
    </selectKey>
    insert into common_log(
    ID,
    EMPLOYEE_NAME,
    OPERATION,
    METHOD_NAME,
    PARAMS,
    OPERATION_TIME,
    IP,
    SYSTEM_IDENTIFIER,
    CREATER,
    UPDATER
    )
    values
    (#{id},#{sysLog.employeeName},#{sysLog.operation},#{sysLog.methodName},#{sysLog.params},#{sysLog.operationTime},#{sysLog.ip},#{sysLog.systemIdentifier},#{sysLog.creater},#{sysLog.updater})
  </insert>

</mapper>