<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.htks.domain.sys.repository.hana.SysDefaultRepository">

  <!-- 查询角色信息 -->
  <select id="getRoleByEmployee" resultType="com.htks.domain.sys.dto.SysRoleEntity">
      SELECT cer.ROLE_ID
      FROM POSTS_UPGRADE_USER_ADMIN cer
      WHERE cer.EMPLOYEE_NO =#{employeeNumber} and cer.DELETED_FLAG = FALSE
    </select>

  <!-- 查询角色信息 -->
  <select id="getStudent" resultType="Integer">
      SELECT count(1) FROM POSTS_UPGRADE_USER_ASSESSED WHERE DELETED_FLAG =FALSE AND ENABLED =TRUE AND EMPLOYEE_NO =#{employeeNumber}
  </select>


</mapper>