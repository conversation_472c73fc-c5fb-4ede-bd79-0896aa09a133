<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.htks.domain.sys.repository.mysql.SysEmployeeRepository">


  <select id="getEmployeeEmail" resultType="com.htks.domain.sys.dto.SysEmployeeEntity">
     SELECT
     NAME AS employeeName,
     EMPLOYEE_NUMBER,
     WXWORK_USERID AS wxworkUserid,
     IN_PLANT_MAIL
     FROM HRM_EMPLOYEE_INFO
     WHERE EMPLOYEE_NUMBER IN
     <foreach collection="evaluatorNumberSet" item="item" open="(" close=")" separator=",">
       #{item}
     </foreach>
  </select>

  <!-- 查询员工信息 -->
  <select id="getEmployeeInfo" resultType="com.htks.domain.sys.dto.SysEmployeeEntity">
    SELECT
          S1.EMPLOYEE_INFO_ID id,
          S1.EMPLOYEE_NUMBER,
          S1.NAME employeeName,
          S1.COMPANY_ID,
          S1.DEPARTMENT_ID,
          S1.GROUP_ID,
          S1.STATION_ID,
          S1.CLASS_BAN_ID,
          S1.JOB_ID,
          S1.POSITION_ID,
          BC.SHORTER AS companyName,
          BDI1.NAME AS departmentName,
          BDI2.NAME AS groupName,
          BDI3.NAME AS stationName,
          BDI4.NAME AS classBanName,
          BJ.NAME AS jobName,
          BP.NAME AS positionName
        FROM
          (
          SELECT
            T1.ID EMPLOYEE_INFO_ID,
            EMPLOYEE_NUMBER,
            NAME,
            PERSONAL_INFO_ID,
            STAFF_SOURCE_ID,
            POLITICAL_APPEARANCE_ID,
            IN_PLANT_MAIL,
            IN_PLANT_TEL,
            ONBOARDING_ID,
            LEAVE_OFFICE_ID,
            INTRODUCER,
            ARCHIVES_NUMBER,
            INSURANCE_NUMBER,
            IS_SIGN_CONFIDENTIALITY_AGREEMENT,
            IS_NEED_APPLY_FOR_OVERTIME,
            IS_NOT_CLOCK,
            REINSTATEMENT_ID,
            T2.ID TRANSFER_ID,
            `NUMBER` TRANSFER_NUMBER,
            TRANSFER_TYPE_ID,
            TRANSFER_TIME,
            TRANSFER_REASON,
            COMPANY_ID,
            DEPARTMENT_ID,
            GROUP_ID,
            STATION_ID,
            CLASS_BAN_ID,
            EMPLOYEE_CATEGORY_ID,
            FIRST_SUPERVISOR_ID,
            SECOND_SUPERVISOR_ID,
            ASSISTANT_ID,
            JOB_ID,
            POSITION_ID,
            GRADE_LEVEL_ID,
            EFFECTIVE_TIME,
            T1.STATUS S_STATUS,
            AB,
            THIRD_SUPERVISOR_ID,
            FOURTH_SUPERVISOR_ID,
            FIFTH_SUPERVISOR_ID,
            SIXTH_SUPERVISOR_ID,
            SEVENTH_SUPERVISOR_ID,
            EIGHTH_SUPERVISOR_ID,
            COST_CENTER_ID,
            PREPARED_SUPERVISOR_ID
          FROM
            (
            SELECT
              ID,
              EMPLOYEE_NUMBER,
              NAME,
              PERSONAL_INFO_ID,
              STAFF_SOURCE_ID,
              POLITICAL_APPEARANCE_ID,
              IN_PLANT_MAIL,
              IN_PLANT_TEL,
              ONBOARDING_ID,
              LEAVE_OFFICE_ID,
              INTRODUCER,
              ARCHIVES_NUMBER,
              INSURANCE_NUMBER,
              IS_SIGN_CONFIDENTIALITY_AGREEMENT,
              IS_NEED_APPLY_FOR_OVERTIME,
              IS_NOT_CLOCK,
              REINSTATEMENT_ID,
              STATUS
            FROM
              HRM_EMPLOYEE_INFO
            WHERE
              STATUS = 1 AND EMPLOYEE_NUMBER = #{employeeNumber}
            ) T1
            INNER JOIN (SELECT * FROM HRM_EMPLOYEE_TRANSFER WHERE ID IN ( SELECT MAX( ID ) FROM HRM_EMPLOYEE_TRANSFER WHERE SYSDATE() > EFFECTIVE_TIME AND STATUS = 5 GROUP BY EMPLOYEE_INFO_ID )
            ) T2 ON T1.ID = T2.EMPLOYEE_INFO_ID
          ) S1
          LEFT JOIN BASE_COMPANY BC ON S1.COMPANY_ID = BC.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI1 ON S1.DEPARTMENT_ID = BDI1.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI2 ON S1.GROUP_ID = BDI2.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI3 ON S1.STATION_ID = BDI3.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI4 ON S1.CLASS_BAN_ID = BDI4.ID
          LEFT JOIN BASE_JOB BJ ON S1.JOB_ID = BJ.ID
          LEFT JOIN BASE_POSITION BP ON S1.POSITION_ID = BP.ID
  </select>

  <!-- 查询员工信息 -->
  <select id="queryEmployeeByCondition" resultType="com.htks.domain.sys.dto.SysEmployeeEntity">
    SELECT
          S1.EMPLOYEE_INFO_ID id,
          S1.EMPLOYEE_NUMBER,
          S1.NAME employeeName,
          S1.COMPANY_ID,
          S1.DEPARTMENT_ID,
          S1.GROUP_ID,
          S1.STATION_ID,
          S1.CLASS_BAN_ID,
          S1.JOB_ID,
          S1.POSITION_ID,
          BC.SHORTER AS companyName,
          BDI1.NAME AS departmentName,
          BDI2.NAME AS groupName,
          BDI3.NAME AS stationName,
          BDI4.NAME AS classBanName,
          BJ.NAME AS jobName,
          BP.NAME AS positionName
        FROM
          (
          SELECT
            T1.ID EMPLOYEE_INFO_ID,
            EMPLOYEE_NUMBER,
            NAME,
            PERSONAL_INFO_ID,
            STAFF_SOURCE_ID,
            POLITICAL_APPEARANCE_ID,
            IN_PLANT_MAIL,
            IN_PLANT_TEL,
            ONBOARDING_ID,
            LEAVE_OFFICE_ID,
            INTRODUCER,
            ARCHIVES_NUMBER,
            INSURANCE_NUMBER,
            IS_SIGN_CONFIDENTIALITY_AGREEMENT,
            IS_NEED_APPLY_FOR_OVERTIME,
            IS_NOT_CLOCK,
            REINSTATEMENT_ID,
            T2.ID TRANSFER_ID,
            `NUMBER` TRANSFER_NUMBER,
            TRANSFER_TYPE_ID,
            TRANSFER_TIME,
            TRANSFER_REASON,
            COMPANY_ID,
            DEPARTMENT_ID,
            GROUP_ID,
            STATION_ID,
            CLASS_BAN_ID,
            EMPLOYEE_CATEGORY_ID,
            FIRST_SUPERVISOR_ID,
            SECOND_SUPERVISOR_ID,
            ASSISTANT_ID,
            JOB_ID,
            POSITION_ID,
            GRADE_LEVEL_ID,
            EFFECTIVE_TIME,
            T1.STATUS S_STATUS,
            AB,
            THIRD_SUPERVISOR_ID,
            FOURTH_SUPERVISOR_ID,
            FIFTH_SUPERVISOR_ID,
            SIXTH_SUPERVISOR_ID,
            SEVENTH_SUPERVISOR_ID,
            EIGHTH_SUPERVISOR_ID,
            COST_CENTER_ID,
            PREPARED_SUPERVISOR_ID
          FROM
            (
            SELECT
              ID,
              EMPLOYEE_NUMBER,
              NAME,
              PERSONAL_INFO_ID,
              STAFF_SOURCE_ID,
              POLITICAL_APPEARANCE_ID,
              IN_PLANT_MAIL,
              IN_PLANT_TEL,
              ONBOARDING_ID,
              LEAVE_OFFICE_ID,
              INTRODUCER,
              ARCHIVES_NUMBER,
              INSURANCE_NUMBER,
              IS_SIGN_CONFIDENTIALITY_AGREEMENT,
              IS_NEED_APPLY_FOR_OVERTIME,
              IS_NOT_CLOCK,
              REINSTATEMENT_ID,
              STATUS
            FROM
              HRM_EMPLOYEE_INFO
            WHERE
              STATUS = 1
            ) T1
            INNER JOIN (SELECT * FROM HRM_EMPLOYEE_TRANSFER WHERE ID IN ( SELECT MAX( ID ) FROM HRM_EMPLOYEE_TRANSFER WHERE SYSDATE() > EFFECTIVE_TIME AND STATUS = 5 GROUP BY EMPLOYEE_INFO_ID )
            ) T2 ON T1.ID = T2.EMPLOYEE_INFO_ID
          ) S1
          LEFT JOIN BASE_COMPANY BC ON S1.COMPANY_ID = BC.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI1 ON S1.DEPARTMENT_ID = BDI1.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI2 ON S1.GROUP_ID = BDI2.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI3 ON S1.STATION_ID = BDI3.ID
          LEFT JOIN BASE_DEPARTMENT_INFO BDI4 ON S1.CLASS_BAN_ID = BDI4.ID
          LEFT JOIN BASE_JOB BJ ON S1.JOB_ID = BJ.ID
          LEFT JOIN BASE_POSITION BP ON S1.POSITION_ID = BP.ID
          <where>
            <if test="groupId != null and groupId != ''">
              AND S1.GROUP_ID = #{groupId}
            </if>
          </where>
  </select>

</mapper>
