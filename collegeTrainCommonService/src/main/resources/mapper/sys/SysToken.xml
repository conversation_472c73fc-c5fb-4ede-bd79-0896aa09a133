<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.htks.domain.sys.repository.hana.SysTokenRepository">

  <!--保存Token -->
  <insert id="saveToken" parameterType="com.htks.domain.sys.dto.SysTokenEntity" keyProperty="id" useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
      SELECT SEQ_COMMON.NEXTVAL FROM DUMMY
    </selectKey>
    INSERT INTO COMMON_TOKEN(
    ID,EMPLOYEE_NUMBER,TOKEN,EXPIRE_TIME,UPDATE_TIME,IP
    )
    VALUES
    (#{id},#{employeeNumber},#{token},#{expireTime},#{updateTime},#{ip})
  </insert>

  <!--更新Token -->
  <update id="updateToken" parameterType="com.htks.domain.sys.dto.SysTokenEntity">
    UPDATE COMMON_TOKEN
    <set>
      <if test="token != null and token != ''">TOKEN = #{token},</if>
      <if test="expireTime != null">EXPIRE_TIME = #{expireTime},</if>
      <if test="updateTime != null">UPDATE_TIME = #{updateTime}</if>
    </set>
    WHERE EMPLOYEE_NUMBER = #{employeeNumber} AND IP = #{ip}
  </update>

  <!--更新过期时间 -->
  <update id="updateExpireTime">
    UPDATE COMMON_TOKEN
    <set>
      <if test="expireTime != null">EXPIRE_TIME = #{expireTime},</if>
      "UPDATE_TIME" = now()
    </set>
    WHERE EMPLOYEE_NUMBER = #{employeeNumber} AND TOKEN = #{token}
  </update>

  <!--根据工号更新当前IP之外的其余Token的过期时间 -->
  <update id="kickOutOtherToken">
    UPDATE COMMON_TOKEN
    <set>
      <if test="expireTime != null">EXPIRE_TIME = #{expireTime},</if>
      "UPDATE_TIME" = now()
    </set>
    WHERE EMPLOYEE_NUMBER = #{employeeNumber} AND IP != #{ip} AND EXPIRE_TIME > now()
  </update>

  <!-- 根据工号与登录IP查询Token -->
  <select id="queryTokenByEmployeeNumberAndIp" resultType="com.htks.domain.sys.dto.SysTokenEntity">
        SELECT
         t.ID,
         t.EMPLOYEE_NUMBER employeeNumber,
         t.TOKEN token,
         t.EXPIRE_TIME expireTime,
         t.UPDATE_TIME updateTime,
         t.IP ip
        FROM COMMON_TOKEN t
        WHERE t.EMPLOYEE_NUMBER = #{employeeNumber} AND  t.IP = #{ip}
    </select>

  <!-- 根据工号与查询Token -->
  <select id="queryTokenByEmployeeNumber" resultType="com.htks.domain.sys.dto.SysTokenEntity">
        SELECT
         t.ID,
         t.EMPLOYEE_NUMBER employeeNumber,
         t.TOKEN token,
         t.EXPIRE_TIME expireTime,
         t.UPDATE_TIME updateTime,
         t.IP ip
        FROM COMMON_TOKEN t
        WHERE t.EMPLOYEE_NUMBER = #{employeeNumber}
    </select>

</mapper>
