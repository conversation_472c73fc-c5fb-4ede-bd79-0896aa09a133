<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.question.repository.hana.QuestionRepository">

    <select id="queryPaperCount" resultType="java.lang.Integer">
        select
        count(1)
        from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER a
        LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON a.EXAM_CATEGORY_ID =ctec.ID
        LEFT JOIN COMMON_EMPLOYEE ce ON a.CREATED_USER =ce.EMPLOYEE_NUMBER
        LEFT JOIN COLLEGE_TRAIN_QUESTION ctq ON ctq.PAPER_ID =a.ID and ctq.DELETED_FLAG=false
        WHERE a.DELETED_FLAG=FALSE
        <if test="department !=null and department !='' ">
            and a.DEPARTMENT = #{department}
        </if>
        <if test="category !=null and category !='' ">
            and ctec.CATEGORY = #{category}
        </if>
        <if test="paperType !=null and paperType !='' ">
            and ctec.PAPER_TYPE = #{paperType}
        </if>
        <if test="questionType !=null and questionType !='' ">
            and ctq.QUESTION_TYPE = #{questionType}
        </if>
        <if test="questionContent !=null and questionContent !='' ">
            and ctq.QUESTION_CONTENT LIKE CONCAT(CONCAT('%',#{questionContent}),'%')
        </if>
        <if test="paperName !=null and paperName !='' ">
            and a.PAPER_NAME LIKE CONCAT(CONCAT('%',#{paperName}),'%')
        </if>
    </select>

    <select id="queryPaperList" resultType="com.htks.domain.question.dto.PaperEntityDto">
        select
        a.ID,
        a.DEPARTMENT,
        ctec.CATEGORY ,
        ctec.CATEGORY_ITEM ,
        ctec.PAPER_TYPE ,
        ce.EMPLOYEE_NAME creator,
        a.PAPER_NAME,
        ctq.id questionId,
        ctq.QUESTION_TYPE questionType,
        ctq.QUESTION_CONTENT questionContent
        from COLLEGE_TRAIN_EXAM_CATEGORY_PAPER a
        LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON a.EXAM_CATEGORY_ID =ctec.ID
        LEFT JOIN COMMON_EMPLOYEE ce ON a.CREATED_USER =ce.EMPLOYEE_NUMBER
        LEFT JOIN COLLEGE_TRAIN_QUESTION ctq ON ctq.PAPER_ID =a.ID and ctq.DELETED_FLAG=false
        WHERE a.DELETED_FLAG=FALSE
        <if test="department !=null and department !='' ">
            and a.DEPARTMENT = #{department}
        </if>
        <if test="category !=null and category !='' ">
            and ctec.CATEGORY = #{category}
        </if>
        <if test="paperType !=null and paperType !='' ">
            and ctec.PAPER_TYPE = #{paperType}
        </if>
        <if test="questionType !=null and questionType !='' ">
            and ctq.QUESTION_TYPE = #{questionType}
        </if>
        <if test="questionContent !=null and questionContent !='' ">
            and ctq.QUESTION_CONTENT LIKE CONCAT(CONCAT('%',#{questionContent}),'%')
        </if>
        <if test="paperName !=null and paperName !='' ">
            and a.PAPER_NAME LIKE CONCAT(CONCAT('%',#{paperName}),'%')
        </if>
        <if test="limit > 0 and offset !=null">
            LIMIT #{limit} OFFSET #{offset}
        </if>
    </select>

    <insert id="addPaper">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_EXAM_CATEGORY_PAPER_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_EXAM_CATEGORY_PAPER
        (
        ID,
        EXAM_CATEGORY_ID,
        DEPARTMENT,
        PAPER_NAME,
        CREATED_USER,
        UPDATED_USER,
        POST_INFO_ID
        )
        values(
        #{id},
        #{examCategoryId},
        #{department},
        #{paperName},
        #{creator},
        #{updater},
        #{postInfoId}

        )
    </insert>
<!--    /* (SELECT ID
    FROM POST_NAME=#{postInfoId} and PRACTICE_DEPARTMENT=#{department} )*/-->
    <insert id="addQuestion">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_QUESTION_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_QUESTION
        (
        ID,
        PAPER_ID,
        QUESTION_TYPE,
        QUESTION_CONTENT,
        STANDARD_ANSWER,
        CREATED_USER,
        UPDATED_USER
        )
        values(
        #{id},
        #{paperId},
        #{questionType},
        #{questionContent},
        #{standardAnswer},
        #{creator},
        #{updater}
        )
    </insert>

    <insert id="addQuestionItem">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_QUESTION_ITEM_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_QUESTION_ITEM
        (
        ID,
        QUESTION_ID,
        ITEM_CONTENT,
        ITEM_OPTION
        )
        values(
        #{id},
        #{questionId},
        #{itemContent},
        #{itemOption}
        )
    </insert>

    <select id="getDepartmentList" resultType="com.htks.domain.question.dto.DepartmentDto">
        select distinct a.PRACTICE_DEPARTMENT department from COLLEGE_TRAIN_POST_AREA a
    </select>

    <select id="categoryList" resultType="com.htks.domain.question.dto.CategoryDto">
        select a.ID examCategoryId, a.CATEGORY_ITEM categoryItem from COLLEGE_TRAIN_EXAM_CATEGORY a
    </select>

    <update id="deletePaper">
        update COLLEGE_TRAIN_EXAM_CATEGORY_PAPER set DELETED_FLAG=true where id=#{id}
    </update>

    <update id="deletePaperQuestion">
        update COLLEGE_TRAIN_QUESTION set DELETED_FLAG=true where id=#{id}
    </update>

    <select id="getQuestionInfo" resultType="com.htks.domain.question.dto.QuestionEntity">
        select
         ID,
         PAPER_ID,
         QUESTION_TYPE,
         QUESTION_CONTENT,
         STANDARD_ANSWER
        from
        COLLEGE_TRAIN_QUESTION ctq
        where ctq.DELETED_FLAG=false
        and ID=#{id}
    </select>

    <select id="getAttachmentByQuestionId" resultType="com.htks.domain.course.dto.Attachment">
        select * from COLLEGE_TRAIN_ATTACHMENT where QUESTION_ID=#{questionId}
    </select>

    <select id="getQuestionItem" resultType="com.htks.domain.question.dto.QuestionItemEntity">
        select * from COLLEGE_TRAIN_QUESTION_ITEM ctqi where ctqi.QUESTION_ID=#{questionId} order by id asc
    </select>

    <delete id="deleteQuestionItem" >
        delete from COLLEGE_TRAIN_QUESTION_ITEM where QUESTION_ID=#{questionId}
    </delete>

    <update id="updateQuestion">
        update COLLEGE_TRAIN_QUESTION
        set PAPER_ID=#{paperId},
        QUESTION_TYPE=#{questionType},
        QUESTION_CONTENT=#{questionContent},
        UPDATED_TIME=current_timestamp
        where id=#{id}
    </update>
</mapper>