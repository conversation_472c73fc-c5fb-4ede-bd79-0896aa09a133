<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.htks.domain.admin.repository.hana.AdminRepository">
    <insert id="addAdmin">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT POSTS_UPGRADE_USER_ADMIN_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into POSTS_UPGRADE_USER_ADMIN
        (
        ID,
        EMPLOYEE_NO,
        EMPLOYEE_NAME,
        ROLE_ID,
        CREATED_USER
        )
        values(
        #{id},
        #{employeeNo},
        #{employeeName},
        #{roleId},
        #{createdUser}
        )
    </insert>

    <select id="queryAdminCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM POSTS_UPGRADE_USER_ADMIN main
        LEFT JOIN COMMON_EMPLOYEE employee ON employee.EMPLOYEE_NUMBER = MAIN.EMPLOYEE_NO
        WHERE MAIN.DELETED_FLAG= FALSE
        <if test="employeeNo != null and employeeNo !=''">
            AND main.EMPLOYEE_NO LIKE CONCAT(CONCAT('%',#{employeeNo}),'%')
        </if>
        <if test="employeeName != null and employeeName !=''">
            AND employee.EMPLOYEE_NAME  LIKE CONCAT(CONCAT('%',#{employeeName}),'%')
        </if>
        <if test="departmentName != null and departmentName !=''">
            AND employee.DEPARTMENT_NAME  LIKE CONCAT(CONCAT('%',#{departmentName}),'%')
        </if>
    </select>
    <select id="queryAdminList" resultType="com.htks.domain.admin.dto.AdminSearchEntity">
        SELECT
        main.ID,
        main.EMPLOYEE_NO,
        employee.EMPLOYEE_NAME,
        main.ROLE_ID,
        employee.DEPARTMENT_NAME
        FROM POSTS_UPGRADE_USER_ADMIN main
        LEFT JOIN COMMON_EMPLOYEE employee ON employee.EMPLOYEE_NUMBER = MAIN.EMPLOYEE_NO
        WHERE MAIN.DELETED_FLAG= FALSE
        <if test="employeeNo != null and employeeNo !=''">
            AND main.EMPLOYEE_NO LIKE CONCAT(CONCAT('%',#{employeeNo}),'%')
        </if>
        <if test="employeeName != null and employeeName !=''">
            AND employee.EMPLOYEE_NAME  LIKE CONCAT(CONCAT('%',#{employeeName}),'%')
        </if>
        <if test="departmentName != null and departmentName !=''">
            AND employee.DEPARTMENT_NAME  LIKE CONCAT(CONCAT('%',#{departmentName}),'%')
        </if>
        ORDER BY MAIN.CREATED_TIME  desc
        LIMIT #{limit} OFFSET #{offset}
    </select>
    <select id="getAdminInfoByEmployNo" resultType="com.htks.domain.common.dto.UpgradeUserAdminEntity">
        SELECT ID,
               EMPLOYEE_NO,
               ENABLED,
               EMPLOYEE_NAME,
               ZN_ID,
               ROLE_ID
        FROM POSTS_UPGRADE_USER_ADMIN WHERE DELETED_FLAG=FALSE AND EMPLOYEE_NO=#{employeeNo}
    </select>
    <update id="deleteAdmin">
        UPDATE POSTS_UPGRADE_USER_ADMIN
        <set>
            DELETED_FLAG = TRUE,
            UPDATED_TIME = CURRENT_TIMESTAMP
        </set>
        WHERE  ID =#{id}
    </update>
    <update id="updateAdmin">
        UPDATE POSTS_UPGRADE_USER_ADMIN
        <set>
            <if test="employeeNo != null and employeeNo !=''">EMPLOYEE_NO = #{employeeNo},</if>
            <if test="employeeName != null and employeeName !=''">EMPLOYEE_NAME = #{employeeName},</if>
            <if test="roleId != null and roleId !=''">ROLE_ID = #{roleId},</if>
            UPDATED_TIME = CURRENT_TIMESTAMP
        </set>
        WHERE ID =#{id}
    </update>
</mapper>