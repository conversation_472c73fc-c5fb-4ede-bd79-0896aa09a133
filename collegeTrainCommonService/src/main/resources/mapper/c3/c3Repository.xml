<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.htks.domain.trainee.repository.c3.C3Repository">
    <select id="selectFaceData" resultType="Map">
        SELECT
        t.AutoId,
        SUBSTRING(CONVERT(VARCHAR, t.RecordTime, 120), 1, 10)  as CLOCK_DATE,
        RIGHT(REPLICATE('0',5-LEN(h.EmpNo))+LTRIM(h.EmpNo),5) as EMPLOYEE_NUMBER,
        SUBSTRING(CONVERT(VARCHAR, t.RecordTime, 120), 12, 5)  as CLOCK_TIME,
        '0' as SECONDS,
        -- DeviceUniqueCode,
        '2570002' as RECID
        FROM
                [AIO20200928130632].[dbo].Ndr2_FaceDev361_Record t
        LEFT JOIN [AIO20200928130632].[dbo].Hrms_Emp h ON CONVERT(VARCHAR,h.uniqueCode) = t.UniqueCode
        WHERE 1=1
        AND h.EmpNo= #{employeeNo}
        AND SUBSTRING(CONVERT(VARCHAR, t.RecordTime, 120), 1, 10) = SUBSTRING(CONVERT(VARCHAR, CURRENT_TIMESTAMP , 120), 1, 10)
        AND DeviceUniqueCode in ('103CAD')
    </select>

    <select id="selectFaceDataHistory" resultType="Map">
        SELECT
        t.AutoId,
        SUBSTRING(CONVERT(VARCHAR, t.RecordTime, 120), 1, 10)  as CLOCK_DATE,
        RIGHT(REPLICATE('0',5-LEN(h.EmpNo))+LTRIM(h.EmpNo),5) as EMPLOYEE_NUMBER,
        SUBSTRING(CONVERT(VARCHAR, t.RecordTime, 120), 1, 16)  as CLOCK_TIME,
        '0' as SECONDS,
        -- DeviceUniqueCode,
        '2570002' as RECID
        FROM
                [AIO20200928130632].[dbo].Ndr2_FaceDev361_Record t
        LEFT JOIN [AIO20200928130632].[dbo].Hrms_Emp h ON CONVERT(VARCHAR,h.uniqueCode) = t.UniqueCode
        WHERE 1=1
        AND h.EmpNo= #{employeeNo}
        AND DeviceUniqueCode in ('103CAD')
    </select>
</mapper>
