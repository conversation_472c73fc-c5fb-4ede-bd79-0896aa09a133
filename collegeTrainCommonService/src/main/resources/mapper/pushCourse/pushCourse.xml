<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.pushCourse.repository.hana.PushCourseRepository">

    <select id="queryPushCourseCount" resultType="java.lang.Integer">
        select
        count(1)
        from COLLEGE_TRAIN_ASSESSED_COURSE a
        WHERE a.DELETED_FLAG=FALSE
        <if test="category !=null and category !='' ">
            and a.CATEGORY = #{category}
        </if>
        <if test="batch !=null and batch !='' ">
            and a.BATCH LIKE CONCAT(CONCAT('%',#{batch}),'%')
        </if>
        <if test="creator !=null and creator !='' ">
            and a.CREATED_USER LIKE CONCAT(CONCAT('%',#{creator}),'%')
        </if>
        <if test="pushObject !=null and pushObject !='' ">
            and a.PUSH_OBJECT LIKE CONCAT(CONCAT('%',#{pushObject}),'%')
        </if>
        <if test="factoryFlagList !=null and factoryFlagList.size()>0 ">
            and a.FACTORY_FLAG in
            <foreach collection="factoryFlagList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryPushCourseList" resultType="com.htks.domain.pushCourse.dto.PushCourseEntityDto">
        select
        a.ID,
        a.FACTORY_FLAG,
        a.BATCH,
        a.CATEGORY ,
        a.PUSH_OBJECT ,
        a.DEAD_TIME,
        ce.EMPLOYEE_NAME creator,
        ce.EMPLOYEE_NAME createName,
        TO_VARCHAR(a.UPDATED_TIME, 'YYYY-MM-DD HH24:MI:SS') AS pushTime
        from COLLEGE_TRAIN_ASSESSED_COURSE a
        left join COMMON_EMPLOYEE ce ON  ce.EMPLOYEE_NUMBER = SUBSTR(a.CREATED_USER, 4)
        WHERE a.DELETED_FLAG=FALSE
        <if test="category !=null and category !='' ">
            and a.CATEGORY = #{category}
        </if>
        <if test="batch !=null and batch !='' ">
            and a.BATCH LIKE CONCAT(CONCAT('%',#{batch}),'%')
        </if>
        <if test="creator !=null and creator !='' ">
            and a.CREATED_USER LIKE CONCAT(CONCAT('%',#{creator}),'%')
        </if>
        <if test="pushObject !=null and pushObject !='' ">
            and a.PUSH_OBJECT LIKE CONCAT(CONCAT('%',#{pushObject}),'%')
        </if>
        <if test="factoryFlagList !=null and factoryFlagList.size()>0 ">
            and a.FACTORY_FLAG in
            <foreach collection="factoryFlagList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by a.UPDATED_TIME desc
        <if test="limit > 0 and offset !=null">
            LIMIT #{limit} OFFSET #{offset}
        </if>
    </select>

    <select id="queryPushExamCount" resultType="java.lang.Integer">
        select
        count(1)
        from COLLEGE_TRAIN_ASSESSED_EXAM a
        WHERE a.DELETED_FLAG=FALSE
        <if test="category !=null and category !='' ">
            and a.CATEGORY = #{category}
        </if>
        <if test="categoryItem!= null and categoryItem.size() >0">
            and a.CATEGORY_ITEM in
            <foreach collection="categoryItem" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="batch !=null and batch !='' ">
            and a.BATCH LIKE CONCAT(CONCAT('%',#{batch}),'%')
        </if>
        <if test="creator !=null and creator !='' ">
            and a.CREATED_USER =#{creator}
        </if>
        <if test="pushObject !=null and pushObject !='' ">
            and a.PUSH_OBJECT =#{pushObject}
        </if>
        <if test="factoryFlagList !=null and factoryFlagList.size()>0 ">
            and a.FACTORY_FLAG in
            <foreach collection="factoryFlagList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryPushExamList" resultType="com.htks.domain.pushCourse.dto.PushExamEntityDto">
        select
        a.ID,
        a.BATCH,
        a.CATEGORY ,
        a.CATEGORY_ITEM ,
        a.PUSH_OBJECT ,
        a.EXAM_START_TIME,
        a.EXAM_END_TIME,
        ce.EMPLOYEE_NAME creator,
        ce.EMPLOYEE_NAME createName,
        TO_VARCHAR(a.UPDATED_TIME, 'YYYY-MM-DD HH24:MI:SS') AS pushTime,
        a.FACTORY_FLAG factoryFlag
        from COLLEGE_TRAIN_ASSESSED_EXAM a
        left join COMMON_EMPLOYEE ce ON  ce.EMPLOYEE_NUMBER = SUBSTR(a.CREATED_USER, 4)
        WHERE a.DELETED_FLAG=FALSE
        <if test="category !=null and category !='' ">
            and a.CATEGORY = #{category}
        </if>
        <if test="categoryItem!= null and categoryItem.size() >0">
            and a.CATEGORY_ITEM in
            <foreach collection="categoryItem" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="batch !=null and batch !='' ">
            and a.BATCH LIKE CONCAT(CONCAT('%',#{batch}),'%')
        </if>
        <if test="creator !=null and creator !='' ">
            and a.CREATED_USER =#{creator}
        </if>
        <if test="pushObject !=null and pushObject !='' ">
            and a.PUSH_OBJECT =#{pushObject}
        </if>
        <if test="factoryFlagList !=null and factoryFlagList.size()>0 ">
            and a.FACTORY_FLAG in
            <foreach collection="factoryFlagList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by a.UPDATED_TIME desc
        <if test="limit > 0 and offset !=null">
            LIMIT #{limit} OFFSET #{offset}
        </if>
    </select>

    <insert id="saveAssessedCourse">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_ASSESSED_COURSE_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_ASSESSED_COURSE
        (
        ID,
        BATCH,
        CATEGORY,
        PUSH_OBJECT,
        DEAD_TIME,CREATED_USER,UPDATED_USER,FACTORY_FLAG
        )
        values(
        #{id},
        #{batch},
        #{category},
        #{pushObject},
        #{deadTime},#{creator},#{creator},#{factoryFlag}
        )
    </insert>

    <update id="updatePushCourseTime">
        update COLLEGE_TRAIN_ASSESSED_COURSE set UPDATED_TIME=CURRENT_TIMESTAMP,CREATED_TIME=CURRENT_TIMESTAMP,DEAD_TIME=#{deadTime} WHERE ID=#{id}
    </update>

    <update id="updatePushExamTime">
        update COLLEGE_TRAIN_ASSESSED_EXAM set UPDATED_TIME=CURRENT_TIMESTAMP
        ,EXAM_START_TIME=#{examStartTime}
        ,EXAM_END_TIME=#{examEndTime}
        WHERE ID=#{id}
    </update>

    <insert id="saveAssessedCourseItem">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_ASSESSED_COURSE_ITEM_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_ASSESSED_COURSE_ITEM
        (
        ID,
        ASSESSED_ID,
        COURSE_ID,
        ASSESSED_COURSE_ID
        )
        values(
        #{id},
        #{assessedId},
        #{courseId},
        #{assessedCourseId}
        )
    </insert>
    <update id="updateAssessedCourseItemTime">
        update  COLLEGE_TRAIN_ASSESSED_COURSE_ITEM set
    </update>

    <select id="getStudentIdByBatch" resultType="hashmap">
        SELECT a.ID, b.PRACTICE_DEPARTMENT DEPARTMENT
        FROM COLLEGE_TRAIN_ASSESSED a
        LEFT JOIN COLLEGE_TRAIN_POST_AREA b ON a.PRACTICE_DEPARTMENT_AREA_ID = b.ID
        WHERE a.DELETED_FLAG = FALSE
          AND a.BATCH = #{batch}
          AND a.FACTORY_FLAG = #{factoryFlag}
    </select>

    <select id="getCourseIdByCategory" resultType="com.htks.domain.course.dto.CourseEntity">
        select *
        from COLLEGE_TRAIN_COURSE
        where DELETED_FLAG = false
          and CATEGORY = #{category}
          and FACTORY_FLAG = #{factoryFlag}
    </select>

    <select id="selectByCategoryAndBatch" resultType="com.htks.domain.pushCourse.dto.PushCourseEntityDto">
        select a.ID,
               a.BATCH,
               a.CATEGORY,
               a.PUSH_OBJECT,
               a.DEAD_TIME,
               a.CREATED_USER creator,
               a.UPDATED_TIME pushTime,
               a.FACTORY_FLAG
        from COLLEGE_TRAIN_ASSESSED_COURSE a
        WHERE a.DELETED_FLAG = FALSE
          and a.CATEGORY = #{category}
          and a.BATCH = #{batch}
          and a.FACTORY_FLAG = #{factoryFlag}
        order by id desc limit 1
    </select>

    <update id="updateAssessedCourseItem">
        update COLLEGE_TRAIN_ASSESSED_COURSE_ITEM
        set ASSESSED_COURSE_ID=#{newId}
        where ASSESSED_COURSE_ID = #{oldId}
    </update>

    <select id="isExists" resultType="java.lang.Integer">
        select count(1)
        from COLLEGE_TRAIN_ASSESSED_COURSE_ITEM a
        where a.COURSE_ID = #{courseId}
          and a.ASSESSED_ID = #{assessedId}
    </select>

    <select id="isExistsExam" resultType="java.lang.Integer">
        select count(1)
        from COLLEGE_TRAIN_ASSESSED_EXAM_ITEM a
        where a.ASSESSED_ID = #{assessedId}
          and a.EXAM_CATEGORY_ID = #{examCategoryId}
          and a.ASSESSED_EXAM_ID = #{assessedExamId}
    </select>

    <select id="getCategoryIdForExam" resultType="com.htks.domain.question.dto.Category">
        select *
        from COLLEGE_TRAIN_EXAM_CATEGORY
        WHERE CATEGORY_ITEM = #{categoryItem}
          and CATEGORY = #{category}
    </select>

    <insert id="saveAssessedExam">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_ASSESSED_EXAM_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_ASSESSED_EXAM
        (
        ID,
        BATCH,
        CATEGORY,
        CATEGORY_ITEM,
        PUSH_OBJECT,
        EXAM_START_TIME,EXAM_END_TIME,CREATED_USER,UPDATED_USER,FACTORY_FLAG
        )
        values(
        #{id},
        #{batch},
        #{category},
        #{categoryItem},
        #{pushObject},
        #{examStartTime},#{examEndTime},#{creator},#{creator},#{factoryFlag}
        )
    </insert>

    <insert id="saveAssessedExamItem">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_ASSESSED_EXAM_ITEM_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_ASSESSED_EXAM_ITEM
        (
        ID,
        ASSESSED_ID,
        EXAM_CATEGORY_ID,
        ASSESSED_EXAM_ID
        )
        values(
        #{id},
        #{assessedId},
        #{examCategoryId},
        #{assessedExamId}
        )
    </insert>

    <select id="inExistsExam" resultType="java.lang.Long">
        select ID
        from COLLEGE_TRAIN_ASSESSED_EXAM a
        where a.DELETED_FLAG = false
          and a.CATEGORY = #{category}
          and a.CATEGORY_ITEM = #{categoryItem}
          and BATCH = #{batch}
          and FACTORY_FLAG = #{factoryFlag}
    </select>

    <select id="getLimitTime" resultType="java.lang.Integer">
        select a.TEST_TIME
        from COLLEGE_TRAIN_EXAM_CATEGORY a
        where CATEGORY = #{category}
          and CATEGORY_ITEM = #{categoryItem}
    </select>

    <select id="selectStartPostExam" resultType="hashmap">
        SELECT a.id assessedId, c.ID postInfoId
        FROM COLLEGE_TRAIN_ASSESSED a
                 LEFT JOIN COLLEGE_TRAIN_POST_AREA b ON a.PRACTICE_DEPARTMENT_AREA_ID = b.ID
                 LEFT JOIN COLLEGE_TRAIN_POST_INFO c ON (b.PRACTICE_DEPARTMENT = c.PRACTICE_DEPARTMENT and b.FACTORY_FLAG=c.FACTORY_FLAG)
        WHERE a.DELETED_FLAG = FALSE
          AND POST_CERTIFICATE_START_TIME = current_date
          AND c.ID IS NOT NULL
          AND  NOT EXISTS (
                SELECT d.ASSESSED_ID ,d.POST_INFO_ID  FROM COLLEGE_TRAIN_POST_INFO_CARD d WHERE a.ID = d.ASSESSED_ID AND c.ID = d.POST_INFO_ID)
    </select>

    <insert id="insertPostInfoCard">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_POST_INFO_CARD_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_POST_INFO_CARD
        (
        ID,
        ASSESSED_ID,
        POST_INFO_ID
        )
        values(
        #{id},
        #{assessedId},
        #{postInfoId}
        )
    </insert>

    <select id="getAdminNo" resultType="com.htks.domain.pushCourse.dto.PushCourseEntityDto">
        select a.EMPLOYEE_NAME createName, a.EMPLOYEE_NO creator
        from COLLEGE_TRAIN_USER a
        where a.ROLE_ID LIKE CONCAT(CONCAT('%', '100'), '%')
          <if test="name!=null||name!=''">
              and a.EMPLOYEE_NAME LIKE CONCAT(CONCAT('%', #{name}), '%')
          </if>

    </select>

    <select id="getExam" resultType="string">
        SELECT DISTINCT a.CATEGORY
        FROM COLLEGE_TRAIN_EXAM_CATEGORY a
        where a.CATEGORY not IN ('BU实操')
    </select>
    <select id="getExamDetail" resultType="string">
        SELECT a.CATEGORY_ITEM
        FROM COLLEGE_TRAIN_EXAM_CATEGORY a
        WHERE a.CATEGORY = #{category} and a.CATEGORY_ITEM!='上岗证考核'
    </select>
    <select id="getCourse" resultType="string">
        SELECT a.TRAIN_CATEGORY
        FROM COLLEGE_TRAIN_CATEGORY a
    </select>

    <select id="getWeeklyPush" resultType="com.htks.domain.student.dto.WeeklyPushEntry">

        SELECT COUNT(a.ID) as sum,b.PRACTICE_DEPARTMENT_AREA_ID as area
        FROM COLLEGE_TRAIN_WEEKLY_REPORT a
            LEFT JOIN COLLEGE_TRAIN_ASSESSED b
        ON
            a.ASSESSED_ID = b.ID
        WHERE
           a.SCORE IS NULL
            GROUP BY b.PRACTICE_DEPARTMENT_AREA_ID
    </select>

    <select id="getWeeklyPushBydepartment" resultType="com.htks.domain.student.dto.WeeklyPushEntry">

        SELECT COUNT(a.ID) as sum,b.CHOOSE_DEPARTMENT_NAME as departmentName
        FROM COLLEGE_TRAIN_WEEKLY_REPORT a
            LEFT JOIN COLLEGE_TRAIN_ASSESSED b
        ON
            a.ASSESSED_ID = b.ID
        WHERE
            a.SCORE IS NULL
        GROUP BY b.CHOOSE_DEPARTMENT_NAME
    </select>

    <select id="getMasterEmployeeNo" resultType="com.htks.domain.student.dto.WeeklyPushEntry">
        select EMPLOYEE_NO as employeeNo ,EMPLOYEE_Name as employeeName from COLLEGE_TRAIN_USER where ZN_ID=#{zu_id}
    </select>



    <select id="getDepartment" resultType="com.htks.domain.student.dto.WeeklyPushEntry">
        select PRACTICE_DEPARTMENT as departmentName  from COLLEGE_TRAIN_POST_AREA where id=#{zu_id}
    </select>
    <select id="getSopDeadTime" resultType="string">
        SELECT ID
        from COLLEGE_TRAIN_ASSESSED
        WHERE SOP_UPLOAD_DEAD_TIME = #{date}
    </select>

    <select id="getSopSum" resultType="com.htks.domain.student.dto.WeeklyPushEntry">
        SELECT COUNT(a.ID) as sum, b.PRACTICE_DEPARTMENT_AREA_ID as area
        FROM COLLEGE_TRAIN_SOP_UPLOAD a
            LEFT JOIN COLLEGE_TRAIN_ASSESSED b
        ON
            a.ASSESSED_ID = b.ID
        WHERE a.EVALUATE_STATUS ='未评价' and a.ASSESSED_ID in
        <foreach collection="employeeNo" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
group by b.PRACTICE_DEPARTMENT_AREA_ID
    </select>
    <select id="getReservationEmployeeno" resultType="com.htks.domain.student.dto.ReservationWx">
        SELECT cta.EMPLOYEE_NAME,cta.EMPLOYEE_NO,tab.SCORE  FROM (SELECT COUNT(a.ASSESSED_ID) AS sum ,a.ASSESSED_ID,a.SCORE from COLLEGE_TRAIN_APPOINT_RECORD  a  WHERE  a.SCORE &lt;'80' AND a.TYPE='1' GROUP BY a.ASSESSED_ID ,a.SCORE) AS tab
        LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.ID =tab.ASSESSED_ID
        WHERE tab.sum='1'
    </select>

    <select id="getTime" resultType="string">
        select ENTRY_DATE from COLLEGE_TRAIN_ASSESSED order by ENTRY_DATE desc limit 1
    </select>

    <select id="getCollegeTrainAssessed" resultType="com.htks.domain.student.dto.Student">
        SELECT ENTRY_DATE AS date, EMPLOYEE_NO AS empployeeNo, EMPLOYEE_NAME AS empployeeName
        FROM COLLEGE_TRAIN_ASSESSED
where BATCH in('202407','202406') and FACTORY_FLAG='华天江苏'
    </select>

    <select id="getRole" resultType="string">
select EMPLOYEE_NO from COLLEGE_TRAIN_USER where ROLE_ID LIKE CONCAT(CONCAT('%', '100'), '%')
    </select>
</mapper>
