<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.htks.domain.common.repository.hana.CommonRepository">
    <insert id="addAttachment">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT POSTS_UPGRADE_ATTACHMENT_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into POSTS_UPGRADE_ATTACHMENT
        ("ID" ,
        "ATTACHMENT_PATH" ,
        "ATTACHMENT_TYPE" ,
        "ATTACHMENT_NAME" ,
        "ATTACHMENT_MEMO")
        values(
        #{id},
        #{attachmentPath},
        #{attachmentType},
        #{attachmentName},
        #{attachmentMemo}
        )
    </insert>

    <!--查询 - 参数下拉框选项数据-->
    <select id="getParameterDropDown" resultType="String">
        SELECT
        to_char(ID),
        PARAM_NAME  AS name,
        PARAM_TYPE,
        MEMO
        FROM LAB_PARAMETER
        WHERE DELETE_FLAG = 'N'
        <if test="null != paramType and ''!=paramType">
            AND PARAM_TYPE = #{paramType}
        </if>
    </select>

    <!--查询 - 员工基本信息-->
    <select id="getEmployeeBaseInfo" resultType="com.htks.domain.common.dto.EmployeeEntity">
        SELECT
        EMPLOYEE_NUMBER,
        EMPLOYEE_NAME,
        DEPARTMENT_ID,
        DEPARTMENT_NAME,
        EMAIL,
        WECHAR_ID AS userWeChatId,
        POSITION_GRADE,
        POSITION_NAME,
        FIRST_SUPERVISOR_NUMBER,
        GENDER,
        BUSINESS_UNIT_NAME ,
        BUSINESS_UNIT_ID ,
        CLASS_BAN_NAME ,
        CLASS_BAN_ID ,
        ZN_NAME ,
        ZN_ID
        FROM COMMON_EMPLOYEE
        WHERE EMPLOYEE_NUMBER = #{employeeNumber}
    </select>

    <!--查询 - 员工基本信息-->
    <select id="getEmployeeBaseInfoByWx" resultType="com.htks.domain.common.dto.EmployeeEntity">
        SELECT
        EMPLOYEE_NUMBER,
        EMPLOYEE_NAME,
        DEPARTMENT_ID,
        DEPARTMENT_NAME,
        WECHAR_ID AS userWeChatId
        FROM COMMON_EMPLOYEE
        WHERE WECHAR_ID = #{weChatId}
    </select>
    <select id="getDictionary" resultType="com.htks.domain.common.dto.UpgradeDictionaryEntity">
        SELECT
            ID,
            TYPE,
            CODE,
            NAME,
            NAME2
        FROM POSTS_UPGRADE_DICTIONARY
        WHERE TYPE = #{type}

    </select>
    <select id="getSkillDictionary" resultType="com.htks.domain.common.dto.UpgradeBuZnSkillEntity">
        SELECT
            ID,
            ZN_ID,
            SCORE_TYPE,
            SKILL_CODE,
            NAME,
            SERVICE,
            PARAM,
            GROUP_NAME
        FROM POSTS_UPGRADE_BU_ZN_SKILL
        WHERE 1=1
        <if test="znId != null and znId >0">
            AND ZN_ID = #{znId}
        </if>

    </select>

    <select id="getSkillGroupDictionary" resultType="com.htks.domain.common.dto.SkillGroupDicEntity">
        SELECT
        SCORE_TYPE,
        SKILL_CODE,
        GROUP_NAME skill_name
        FROM POSTS_UPGRADE_BU_ZN_SKILL
        WHERE 1=1
        <if test="znId != null and znId >0">
            AND ZN_ID = #{znId}
        </if>
        <if test="scoreType != null and scoreType !=''">
            AND SCORE_TYPE = #{scoreType}
        </if>
        GROUP BY SCORE_TYPE,SKILL_CODE,GROUP_NAME
    </select>

    <select id="getBusinessUnit" resultType="com.htks.domain.common.dto.UpgradeBusinessUnitEntity">
        SELECT ID,
               NAME
        From POSTS_UPGRADE_BUSINESS_UNIT

    </select>
    <select id="getDepartment" resultType="com.htks.domain.common.dto.UpgradeDepartmentEntity">
        SELECT ID,
               BUSINESS_UNIT_ID,
               NAME
        From POSTS_UPGRADE_DEPARTMENT
        WHERE 1=1
        <if test="businessUnitId != null and businessUnitId >0">
            AND BUSINESS_UNIT_ID = #{businessUnitId}
        </if>
    </select>
    <select id="getClassBan" resultType="com.htks.domain.common.dto.UpgradeClassBanEntity">
        SELECT ID,
               DEPARTMENT_ID,
               NAME
        From POSTS_UPGRADE_CLASS_BAN
        WHERE 1=1
        <if test="departmentId != null and departmentId >0">
            AND DEPARTMENT_ID = #{departmentId}
        </if>
    </select>
    <select id="getZn" resultType="com.htks.domain.common.dto.UpgradeZnEntity">
        SELECT ID,
                CLASS_BAN_ID,
               NAME
        From POSTS_UPGRADE_ZN
        WHERE 1=1
        <if test="classBanId != null and classBanId >0">
            AND CLASS_BAN_ID = #{classBanId}
        </if>
    </select>
    <select id="getRoleInfo" resultType="com.htks.domain.common.dto.CommonRoleEntity">
        SELECT ID,
        ROLE_NAME FROM COMMON_ROLE
        WHERE SYSTEM_IDENTIFIER = #{systemIdentifier}
    </select>
    <select id="getAttachmentByAnswerId" resultType="com.htks.domain.common.dto.AttachmentEntity">
        SELECT ATTACHMENT_PATH,
               ATTACHMENT_TYPE,
               ATTACHMENT_NAME,
               ATTACHMENT_MEMO FROM POSTS_UPGRADE_ATTACHMENT where EXAM_RECORD_ANSWER_ID = #{answerId}
    </select>

    <select id="getEmployeeNoByUserId" resultType="string">
        SELECT a.EMPLOYEE_NUMBER
        from COMMON_EMPLOYEE a
        where a.WECHAR_ID = #{userId} LIMIT 1
    </select>

    <select id="geyRoleByEmployeeNo" resultType="string">
        SELECT a.ROLE_ID FROM  COLLEGE_TRAIN_USER a WHERE a.EMPLOYEE_NO = #{employeeNO} AND a.ENABLED =TRUE
    </select>

    <select id="getSumByCollegeNo" resultType="integer">
        SELECT COUNT(ID) FROM  COLLEGE_TRAIN_ASSESSED a WHERE a.EMPLOYEE_NO = #{employeeNo} and ENABLED='开启'
    </select>
</mapper>
