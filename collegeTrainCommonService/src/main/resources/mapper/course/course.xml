<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.course.repository.hana.CourseRepository">

    <select id="queryCourseCount" resultType="java.lang.Integer">
        select
        count(1)
        from COLLEGE_TRAIN_COURSE a
        WHERE DELETED_FLAG=FALSE
        <if test="category !=null and category !='' ">
            and a.CATEGORY = #{category}
        </if>
        <if test="title !=null and title !='' ">
            and a.TITLE LIKE CONCAT(CONCAT('%',#{title}),'%')
        </if>
    </select>

    <select id="queryCourseList" resultType="com.htks.domain.course.dto.CourseEntityDto">
        select
        a.ID,
        a.TITLE,
        a.CATEGORY ,
        a.ATTACHMENT_ID ,
        a.CREATED_USER CREATOR,
        b.QUESTION_ID ,
        b.ATTACHMENT_MEMO,
        b.ATTACHMENT_NAME,
        b.ATTACHMENT_PATH,
        b.ATTACHMENT_TYPE,
        a.CREATED_USER creator,
        b.POST_CARD_ID,
        a.DEPARTMENT_NAME
        from COLLEGE_TRAIN_COURSE a
        left join COLLEGE_TRAIN_ATTACHMENT b on a.ATTACHMENT_ID=b.ID
        WHERE a.DELETED_FLAG=FALSE
        <if test="category !=null and category !='' ">
            and a.CATEGORY = #{category}
        </if>
        <if test="title !=null and title !='' ">
            and a.TITLE LIKE CONCAT(CONCAT('%',#{title}),'%')
        </if>
        <if test="limit > 0 and offset !=null">
            LIMIT #{limit} OFFSET #{offset}
        </if>
    </select>

    <insert id="saveCourse">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_COURSE_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_COURSE
        (
        ID,
        TITLE,
        CATEGORY,
        ATTACHMENT_ID,
        CREATED_USER,DEPARTMENT_NAME
        )
        values(
        #{id},
        #{title},
        #{category},
        #{attachmentId},
        #{creator},#{departmentName}
        )
    </insert>

    <update id="updateCourse">
        update COLLEGE_TRAIN_COURSE
        set TITLE=#{title}
        ,CATEGORY=#{category}
        ,ATTACHMENT_ID=#{attachmentId}
        ,CREATED_USER=#{creator}
        ,DEPARTMENT_NAME=#{departmentName}
        ,UPDATED_TIME=CURRENT_TIMESTAMP
        WHERE ID=#{id}
    </update>

    <insert id="insertAttachment" useGeneratedKeys="true" keyProperty="id">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id" keyColumn="ID">
            SELECT COLLEGE_TRAIN_ATTACHMENT_SEQ.NEXTVAL FROM DUMMY
        </selectKey>
        insert into COLLEGE_TRAIN_ATTACHMENT
        ("ID" ,
        "ATTACHMENT_PATH" ,
        "ATTACHMENT_TYPE" ,
        "ATTACHMENT_NAME" ,
        "ATTACHMENT_MEMO","POST_CARD_ID","QUESTION_ID")
        values(
        #{id},
        #{attachmentPath},
        #{attachmentType},
        #{attachmentName},
        #{attachmentMemo},#{postCardId},#{questionId}
        )
    </insert>

    <update id="deleteCourse">
        update COLLEGE_TRAIN_COURSE set DELETED_FLAG=true where ID=#{id}
    </update>

</mapper>