<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.htks.domain.ExamInformation.repository.hana.SatisfactionCountRepository">

    <!--查询大学生满意度情况-->
    <select id="getSatisfactionInformation" resultType="com.htks.domain.ExamInformation.dto.StatisticsInformationCount">

        SELECT a.THEORY_COURSE_DURATION ,a.THEORY_MAJOR,a.THEORY_COURSE_INTERACTION,a.MASTER_MAJOR
        ,a.MASTER_COURSE_ARRANGE ,a.MASTER_PRACTICAL_OPERATION ,a.MASTER_PATIENCE ,
        a.LEADER_QUESTION ,a.LEADER_MEET ,a.LEADER_CARE ,a.STUDY_THEORY ,a.STUDY_PRACTICAL_OPERATION ,a.STUDY_REPORT
        ,a.CREATED_TIME ,b.BATCH ,c.PRACTICE_DEPARTMENT as department ,
        b.EMPLOYEE_NAME ,b.EMPLOYEE_NO ,c.AREA as area
        FROM COLLEGE_TRAIN_SATISFACTION_DEGREE a LEFT JOIN COLLEGE_TRAIN_ASSESSED b ON b.ID =a.ASSESSED_ID LEFT JOIN
        COLLEGE_TRAIN_POST_AREA c
        ON c.ID =b.PRACTICE_DEPARTMENT_AREA_ID
        where
        <if test="startTime!=null and startTime!=''">
            a.CREATED_TIME between #{startTime}  and #{endTime} AND
        </if>
        a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            b.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            b.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            b.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            c.ID in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            b.PRACTICE_DEPARTMENT_AREA_ID in
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            and
        </if>
        1=1
        )
    </select>

    <!--获取IQ，HR1,HR2,工艺1，工艺2，BU1,BU2,工程结业考试-->
    <select id="getIqHrBu" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.EXAM_NAME as examName ,a.TOTAL_SCORE as masterCourseArrange,b.WEIGHT as
        masterMajor ,b.MAX_SCORE as theoryCourseInteraction,a.EVALUATE_REASON as masterPatience ,c.BATCH as batch ,c.EMPLOYEE_NO as
        employeeNo
        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo,c.ID as assessedId FROM
        COLLEGE_TRAIN_EXAM_RECORD a LEFT JOIN
        COLLEGE_TRAIN_CATEGORY b
        ON a.EXAM_CATEGORY_ID =b.CATEGORY_ID  LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN
        COLLEGE_TRAIN_POST_AREA d ON d.ID =c.PRACTICE_DEPARTMENT_AREA_ID
        LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE POST_INFO_ID IS NULL
        AND a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
       1=1 )
        and a.EXAM_CATEGORY_ID not in (8)
        <if test="factoryTypes.size() > 0">
            and c.FACTORY_FLAG in
            <foreach collection="factoryTypes" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        <if test="classNo != null and classNo != ''">
            and c.CLASS_NO like  concat('%',#{classNo}) || '%'
        </if>
    </select>
    <!--获取上岗证-->
    <select id="getPost" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT ('上岗证考核')                as examName,
               b.WEIGHT                 as masterMajor,
               b.MAX_SCORE              as theoryCourseInteraction,
               c.BATCH                  as batch,
               c.EMPLOYEE_NO            as employeeNo,
               c.EMPLOYEE_NAME          as employeeName,
               c.CHOOSE_DEPARTMENT_NAME as department,
               d.AREA                   as area
                ,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo
        from COLLEGE_TRAIN_POST_CARD f
                 LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '上岗证考核'
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID = f.ASSESSED_ID
                 LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID = c.PRACTICE_DEPARTMENT_AREA_ID
                 LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE
           f.ASSESSED_ID = #{id} AND f.POST_ID  IN (SELECT g.ID from COLLEGE_TRAIN_POST_INFO g WHERE g.AREA in (SELECT d.AREA FROM COLLEGE_TRAIN_POST_AREA d
                                     LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON d.ID = c.PRACTICE_DEPARTMENT_AREA_ID WHERE c.ID = #{id}) and  g.IS_CORE is not null)
    </select>
    <select id="getPostOther" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT ('上岗证考核')                as examName,
               b.WEIGHT                 as masterMajor,
               b.MAX_SCORE              as theoryCourseInteraction,
               c.BATCH                  as batch,
               c.EMPLOYEE_NO            as employeeNo,
               c.EMPLOYEE_NAME          as employeeName,
               c.CHOOSE_DEPARTMENT_NAME as department,
               d.AREA                   as area
                ,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo
        from COLLEGE_TRAIN_POST_CARD f
                 LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '上岗证考核'
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID = f.ASSESSED_ID
                 LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID = c.PRACTICE_DEPARTMENT_AREA_ID
                 LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE
            f.ASSESSED_ID = #{id} AND f.POST_ID not IN (SELECT g.ID from COLLEGE_TRAIN_POST_INFO g WHERE g.AREA in (SELECT d.AREA FROM COLLEGE_TRAIN_POST_AREA d
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON d.ID = c.PRACTICE_DEPARTMENT_AREA_ID WHERE c.ID = #{id}))
    </select>
    <!--获取工程实操-->
    <select id="getAppointRecord" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT b.TRAIN_CATEGORY_ITEM    as examName
             , a.SCORE                  as masterCourseArrange
             , a.ADDITIONAL_POINTS
             , a.TYPE
             , b.WEIGHT                 as masterMajor
             , b.MAX_SCORE              as theoryCourseInteraction
             , CONCAT(e.QUESTION , e.ANSWER)as masterPatience
             , c.BATCH                  as batch
             , c.EMPLOYEE_NO            as employeeNo
             , c.EMPLOYEE_NAME          as employeeName
             , c.CHOOSE_DEPARTMENT_NAME as department
             , d.AREA                   as area
             ,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '工程类实操考试'
                 LEFT JOIN COLLEGE_TRAIN_APPOINT_RECORD_DETAIL e ON
            e.APPOINT_RECORD_ID = b.ID
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID = a.ASSESSED_ID
                 LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
            = c.PRACTICE_DEPARTMENT_AREA_ID
                 LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY f ON f.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE a.TYPE = '1'
          AND a.ASSESSED_ID = #{id}
    </select>

    <!--异常答辩-->
    <select id="getExceptionRecord" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT b.TRAIN_CATEGORY_ITEM as examName,a.SCORE as masterCourseArrange,Round((a.SCORE*0.2+a.ADDITIONAL_POINTS),2 )as masterPracticalOperation

        ,b.WEIGHT as masterMajor ,b.MAX_SCORE as theoryCourseInteraction,e.QUESTION ,e.ANSWER,c.BATCH as batch
        ,c.EMPLOYEE_NO as employeeNo
        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo FROM
        COLLEGE_TRAIN_APPOINT_RECORD a LEFT JOIN
        COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='异常答辩' LEFT JOIN COLLEGE_TRAIN_APPOINT_RECORD_DETAIL e ON
        e.APPOINT_RECORD_ID =b.ID
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
        =c.PRACTICE_DEPARTMENT_AREA_ID
        LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY f ON f.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE a.TYPE ='2' AND a.APPOINT_STATUS= '1'  and  a.ASSESSED_ID IN
        (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1
        )
        <if test="factoryTypes.size() > 0">
            and c.FACTORY_FLAG in
            <foreach collection="factoryTypes" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        <if test="classNo != null and classNo != ''">
            and c.CLASS_NO like  concat('%',#{classNo}) || '%'
        </if>
    </select>

    <!--获取SOP-->
    <select id="getSOP" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT b.TRAIN_CATEGORY_ITEM as examName,e.SOP_NAME ,(e.QA_SCORE+e.EN_SCORE)/2 as
        masterCourseArrange,b.MAX_SCORE as theoryCourseInteraction ,b.WEIGHT as masterMajor ,c.BATCH
        as batch ,c.EMPLOYEE_NO as employeeNo
        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area ,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo FROM
        COLLEGE_TRAIN_SCORE e  LEFT JOIN
        COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='SOP制作'
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.EMPLOYEE_NO =e.EMPLOYEE_NUMBER
            LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
        =c.PRACTICE_DEPARTMENT_AREA_ID
        WHERE c.EMPLOYEE_NO in
        (SELECT COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1
        )
        <if test="factoryTypes.size() > 0">
            and c.FACTORY_FLAG in
            <foreach collection="factoryTypes" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        <if test="classNo != null and classNo != ''">
            and c.CLASS_NO like  concat('%',#{classNo}) || '%'
        </if>
    </select>

    <!--见面会评价-->
    <select id="getMeeting" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT b.TRAIN_CATEGORY_ITEM as examName,a.AVERAGE_SCORE as masterCourseArrange,b.MAX_SCORE as
        theoryCourseInteraction ,b.WEIGHT as masterMajor ,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo
        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo
       FROM COLLEGE_TRAIN_MEET a
        LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON
        b.TRAIN_CATEGORY_ITEM ='新生见面会'
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
        =c.PRACTICE_DEPARTMENT_AREA_ID
        LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE a.ASSESSED_ID in
        (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1
        )
        <if test="factoryTypes.size() > 0">
            and c.FACTORY_FLAG in
            <foreach collection="factoryTypes" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        <if test="classNo != null and classNo != ''">
            and c.CLASS_NO like  concat('%',#{classNo}) || '%'
        </if>
    </select>

    <!--每周周报-->
    <select id="getWeeklyReport" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT b.TRAIN_CATEGORY_ITEM as examName,a.SCORE as masterCourseArrange,b.WEIGHT as masterMajor,b.MAX_SCORE as
        theoryCourseInteraction,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo

        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo FROM
        COLLEGE_TRAIN_WEEKLY_REPORT a LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='每周周报'
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
        =c.PRACTICE_DEPARTMENT_AREA_ID
         LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE a.ASSESSED_ID =#{id}
        order by a.WEEKLY desc limit 10
    </select>
<select id="getWeeklyScore" resultType="string">
select SCORE from COLLEGE_TRAIN_WEEKLY_REPORT where ASSESSED_ID=(select  id from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO =#{id})
order by WEEKLY desc limit 10
</select>
    <!--组长评价-->
    <select id="getSubjectiveJudge" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT b.TRAIN_CATEGORY_ITEM as examName,a.SCORE as masterCourseArrange,b.MAX_SCORE as theoryCourseInteraction
        ,b.WEIGHT as masterMajor,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo ,
        e.PAPER_TYPE as masterPatience
        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo
        FROM COLLEGE_TRAIN_SUBJECTIVE_JUDGE a LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='培训组长主观评价'
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
        =c.PRACTICE_DEPARTMENT_AREA_ID
        LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE a.ASSESSED_ID in
        (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1
        )
        <if test="factoryTypes.size() > 0">
            and c.FACTORY_FLAG in
            <foreach collection="factoryTypes" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        <if test="classNo != null and classNo != ''">
            and c.CLASS_NO like  concat('%',#{classNo}) || '%'
        </if>
    </select>

    <!--什么是华天读后感-->
    <select id="getAfterRead" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT b.TRAIN_CATEGORY_ITEM as examName,a.SOP_NAME ,a.SCORE as masterCourseArrange,b.MAX_SCORE as
        theoryCourseInteraction ,b.WEIGHT as masterMajor,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo
        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area ,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo FROM
        COLLEGE_TRAIN_SOP_UPLOAD a LEFT JOIN
        COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='为什么是华天读后感'
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
        =c.PRACTICE_DEPARTMENT_AREA_ID
        LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
        WHERE a.TYPE ='2' AND a.ASSESSED_ID in
        (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1 and a.EVALUATE_STATUS='已评价'
        )
        <if test="factoryTypes.size() > 0">
            and c.FACTORY_FLAG in
            <foreach collection="factoryTypes" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        <if test="classNo != null and classNo != ''">
            and c.CLASS_NO like  concat('%',#{classNo}) || '%'
        </if>
    </select>

    <!--获取学生全部考试信息-->
    <select id="getAllExamInformation" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">

        <if test="list!= null and list.size() >0">
            <foreach collection="list" item="value" separator="," open="(" close=")">
            <if test='value== "HR"'>
                select a.EXAM_NAME as examName ,a.TOTAL_SCORE as masterCourseArrange,b.WEIGHT as masterMajor ,b.MAX_SCORE as theoryCourseInteraction,a.EVALUATE_REASON
                ,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo,
                e.PAPER_TYPE as masterPatience
                ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area FROM
                COLLEGE_TRAIN_EXAM_RECORD a LEFT JOIN
                COLLEGE_TRAIN_CATEGORY b
                ON a.EXAM_CATEGORY_ID =b.ID LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT
                JOIN
                COLLEGE_TRAIN_POST_AREA d ON d.ID =c.PRACTICE_DEPARTMENT_AREA_ID
                LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
                WHERE POST_INFO_ID IS NULL and a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM
                COLLEGE_TRAIN_ASSESSED WHERE
                <if test="batch!=null and batch!=''">
                    COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
                </if>
                <if test="employeeName!=null and employeeName!=''">
                    COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
                </if>
                <if test="employeeNo!=null and employeeNo!=''">
                    COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
                </if>
                <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
                    COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
                    <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                        #{value}
                    </foreach>
                    AND
                </if>
                <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
                    COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
                    <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                        concat('%',#{value}) || '%'
                    </foreach>
                    and
                </if>
                1=1
                )
            </if>
        <if test='value== "异常答辩"'>
            SELECT b.TRAIN_CATEGORY_ITEM as examName,a.SCORE as masterCourseArrange,Round((a.SCORE*0.1+a.ADDITIONAL_POINTS),2 ) as masterPracticalOperation,a.TYPE
            ,b.WEIGHT as masterMajor ,b.MAX_SCORE as theoryCourseInteraction,e.QUESTION ,e.ANSWER,c.BATCH as batch
            ,c.EMPLOYEE_NO as employeeNo,
            e.PAPER_TYPE as masterPatience
            ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area FROM
            COLLEGE_TRAIN_APPOINT_RECORD a LEFT JOIN
            COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='异常答辩' LEFT JOIN COLLEGE_TRAIN_APPOINT_RECORD_DETAIL e ON
            e.APPOINT_RECORD_ID =b.ID
            LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
            =c.PRACTICE_DEPARTMENT_AREA_ID
            LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
            WHERE a.TYPE ='2' and a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
            <if test="batch!=null and batch!=''">
                COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
            </if>
            <if test="employeeName!=null and employeeName!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
            </if>
            <if test="employeeNo!=null and employeeNo!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
            </if>
            <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
                COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
                <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
                AND
            </if>
            <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
                COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
                <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                    concat('%',#{value}) || '%'
                </foreach>
                and
            </if>
            1=1
            )
        </if>
        <if test='value== "获取SOP"'>
            select b.TRAIN_CATEGORY_ITEM as examName,a.SOP_NAME ,a.EVALUATE_REASON as EVALUATE_REASON,a.SCORE as
            masterCourseArrange,b.MAX_SCORE as theoryCourseInteraction ,b.WEIGHT as masterMajor,e.PAPER_TYPE as masterPatience
            ,a.EVALUATE_REASON,c.BATCH
            as batch ,c.EMPLOYEE_NO as employeeNo
            ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area FROM
            COLLEGE_TRAIN_SOP_UPLOAD a LEFT JOIN
            COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='SOP制作'
            LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
            =c.PRACTICE_DEPARTMENT_AREA_ID
            LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
            WHERE a.TYPE ='1' AND a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
            <if test="batch!=null and batch!=''">
                COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
            </if>
            <if test="employeeName!=null and employeeName!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
            </if>
            <if test="employeeNo!=null and employeeNo!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
            </if>
            <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
                COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
                <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
                AND
            </if>
            <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
                COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
                <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                    concat('%',#{value}) || '%'
                </foreach>
                and
            </if>
            1=1
            )
        </if>
        <if test='value== "组长评价"'>
            select b.TRAIN_CATEGORY_ITEM as examName,a.SCORE as masterCourseArrange,b.MAX_SCORE as
            theoryCourseInteraction ,b.WEIGHT
            as masterMajor,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo,e.PAPER_TYPE as masterPatience
            ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area
            FROM COLLEGE_TRAIN_SUBJECTIVE_JUDGE a LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM
            ='培训组长主观评价'
            LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
            =c.PRACTICE_DEPARTMENT_AREA_ID
            LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
            WHERE a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
            <if test="batch!=null and batch!=''">
                COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
            </if>
            <if test="employeeName!=null and employeeName!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
            </if>
            <if test="employeeNo!=null and employeeNo!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
            </if>
            <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
                COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
                <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
                AND
            </if>
            <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
                COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
                <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                    concat('%',#{value}) || '%'
                </foreach>
                and
            </if>
            1=1
            )
        </if>
        <if test='value== "《为什么是华天》读后感"'>
            select b.TRAIN_CATEGORY_ITEM as examName,a.SOP_NAME ,a.SCORE as masterCourseArrange,b.MAX_SCORE as
            theoryCourseInteraction ,b.WEIGHT as masterMajor,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo,e.PAPER_TYPE as masterPatience
            ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area FROM
            COLLEGE_TRAIN_SOP_UPLOAD a
            LEFT JOIN
            COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='为什么是华天读后感'
            LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
            =c.PRACTICE_DEPARTMENT_AREA_ID
            LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY e ON e.CATEGORY_ITEM =b.TRAIN_CATEGORY_ITEM
            WHERE a.TYPE ='2'
            and a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
            <if test="batch!=null and batch!=''">
                COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
            </if>
            <if test="employeeName!=null and employeeName!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
            </if>
            <if test="employeeNo!=null and employeeNo!=''">
                COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
            </if>
            <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
                COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
                <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
                AND
            </if>
            <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
                COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
                <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                    concat('%',#{value}) || '%'
                </foreach>
                and
            </if>
            1=1
            )

        </if>
            </foreach>
        </if>
    </select>
    <!--<if test='value== "每周周报"'>
        select b.TRAIN_CATEGORY_ITEM as examName,a.SCORE as masterCourseArrange,b.WEIGHT as masterMajor,b.MAX_SCORE
        as
        theoryCourseInteraction,c.BATCH as batch ,c.EMPLOYEE_NO as employeeNo
        ,c.EMPLOYEE_NAME as employeeName,c.CHOOSE_DEPARTMENT_NAME as department,d.AREA as area FROM
        COLLEGE_TRAIN_WEEKLY_REPORT a LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM ='每周周报'
        LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID =a.ASSESSED_ID LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
        =c.PRACTICE_DEPARTMENT_AREA_ID
        WHERE a.ASSESSED_ID in (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1
        )
    </if>-->
    <!--获取AssessedId-->
    <select id="getAssessedId" resultType="long">
        (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1
        )
    </select>


    <select id="getAssessedId2" resultType="long">
        (SELECT COLLEGE_TRAIN_ASSESSED.ID FROM COLLEGE_TRAIN_ASSESSED WHERE
        <if test="batch!=null and batch!=''">
            COLLEGE_TRAIN_ASSESSED.BATCH like concat('%',#{batch}) || '%' AND
        </if>
        <if test="employeeName!=null and employeeName!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NAME like concat('%',#{employeeName}) || '%' AND
        </if>
        <if test="employeeNo!=null and employeeNo!=''">
            COLLEGE_TRAIN_ASSESSED.EMPLOYEE_NO like concat('%',#{employeeNo}) || '%' AND
        </if>
        <if test="chooseDepartmentName!= null and chooseDepartmentName.size() >0">
            COLLEGE_TRAIN_ASSESSED.CHOOSE_DEPARTMENT_NAME in
            <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
            AND
        </if>
        <if test="practiceDepartmentAreaId!= null and practiceDepartmentAreaId.size() >0">
            COLLEGE_TRAIN_ASSESSED.PRACTICE_DEPARTMENT_AREA_ID LIKE
            <foreach collection="practiceDepartmentAreaId" item="value" separator="," open="(" close=")">
                concat('%',#{value}) || '%'
            </foreach>
            and
        </if>
        1=1
        <if test="factoryTypes != null and factoryTypes.size > 0">
            and COLLEGE_TRAIN_ASSESSED.FACTORY_FLAG in
            <foreach collection="factoryTypes" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        <if test="classNo != null and classNo != ''">
            and COLLEGE_TRAIN_ASSESSED.CLASS_NO like  concat('%',#{classNo}) || '%'
        </if>
        )
    </select>

    <!--获取附加分-->
    <select id="getAdditionalPoints" resultType="double">
        SELECT a.ADDITIONAL_POINTS
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '工程实际操作'
                 LEFT JOIN COLLEGE_TRAIN_APPOINT_RECORD_DETAIL e ON
            e.APPOINT_RECORD_ID = b.ID
                 LEFT JOIN COLLEGE_TRAIN_ASSESSED c ON c.ID = a.ASSESSED_ID
                 LEFT JOIN COLLEGE_TRAIN_POST_AREA d ON d.ID
            = c.PRACTICE_DEPARTMENT_AREA_ID
        WHERE a.TYPE = '1'
          AND a.ASSESSED_ID = #{id}
        order BY a.CREATED_TIME DESC limit 1
    </select>

    <!--获取工程类问题-->
    <select id="getQuestion" resultType="string">
        SELECT a.QUESTION
        FROM COLLEGE_TRAIN_APPOINT_RECORD_DETAIL a
                 LEFT JOIN COLLEGE_TRAIN_APPOINT_RECORD b ON b.ID = a.APPOINT_RECORD_ID
        WHERE b.TYPE = '1'
          AND b.ASSESSED_ID = #{id}
        order BY a.CREATED_TIME DESC limit 1
    </select>
    <!--获取工程类答案-->
    <select id="getAnswer" resultType="string">
        SELECT a.ANSWER
        FROM COLLEGE_TRAIN_APPOINT_RECORD_DETAIL a
                 LEFT JOIN COLLEGE_TRAIN_APPOINT_RECORD b ON b.ID = a.APPOINT_RECORD_ID
        WHERE b.TYPE = '1'
          AND b.ASSESSED_ID = #{id}
        order BY a.CREATED_TIME DESC limit 1
    </select>


    <select id="getIQRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.TOTAL_SCORE as masterCourseArrange, b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_EXAM_RECORD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b
             ON b.TRAIN_CATEGORY_ITEM='IQ测试'
        where a.ASSESSED_ID = #{id}
          and a.EXAM_CATEGORY_ID ='1' or a.EXAM_CATEGORY_ID ='10'
    </select>

    <select id="getMeetingRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.AVERAGE_SCORE as masterCourseArrange, b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_MEET a
                 LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON
            b.TRAIN_CATEGORY_ITEM = '新生见面会'
        WHERE a.ASSESSED_ID = #{id}
    </select>

    <select id="getCraft" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.TOTAL_SCORE as masterCourseArrange, b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_EXAM_RECORD a
                 left join
             COLLEGE_TRAIN_EXAM_CATEGORY c
             on a.EXAM_CATEGORY_ID=c.id
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b on
           b.TRAIN_CATEGORY_ITEM=c.CATEGORY_ITEM
        where a.ASSESSED_ID = #{id}
          and (a.EXAM_CATEGORY_ID ='4' or a.EXAM_CATEGORY_ID ='5')
    </select>

    <select id="getHR" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.TOTAL_SCORE as masterCourseArrange, b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_EXAM_RECORD a
                 left join
             COLLEGE_TRAIN_EXAM_CATEGORY c
             on a.EXAM_CATEGORY_ID=c.id
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b on
                 b.TRAIN_CATEGORY_ITEM=c.CATEGORY_ITEM
        where a.ASSESSED_ID = #{id}
          and (a.EXAM_CATEGORY_ID ='2' or a.EXAM_CATEGORY_ID ='3')
    </select>

    <select id="getBU" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.TOTAL_SCORE as masterCourseArrange, b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_EXAM_RECORD a
                 left join
             COLLEGE_TRAIN_EXAM_CATEGORY c
             on a.EXAM_CATEGORY_ID=c.id
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b on
                 b.TRAIN_CATEGORY_ITEM=c.CATEGORY_ITEM
        where a.ASSESSED_ID = #{id}
          and a.EXAM_CATEGORY_ID in(6,7)
    </select>

    <select id="getPostRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.TOTAL_SCORE as masterCourseArrange
             , b.WEIGHT      as masterMajor
        FROM COLLEGE_TRAIN_EXAM_RECORD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b
             ON b.TRAIN_CATEGORY_ITEM = '上岗证考核'
        WHERE a.CREATED_TIME IN
              (SELECT MAX(a.CREATED_TIME)
               FROM COLLEGE_TRAIN_EXAM_RECORD a
               WHERE POST_INFO_ID IS NOT NULL
               GROUP BY a.ASSESSED_ID, a.POST_INFO_ID)
          AND a.ASSESSED_ID = #{id}
    </select>

    <select id="getProject" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.TOTAL_SCORE as masterCourseArrange, b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_EXAM_RECORD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b
             ON b.TRAIN_CATEGORY_ITEM='工程结业理论考试'
        where a.ASSESSED_ID = #{id}
          and a.EXAM_CATEGORY_ID ='9'
    </select>

    <select id="getAppointRecordRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.SCORE  as masterCourseArrange
             , b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '工程类实操考试'
        WHERE a.TYPE = '1'
          AND a.ASSESSED_ID = #{id}
    </select>

    <select id="getSOPRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT(a.QA_SCORE+a.EN_SCORE)/2   as masterCourseArrange
             , b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_SCORE a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = 'SOP制作'
        WHERE
         a.EMPLOYEE_NUMBER =(select EMPLOYEE_NO from  COLLEGE_TRAIN_ASSESSED  where ID= #{id})
    </select>

    <select id="getExceptionRecordRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT ROUND((a.SCORE * 0.2 + a.ADDITIONAL_POINTS), 2) as masterCourseArrange
             , b.WEIGHT                              as masterMajor
        FROM COLLEGE_TRAIN_APPOINT_RECORD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '异常答辩'
        WHERE a.TYPE = '2'
          AND a.ASSESSED_ID = #{id}
    </select>

    <select id="getWeeklyReportRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.SCORE  as masterCourseArrange
             , b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_WEEKLY_REPORT a
                 LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '每周周报'
        WHERE a.ASSESSED_ID = #{id}
        order by a.WEEKLY desc limit 10
    </select>

    <select id="getSubjectiveJudgeRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.SCORE  as masterCourseArrange
             , b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_SUBJECTIVE_JUDGE a
                 LEFT JOIN COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '培训组长主观评价'
        WHERE a.ASSESSED_ID = #{id}
    </select>

    <select id="getAfterReadRanking" resultType="com.htks.domain.ExamInformation.dto.ExamInformation">
        SELECT a.SCORE  as masterCourseArrange
             , b.WEIGHT as masterMajor
        FROM COLLEGE_TRAIN_SOP_UPLOAD a
                 LEFT JOIN
             COLLEGE_TRAIN_CATEGORY b ON b.TRAIN_CATEGORY_ITEM = '为什么是华天读后感'
        WHERE a.TYPE = '2'
          AND a.ASSESSED_ID = #{id}
    </select>

    <select id="getExamAllCollegeInformation"
            resultType="com.htks.domain.ExamInformation.dto.ExamAllCollegeInformation">
        select c.BATCH                  as batch
             , c.EMPLOYEE_NO            as employeeNo
             , c.EMPLOYEE_NAME          as employeeName
             , c.CHOOSE_DEPARTMENT_NAME as department
             , d.AREA                   as area
             ,c.FACTORY_FLAG as factoryType,c.CLASS_NO as classNo
        FROM COLLEGE_TRAIN_ASSESSED c
                 LEFT JOIN COLLEGE_TRAIN_POST_AREA d
                           ON d.ID = c.PRACTICE_DEPARTMENT_AREA_ID
        WHERE c.ID = #{id}
    </select>
<!--获取微信号-->
    <select id="getWechatId" resultType="com.htks.domain.common.dto.EmployeeEntity">
        SELECT a.WECHAR_ID as userWeChatId,EMPLOYEE_NAME as employeeName
        FROM COMMON_EMPLOYEE a
        WHERE a.EMPLOYEE_NUMBER = #{employeeNo}
    </select>
<select id="getAddScore" resultType="com.htks.domain.common.dto.PostAddScore">
 select  score as score,TYPE as type, EMPLOYEE_NO as employeeNo from    COLLEGE_TRAIN_POST_ADD_SCORE where EMPLOYEE_NO=#{employeeNo}
</select>
<select id="getEmployeeNoByAid" resultType="string">
  select  EMPLOYEE_NO from COLLEGE_TRAIN_ASSESSED where ID=#{id}
</select>
    <!--获取学生全部排名信息-->
    <select id="getAllStudentRanking">

    </select>
    <select id="getSubjectScore" resultType="java.lang.String">
        select SCORE from COLLEGE_TRAIN_SUBJECTIVE_JUDGE where ASSESSED_ID = #{id}
    </select>
    <select id="getExamScore" resultType="java.lang.String">
        select TOTAL_SCORE from COLLEGE_TRAIN_EXAM_RECORD where ASSESSED_ID = #{id}
    </select>

    <select id="getAreaId" resultType="string">
        SELECT d.id FROM
        COLLEGE_TRAIN_POST_AREA d LEFT JOIN COLLEGE_TRAIN_DEPARTMENT f ON d.PRACTICE_DEPARTMENT = f.DEPARTMENT AND
        f.FACTORY_FLAG = d.FACTORY_FLAG
        WHERE f.ID in
        <foreach collection="chooseDepartmentName" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </select>
</mapper>