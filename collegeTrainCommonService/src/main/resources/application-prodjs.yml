server:
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 64
      min-spare: 32
  undertow:
    buffer-size: 1024  # 每个区分配的buffer数量
    direct-buffers: true   # 是否分配的直接内存(NIO直接分配的堆外内存)
    threads:
      io: 8  #IO线程数
      worker: 256  #阻塞任务线程池
    accesslog: #Undertow容器日志配置
      dir: /home/<USER>/undertow/access/
      enabled: true
      pattern: "%h %l %u %t \"%r\" %s %b %D \"%{i,Referer}\" \"%{i,User-Agent}\""
      prefix: labMis_access
      suffix: log
      rotate: true
  port: 9088
  servlet:
    context-path: /collegeTrainBack
  ssl:
    enabled: false #是否启用SSL
    key-store: classpath:htks-hub.htkjks.com.jks  #证书路径
    key-store-type: JKS
    #key-alias: htks-hub.htkjks.com
    key-store-password: tT49N207  #配置密码

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hana:
      first:
        driver-class-name: com.sap.db.jdbc.Driver
        jdbcurl: **************************************************
        username: ERP_PROD
        password: Sap12345prod
        pool-name: HTKS-HikariCP-Hana-First     # 连接池名称
        idle-timeout: 30000        # 空闲连接存活最大时间，默认600000（10分钟）
        max-lifetime: 1800000       # 池中连接的最长生命周期，默认1800000即30分钟
        maximum-pool-size: 2       # 最大连接数
        connection-timeout: 30000   # 数据库连接超时时间,默认30秒

# xx-job 配置
xxl:
  job:
    admin:
      # 注册定时任务服务地址（测试环境地址9001，正式环境地址9000）
      addresses: http://**************:9001/xxl-job-admin
    # 执行器TOKEN
    accessToken: default_token
    executor:
      # 执行器AppName（与应用名一致）
      appname: collegeTrainBack
      # 执行器注册地址（应用部署地址（IP:PORT）） 不填自动获取
      address:
      # 执行器ip 不填自动获取
      ip:
      # 配置和项目相同的端口
      port: ${server.port}
      # 日志文件保存地址
      logpath: /data/applogs/xxl-job/jobhandler
      # 日志保存天数
      logretentiondays: 30


#企业微信配置
wechat:
  collegeTrain: #华天学堂
    corpId: ww329350be2823ca09
    #测试环境
    #secret: xTiI5qL5TdrRMUZuYSFop9s_PWzDI7BHGwcGaTFZ04g
    #agentId: 1000018
    #正式环境
    secret: 3DzKigsmF8xTUemDUQugPxlqQQfpGN6YQ3JG5Wz13WM
    agentId: 1000028