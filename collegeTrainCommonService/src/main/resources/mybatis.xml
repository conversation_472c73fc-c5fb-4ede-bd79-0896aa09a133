<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

  <settings>
    <!--启用数据中列自动映射到Java类中驼峰命名的属性-->
    <setting name="mapUnderscoreToCamelCase" value="true"/>
    <!--执行类型REUSE:执行器可能重复使用prepared statements 语句-->
    <setting name="defaultExecutorType" value="REUSE"/>
    <!--设置JDBC类型为空时,指定值为NULL-->
    <setting name="jdbcTypeForNull" value="NULL"/>
  </settings>

</configuration>
