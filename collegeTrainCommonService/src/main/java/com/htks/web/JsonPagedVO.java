package com.htks.web;

import cn.hutool.core.util.ObjectUtil;
import com.htks.common.external.wx.ResultCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

import static com.google.common.base.MoreObjects.toStringHelper;
import static java.util.Collections.emptyList;

/**
 * 返回前端的分页VO
 *
 * <AUTHOR>
 * @date 2020/12/01.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "统一响应结果")
public final class JsonPagedVO<T> extends AbstractVO {

  private static final long serialVersionUID = -7625854546900163242L;

  @ApiModelProperty(value = "状态码", notes = "默认1000是成功")
  private int code;
  @ApiModelProperty(value = "响应信息", notes = "来说明响应情况")
  private String message;
  @ApiModelProperty(value = "响应的具体数据")
  private T data;

  /**
   * 数据总条数
   */
  @ApiModelProperty(value = "数据总条数")
  private Integer total;


  public JsonPagedVO(T data,int total) {
    this( ResultCode.SUCCESS, data, total);
  }

  public static JsonPagedVO success(Object data,int total) {
    JsonPagedVO jsonPagedVO = new JsonPagedVO();
    jsonPagedVO.setCode(ResultCode.SUCCESS.getCode());
    jsonPagedVO.setMessage(ResultCode.SUCCESS.getMsg());
    jsonPagedVO.setData(data);
    jsonPagedVO.setTotal(total);
    return jsonPagedVO;
  }

  public JsonPagedVO(Integer code, String msg, T data) {
    this.code = code;
    this.message = msg;
    this.data = data;
  }

  public JsonPagedVO(ResultCode resultCode, T data, int recordCount) {
    this.code = resultCode.getCode();
    this.message = resultCode.getMsg();
    this.data = data;
    this.total = recordCount;
  }

}
