package com.htks.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 返回前端的Abstract类VO
 *
 * <AUTHOR>
 * @date 2020/12/01.
 */
@Getter
@Setter
@ApiModel(value = "AbstractVO")
public abstract class AbstractVO implements Serializable {

  private static final long serialVersionUID = 5836802944760616343L;

}
