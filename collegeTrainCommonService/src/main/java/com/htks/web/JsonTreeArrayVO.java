package com.htks.web;

import static com.google.common.base.MoreObjects.toStringHelper;
import static java.util.Collections.emptyList;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * 返回前端的树形集合VO
 *
 * <AUTHOR>
 * @date 2020/12/01.
 */
@ApiModel(value = "JsonTreeArrayVO")
public final class JsonTreeArrayVO<T extends AbstractVO> extends AbstractVO {

  private static final long serialVersionUID = -4876690465533010785L;

  /**
   * 数据集合
   */
  @ApiModelProperty(value = "数据集合")
  private List<T> records;

  public JsonTreeArrayVO() {
    this.records = emptyList();
  }

  public JsonTreeArrayVO(List<T> records) {
    this.records = records;
  }

  public List<T> getRecords() {
    return records;
  }

  public void setRecords(List<T> records) {
    this.records = records;
  }

  @Override
  public String toString() {
    return toStringHelper(this).
        add("records", records).
        toString();
  }
}
