package com.htks.web;

import static com.htks.common.utils.DateUtils.formatDateTime;
import static org.apache.commons.lang3.StringUtils.EMPTY;

import com.google.common.io.Closer;
import com.google.common.io.Resources;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;

/**
 * XX
 *
 * <AUTHOR>
 * @date 2022/7/12.
 */
public abstract class AbstractExportExcel {

  private static final Logger LOGGER = LoggerFactory.getLogger(AbstractExportExcel.class);

  private static final String EXCEL_XLSX = ".xlsx";
  private static final String ACTION_1 = "INVALID FORMAT:{}";
  private static final String ACTION_2 = "THE MODEL XLS CAN NOT FIND! ERROR:{}";
  private static final String ACTION_3 = "THE JAVA IO ERROR:{}";

  /**
   * 准备导入模版
   *
   * @param name 文件名称
   * @param response response
   */
  public void write(HttpServletResponse response, String name) {
    final Closer closer = Closer.create();
    try (InputStream fs = Resources.asByteSource(Resources.getResource(getExcelUri()))
        .openStream()) {
      closer.register(fs);
      final XSSFWorkbook workBook = new XSSFWorkbook(OPCPackage.open(fs));
      buildExcel(workBook);
      response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder
          .encode(name + formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"), "UTF-8") + EXCEL_XLSX);
      workBook.write(response.getOutputStream());
    } catch (InvalidFormatException e) {
      LOGGER.error(ACTION_1, e);
    } catch (FileNotFoundException e1) {
      LOGGER.error(ACTION_2, e1);
    } catch (IOException e1) {
      LOGGER.error(ACTION_3, e1);
    } finally {
      try {
        closer.close();
      } catch (IOException e) {
        LOGGER.error(ACTION_3, e);
      }
    }
  }

  /**
   * 准备导入模版
   *
   * @param filePath 文件存储路径
   */
  public void write( String filePath) {
    final Closer closer = Closer.create();
    OutputStream stream = null;
    try (InputStream fs = Resources.asByteSource(Resources.getResource(getExcelUri()))
            .openStream()) {
      stream = new FileOutputStream(filePath);
      closer.register(fs);
      final XSSFWorkbook workBook = new XSSFWorkbook(OPCPackage.open(fs));
      buildExcel(workBook);
      workBook.write(stream);
    } catch (InvalidFormatException e) {
      LOGGER.error(ACTION_1, e);
    } catch (FileNotFoundException e1) {
      LOGGER.error(ACTION_2, e1);
    } catch (IOException e1) {
      LOGGER.error(ACTION_3, e1);
    } finally {
      try {
        closer.close();
      } catch (IOException e) {
        LOGGER.error(ACTION_3, e);
      }
    }
  }

  protected abstract String getExcelUri();

  protected abstract void buildExcel(XSSFWorkbook workBook);

  protected void setSheetStyle(XSSFSheet sheet, int rowIndex, int cellIndex, XSSFCellStyle style) {
    XSSFRow row = sheet.getRow(rowIndex);
    if (row == null) {
      row = sheet.createRow(rowIndex);
    }
    XSSFCell cell = row.getCell(cellIndex);
    if (cell == null) {
      cell = row.createCell(cellIndex);
    }
    cell.setCellStyle(style);
  }

  protected void setSheetValue(XSSFSheet sheet, int rowIndex, int cellIndex, String value) {
    XSSFRow row = sheet.getRow(rowIndex);
    if (row == null) {
      row = sheet.createRow(rowIndex);
    }
    XSSFCell cell = row.getCell(cellIndex);
    if (cell == null) {
      cell = row.createCell(cellIndex);
    }
    cell.setCellType(CellType.STRING);
    cell.setCellValue(StringUtils.isBlank(value) ? EMPTY : value);
  }

  protected void setSheetValue(XSSFSheet sheet, int rowIndex, int cellIndex, String value, XSSFCellStyle style) {
    XSSFRow row = sheet.getRow(rowIndex);
    if (row == null) {
      row = sheet.createRow(rowIndex);
    }
    XSSFCell cell = row.getCell(cellIndex);
    if (cell == null) {
      cell = row.createCell(cellIndex);
    }
    cell.setCellType(CellType.STRING);
    cell.setCellValue(value);
    cell.setCellStyle(style);
  }

  protected void setSheetValue(XSSFSheet sheet, int rowIndex, int cellIndex, int value, XSSFCellStyle style) {
    XSSFRow row = sheet.getRow(rowIndex);
    if (row == null) {
      row = sheet.createRow(rowIndex);
    }
    XSSFCell cell = row.getCell(cellIndex);
    if (cell == null) {
      cell = row.createCell(cellIndex);
    }

    if (value > 0) {
      cell.setCellType(CellType.NUMERIC);
      cell.setCellValue(value);
    }

    cell.setCellStyle(style);
  }


  protected void setSheetValue(XSSFSheet sheet, int rowIndex, int cellIndex, XSSFCellStyle style) {
    XSSFRow row = sheet.getRow(rowIndex);
    if (row == null) {
      row = sheet.createRow(rowIndex);
    }
    XSSFCell cell = row.getCell(cellIndex);
    if (cell == null) {
      cell = row.createCell(cellIndex);
    }

    cell.setCellStyle(style);
  }


  protected void setSheetValue(XSSFSheet sheet, int rowIndex, int cellIndex, XSSFRichTextString value) {
    XSSFRow row = sheet.getRow(rowIndex);
    if (row == null) {
      row = sheet.createRow(rowIndex);
    }
    XSSFCell cell = row.getCell(cellIndex);
    if (cell == null) {
      cell = row.createCell(cellIndex);
    }
    cell.setCellType(CellType.STRING);
    cell.setCellValue(value);
  }

  protected String getSheetValue(XSSFSheet sheet, int rowIndex, int cellIndex) {
    if (sheet.getRow(rowIndex) == null) {
      return null;
    } else {
      if (sheet.getRow(rowIndex).getCell(cellIndex) == null) {
        return null;
      } else {
        return sheet.getRow(rowIndex).getCell(cellIndex).getStringCellValue();
      }
    }
  }

  protected XSSFCellStyle getCellStyle(XSSFSheet sheet, int rowIndex, int cellIndex) {
    if (sheet.getRow(rowIndex) == null) {
      return null;
    } else {
      if (sheet.getRow(rowIndex).getCell(cellIndex) == null) {
        return null;
      } else {
        return sheet.getRow(rowIndex).getCell(cellIndex).getCellStyle();
      }
    }
  }


  protected XSSFCellStyle createBgStyle(XSSFWorkbook workbook, XSSFCellStyle cellStyle, int red, int green, int blue) {

    final XSSFCellStyle styleBg = workbook.createCellStyle();
    //设置填充颜色
    styleBg.setFillForegroundColor(new XSSFColor(new java.awt.Color(red, green, blue), new DefaultIndexedColorMap()));
    styleBg.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    styleBg.setAlignment(cellStyle.getAlignment());
    styleBg.setBorderTop(cellStyle.getBorderTop());
    styleBg.setBorderRight(cellStyle.getBorderRight());
    styleBg.setBottomBorderColor(cellStyle.getBottomBorderColor());
    styleBg.setLeftBorderColor(cellStyle.getLeftBorderColor());

    return styleBg;
  }

  protected void setCellBgStyle(XSSFCellStyle style, XSSFColor bgColor) {
    //设置填充颜色
    style.setFillForegroundColor(bgColor);
    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

  }


  protected void setCellBgStyle(XSSFSheet sheet, XSSFCellStyle style, int rowIndex, int startCellIndex, int endCellIndex) {
    for (int cellIndex = startCellIndex; cellIndex < endCellIndex; cellIndex++) {
      setSheetStyle(sheet, rowIndex, cellIndex, style);
    }
  }

  /**
   * 单元格合并 参数：起始行号，终止行号， 起始列号，终止列号
   *
   * @param sheet 工作表
   * @param firstRow 起始行号
   * @param lastRow 终止行号
   * @param firstCol 起始列号
   * @param lastCol 终止列号
   */
  protected void cellRangeAddress(XSSFSheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
    //当起始行号小于终止行号(起始列号小于等于终止列号)，或者（起始行号小于终止行号）起始列号小于终止列号时，可以进行合并
    if ((firstRow < lastRow && firstCol <= lastCol) || (firstRow <= lastRow && firstCol < lastCol)) {
      //参数：起始行号，终止行号， 起始列号，终止列号
      sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }
  }

}
