package com.htks.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import static com.htks.common.SystemConfig.ERROR;
import static com.htks.common.SystemConfig.SUCCESS;
import static com.htks.common.SystemConfig.WARN;

/**
 * 返回前端RestBody
 *
 * <AUTHOR>
 * @date 2020/09/09.
 */
@ApiModel(value = "RestBody")
public class RestBody<T> implements Rest<T>, Serializable {

  private static final long serialVersionUID = 5152071622019929314L;

  @ApiModelProperty(value = "响应状态代码")
  private int code = 1000;

  @ApiModelProperty(value = "响应状态")
  private String status = SUCCESS;

  @ApiModelProperty(value = "响应数据")
  private T data;

  @ApiModelProperty(value = "响应状态描述")
  private String message = "";

  @ApiModelProperty(value = "系统标识")
  private String identifier = "";

  public static Rest<?> ok() {
    return new RestBody<>();
  }

  public static <T> Rest<T> ok(String message) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setMessage(message);

    return restBody;
  }

  public static <T> Rest<T> okData(T data) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setData(data);

    return restBody;
  }

  public static <T> Rest<T> okData(T data, String message) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setData(data);
    restBody.setMessage(message);

    return restBody;
  }

  public static <T> Rest<T> build(int code, String status, T data,
      String message,
      String identifier) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setCode(code);
    restBody.setStatus(status);
    restBody.setData(data);
    restBody.setMessage(message);
    restBody.setIdentifier(identifier);

    return restBody;
  }

  public static <T> Rest<T> failure(String message) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setCode(-1);
    restBody.setStatus(ERROR);
    restBody.setMessage(message);
    restBody.setIdentifier("-9999");

    return restBody;
  }

  public static <T> Rest<T> failure(String message, String identifier) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setStatus(ERROR);
    restBody.setMessage(message);
    restBody.setIdentifier(identifier);

    return restBody;
  }

  public static <T> Rest<T> failure(int httpStatus, String message) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setCode(httpStatus);
    restBody.setStatus(ERROR);
    restBody.setMessage(message);
    restBody.setIdentifier("-1");

    return restBody;
  }

  public static <T> Rest<T> failureData(T data, String message,
      String identifier) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setStatus(ERROR);
    restBody.setIdentifier(identifier);
    restBody.setData(data);
    restBody.setMessage(message);

    return restBody;
  }

  public static <T> Rest warn(int httpStatus, String message) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setCode(httpStatus);
    restBody.setStatus(WARN);
    restBody.setMessage(message);
    restBody.setIdentifier("-1");

    return restBody;
  }

  public static <T> Rest<T> warn(String message) {
    final Rest<T> restBody = new RestBody<>();
    restBody.setCode(-1);
    restBody.setStatus(WARN);
    restBody.setMessage(message);
    restBody.setIdentifier("-1");

    return restBody;
  }

  public int getCode() {
    return code;
  }

  @Override
  public void setCode(int code) {
    this.code = code;
  }

  @Override
  public void setStatus(String status) {
    this.status = status;
  }

  public String getStatus() {
    return status;
  }

  public T getData() {
    return data;
  }

  @Override
  public void setData(T data) {
    this.data = data;
  }

  public String getMessage() {
    return message;
  }

  @Override
  public void setMessage(String message) {
    this.message = message;
  }

  public String getIdentifier() {
    return identifier;
  }

  @Override
  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }
}
