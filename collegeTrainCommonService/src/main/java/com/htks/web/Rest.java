package com.htks.web;

import io.swagger.annotations.ApiModel;

/**
 * 返回前端Rest
 *
 * <AUTHOR>
 * @date 2020/09/09.
 */
@ApiModel(value = "Rest")
public interface Rest<T> {

  /**
   * 业务状态码，设计时应该区别于http状态码.
   *
   * @param code the code
   */
  void setCode(int code);

  /**
   * 状态.
   *
   * @param status the status
   */
  void setStatus(String status);

  /**
   * 数据载体.
   *
   * @param data the data
   */
  void setData(T data);

  /**
   * 提示信息.
   *
   * @param message the message
   */
  void setMessage(String message);

  /**
   * 预留的标识位，作为一些业务的处理标识.
   *
   * @param identifier 标识
   */
  void setIdentifier(String identifier);
}
