package com.htks.web;

import static com.google.common.base.MoreObjects.toStringHelper;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collections;
import java.util.List;

/**
 * 返回前端的集合VO
 *
 * <AUTHOR>
 * @date 2020/12/01.
 */
@ApiModel(value = "JsonArrayVO")
public final class JsonArrayVO<T extends AbstractVO> extends AbstractVO {

  private static final long serialVersionUID = 6966189843992446695L;

  /**
   * 数据集合
   */
  @ApiModelProperty(value = "数据集合")
  private List<T> records;

  public JsonArrayVO() {
    this.records = Collections.emptyList();
  }

  public JsonArrayVO(List<T> records) {
    this.records = records;
  }

  public List<T> getRecords() {
    return records;
  }

  public void setRecords(List<T> records) {
    this.records = records;
  }

  @Override
  public String toString() {
    return toStringHelper(this).
        add("records", records).
        toString();
  }
}
