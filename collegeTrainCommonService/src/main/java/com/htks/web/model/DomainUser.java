package com.htks.web.model;

/**
 * <AUTHOR>
 * @since 2022/03/03 10:39
 */

public class DomainUser {

  public String objectCategory;
  public String whenCreated;
  public String badPwdCount;
  public String mDBUseDefaults;
  public String codePage;
  public String msExchDumpsterWarningQuota;
  public String msExchBlockedSendersHash;
  public String msExchELCMailboxFlags;
  public String msExchMobileMailboxFlags;
  public String mail;
  public String objectGUID;
  public String adminCount;
  public String msExchUserAccountControl;
  public String msExchDumpsterQuota;
  public String msExchMailboxSecurityDescriptor;
  public String msExchAuditOwner;
  public String mSDSConsistencyGuid;
  public String memberOf;
  public String msExchMailboxGuid;
  public String instanceType;
  public String msExchPoliciesIncluded;
  public String msDSExternalDirectoryObjectId;
  public String objectSid;
  public String badPasswordTime;
  public String proxyAddresses;
  public String dSCorePropagationData;
  public String objectClass;
  public String company;
  public String msExchWhenMailboxCreated;
  public String name;
  public String sn;
  public String msExchPreviousRecipientTypeDetails;
  public String userAccountControl;
  public String primaryGroupID;
  public String lastLogon;
  public String accountExpires;
  public String lastLogoff;
  public String uSNChanged;
  public String msExchRBACPolicyLink;
  public String cn;
  public String msExchArchiveWarnQuota;
  public String msExchVersion;
  public String msExchTextMessagingState;
  public String title;
  public String logonCount;
  public String msExchHomeServerName;
  public String sAMAccountType;
  public String msExchRecipientTypeDetails;
  public String legacyExchangeDN;
  public String givenName;
  public String uSNCreated;
  public String displayName;
  public String userPrincipalName;
  public String pwdLastSet;
  public String whenChanged;
  public String lastLogonTimestamp;
  public String msExchMailboxAuditEnable;
  public String department;
  public String msExchAuditDelegate;
  public String countryCode;
  public String mailNickname;
  public String msExchArchiveQuota;
  public String distinguishedName;
  public String msExchRecipientDisplayType;
  public String homeMDB;
  public String msExchRecipientSoftDeletedStatus;
  public String msExchUMDtmfMap;
  public String msExchCalendarLoggingQuota;
  public String showInAddressBook;
  public String msExchUserCulture;
  public String sAMAccountName;


}