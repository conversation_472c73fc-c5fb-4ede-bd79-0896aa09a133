package com.htks.domain.question.dto;

import com.htks.domain.AbstractPaginationQueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class PaperCondition extends AbstractPaginationQueryCondition {

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "试卷标题")
    private String paperName;

    @ApiModelProperty(value = "试卷类型 单选题、多选题、判断题、简答题、填空题")
    private String questionType;

    @ApiModelProperty(value = "考试项目")
    private String category;

    @ApiModelProperty(value = "考试类型")
    private String paperType;

    @ApiModelProperty(value = "试题内容")
    private String questionContent;
}
