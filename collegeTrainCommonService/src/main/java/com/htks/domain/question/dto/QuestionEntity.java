package com.htks.domain.question.dto;

import com.htks.domain.course.dto.Attachment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class QuestionEntity {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "试卷ID")
    private Long paperId;

    @ApiModelProperty(value = "试题类型 1 单选题\n" + "2 多选题\n" + "3 判断题\n" + "4 填空题\n" + "5 主观题")
    private String questionType;

    @ApiModelProperty(value = "试题内容")
    private String questionContent;

    @ApiModelProperty(value = "答案")
    private String standardAnswer;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "选项列表")
    private List<QuestionItemEntity> questionItemEntityList;

    @ApiModelProperty(value = "题干的附件信息")
    private Attachment attachment;
}
