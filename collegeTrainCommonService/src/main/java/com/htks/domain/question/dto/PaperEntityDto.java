package com.htks.domain.question.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class PaperEntityDto {
    @ApiModelProperty(value = "试卷ID")
    private Long id;

    @ApiModelProperty(value = "试题ID")
    private Long questionId;

    @ApiModelProperty(value = "考试项目")
    private String category;

    @ApiModelProperty(value = "考试名称")
    private String categoryItem;

    @ApiModelProperty(value = "考试类型")
    private String paperType;

    @ApiModelProperty(value = "试卷标题")
    private String paperName;

    @ApiModelProperty(value = "试题类型")
    private String questionType;

    @ApiModelProperty(value = "试题内容")
    private String questionContent;

    @ApiModelProperty(value = "试题内容")
    private String department;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "试题列表")
    private List<QuestionEntity> questionEntityList;
}
