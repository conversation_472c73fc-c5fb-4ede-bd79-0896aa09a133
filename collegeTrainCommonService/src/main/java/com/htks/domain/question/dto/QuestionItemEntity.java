package com.htks.domain.question.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class QuestionItemEntity {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "试题ID")
    private Long questionId;

    @ApiModelProperty(value = "选项内容")
    private String itemContent;

    @ApiModelProperty(value = "选项ABCD")
    private String itemOption;

}
