package com.htks.domain.question.repository.hana;

import com.htks.domain.admin.AdminCondition;
import com.htks.domain.admin.dto.AdminSearchEntity;
import com.htks.domain.common.dto.UpgradeUserAdminEntity;
import com.htks.domain.course.dto.Attachment;
import com.htks.domain.question.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface QuestionRepository {
    int queryPaperCount(PaperCondition condition);

    List<PaperEntityDto> queryPaperList(PaperCondition condition);

    int addPaper(PaperEntity condition);

    int addQuestion(QuestionEntity condition);

    int addQuestionItem(QuestionItemEntity condition);

    List<DepartmentDto> getDepartmentList();

    List<CategoryDto> categoryList();

    int deletePaper(Long id);

    int deletePaperQuestion(Long id);

    QuestionEntity getQuestionInfo(Long id);

    Attachment getAttachmentByQuestionId(Long questionId);

    List<QuestionItemEntity> getQuestionItem(Long questionId);

    int deleteQuestionItem(Long questionId);

    int updateQuestion(QuestionEntity questionEntity);
}
