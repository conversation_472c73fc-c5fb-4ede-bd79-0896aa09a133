package com.htks.domain.question.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class PaperEntity {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "考试配置项目ID")
    private Long examCategoryId;

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "上岗证id")
    private String postInfoId;

    @ApiModelProperty(value = "创建人", example = "31187")
    private String creator;

    @ApiModelProperty(value = "修改人", example = "31187")
    private String updater;

    @ApiModelProperty(value = "试卷标题")
    private String paperName;

    @ApiModelProperty(value = "试题列表")
    private List<QuestionEntity> questionEntityList;
}
