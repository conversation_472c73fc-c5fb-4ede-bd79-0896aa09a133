package com.htks.domain.question.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class Category {
    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "考试配置项目")
    private String category;
    @ApiModelProperty(value = "考试配置项目-名称")
    private String categoryItem;
    @ApiModelProperty(value = "试卷类型")
    private String paperType;
    @ApiModelProperty(value = "考试时长")
    private Integer testTime;
    @ApiModelProperty(value = "题库名称")
    private String title;
}
