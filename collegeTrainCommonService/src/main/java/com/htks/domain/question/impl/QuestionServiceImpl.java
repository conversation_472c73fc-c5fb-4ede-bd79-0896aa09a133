package com.htks.domain.question.impl;

import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.course.dto.Attachment;
import com.htks.domain.course.repository.hana.CourseRepository;
import com.htks.domain.question.QuestionService;
import com.htks.domain.question.dto.*;
import com.htks.domain.question.repository.hana.QuestionRepository;
import com.htks.web.JsonPagedVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/05/08 16:44
 **/
@Slf4j
@Service
public class QuestionServiceImpl implements QuestionService {

    @Autowired
    private QuestionRepository questionRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Override
    public JsonPagedVO<PaperEntityDto> queryPaperList(PaperCondition condition) {
        final int recordCount = questionRepository.queryPaperCount(condition);
        final List<PaperEntityDto> result = questionRepository.queryPaperList(condition);
        if(result !=null){
            return new JsonPagedVO(result,recordCount);
        }
        return new JsonPagedVO(new ArrayList<>(),recordCount);
    }

    @Override
    public ResultVO savePaper(PaperSaveCondition condition) {
        PaperEntity paperEntity = new PaperEntity();
        BeanUtils.copyProperties(condition, paperEntity);
        questionRepository.addPaper(paperEntity);
        for(QuestionImportExcel questionImportExcel : condition.getQuestionImportExcelList()){
            QuestionEntity questionEntity = new QuestionEntity();
            questionEntity.setCreator(condition.getCreator());
            questionEntity.setUpdater(condition.getUpdater());
            questionEntity.setPaperId(paperEntity.getId());
            questionEntity.setQuestionContent(questionImportExcel.getTitle());
            questionEntity.setQuestionType(questionImportExcel.getType());
            if(!StringUtils.isEmpty(questionImportExcel.getAnswer())){
                questionEntity.setStandardAnswer(questionImportExcel.getAnswer().replaceAll("，",","));
            }
            questionRepository.addQuestion(questionEntity);
            QuestionItemEntity questionItemEntity = new QuestionItemEntity();
            if(StringUtils.isNotEmpty(questionImportExcel.getOptionA())){
                questionItemEntity = new QuestionItemEntity();
                questionItemEntity.setQuestionId(questionEntity.getId());
                questionItemEntity.setItemContent(questionImportExcel.getOptionA());
                questionItemEntity.setItemOption("A");
                questionRepository.addQuestionItem(questionItemEntity);
            }
            if(StringUtils.isNotEmpty(questionImportExcel.getOptionB())){
                questionItemEntity = new QuestionItemEntity();
                questionItemEntity.setQuestionId(questionEntity.getId());
                questionItemEntity.setItemContent(questionImportExcel.getOptionB());
                questionItemEntity.setItemOption("B");
                questionRepository.addQuestionItem(questionItemEntity);
            }
            if(StringUtils.isNotEmpty(questionImportExcel.getOptionC())){
                questionItemEntity = new QuestionItemEntity();
                questionItemEntity.setQuestionId(questionEntity.getId());
                questionItemEntity.setItemContent(questionImportExcel.getOptionC());
                questionItemEntity.setItemOption("C");
                questionRepository.addQuestionItem(questionItemEntity);
            }
            if(StringUtils.isNotEmpty(questionImportExcel.getOptionD())){
                questionItemEntity = new QuestionItemEntity();
                questionItemEntity.setQuestionId(questionEntity.getId());
                questionItemEntity.setItemContent(questionImportExcel.getOptionD());
                questionItemEntity.setItemOption("D");
                questionRepository.addQuestionItem(questionItemEntity);
            }
        }
        return new ResultVO(ResultCode.SUCCESS.getCode());
    }

    @Override
    public ResultVO getDepartmentList() {
        return new ResultVO(questionRepository.getDepartmentList());
    }

    @Override
    public ResultVO categoryList() {
        return new ResultVO(questionRepository.categoryList());
    }

    @Override
    public ResultVO deletePaperQuestion(Long id) {
        return new ResultVO(questionRepository.deletePaperQuestion(id));
    }

    @Override
    public ResultVO getQuestionDetail(Long id) {
        QuestionEntity questionEntity = questionRepository.getQuestionInfo(id);
        questionEntity.setAttachment(questionRepository.getAttachmentByQuestionId(id));
        questionEntity.setQuestionItemEntityList(questionRepository.getQuestionItem(id));
        return new ResultVO(questionEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVO updateQuestion(QuestionEntity questionEntity) {
        Attachment attachment = questionEntity.getAttachment();
        if(attachment!=null){
            attachment.setQuestionId(questionEntity.getId());
            courseRepository.insertAttachment(attachment);
        }
        questionRepository.deleteQuestionItem(questionEntity.getId());
        for(QuestionItemEntity questionItemEntity : questionEntity.getQuestionItemEntityList()){
            questionItemEntity.setQuestionId(questionEntity.getId());
            questionRepository.addQuestionItem(questionItemEntity);
        }
        questionRepository.updateQuestion(questionEntity);
        return new ResultVO(ResultCode.SUCCESS.getCode());
    }
}
