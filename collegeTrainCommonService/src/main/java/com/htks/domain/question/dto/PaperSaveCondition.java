package com.htks.domain.question.dto;

import com.htks.domain.AbstractPaginationQueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class PaperSaveCondition {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "考试配置项目ID", example = "1")
    private Long examCategoryId;

    @ApiModelProperty(value = "部门", example = "Bumping工程部")
    private String department;

    @ApiModelProperty(value = "试卷标题", example = "切筋工艺流程介绍")
    private String paperName;

    @ApiModelProperty(value = "创建人", example = "31187")
    private String creator;

    @ApiModelProperty(value = "修改人", example = "31187")
    private String updater;

    @ApiModelProperty(value = "上岗证id")
    private String postInfoId;

    @ApiModelProperty(value = "试题列表")
    private List<QuestionImportExcel> questionImportExcelList;

}
