package com.htks.domain.question;

import com.github.pagehelper.Page;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.admin.AdminCondition;
import com.htks.domain.admin.dto.AdminSearchEntity;
import com.htks.domain.common.dto.UpgradeUserAdminEntity;
import com.htks.domain.question.dto.*;
import com.htks.web.JsonPagedVO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface QuestionService {

    JsonPagedVO<PaperEntityDto> queryPaperList(PaperCondition condition);

    ResultVO savePaper(PaperSaveCondition condition);

    ResultVO getDepartmentList();

    ResultVO categoryList();

    ResultVO deletePaperQuestion(Long id);

    ResultVO getQuestionDetail(Long id);

    ResultVO updateQuestion(QuestionEntity questionEntity);

}
