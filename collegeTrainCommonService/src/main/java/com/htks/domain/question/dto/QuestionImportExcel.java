package com.htks.domain.question.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QuestionImportExcel {
    @ExcelProperty({"题型*"})
    private String type;
    @ExcelProperty({"题干*"})
    private String title;
    @ExcelProperty({"答案*"})
    private String answer;
    @ExcelProperty({"选项A"})
    private String optionA;
    @ExcelProperty({"选项B"})
    private String optionB;
    @ExcelProperty({"选项C"})
    private String optionC;
    @ExcelProperty({"选项D"})
    private String optionD;
}
