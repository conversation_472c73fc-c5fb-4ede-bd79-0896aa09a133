package com.htks.domain.pushCourse.repository.hana;

import com.htks.domain.course.dto.CourseEntity;
import com.htks.domain.pushCourse.dto.*;
import com.htks.domain.question.dto.Category;
import com.htks.domain.student.dto.ReservationWx;
import com.htks.domain.student.dto.Student;
import com.htks.domain.student.dto.WeeklyPushEntry;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface PushCourseRepository {
    int queryPushCourseCount(PushCourseCondition condition);

    List<PushCourseEntityDto> queryPushCourseList(PushCourseCondition condition);

    int queryPushExamCount(PushExamCondition condition);

    List<PushExamEntityDto> queryPushExamList(PushExamCondition condition);

    int saveAssessedCourse(PushCourseEntityDto pushCourseEntityDto);

    int updatePushCourseTime(PushCourseEntityDto pushCourseEntityDto);

    int updatePushExamTime(PushExamEntityDto pushCourseEntityDto);

    int saveAssessedCourseItem(PushCourseEntityItemDto pushCourseEntityItemDto);

    List<HashMap<String,Object>> getStudentIdByBatch(@Param("batch") String batch, @Param("factoryFlag") String factoryFlag);

    List<CourseEntity> getCourseIdByCategory(@Param("category") String category, @Param("factoryFlag") String factoryFlag);

    PushCourseEntityDto selectByCategoryAndBatch(PushCourseEntityDto pushCourseEntityDto);

    int updateAssessedCourseItem(@Param("oldId") Long oldId, @Param("newId") Long newId);

    int isExists(PushCourseEntityItemDto pushCourseEntityItemDto);

    int isExistsExam(PushExamEntityItemDto pushExamEntityDto);

    List<Category> getCategoryIdForExam(@Param("category") String category, @Param("categoryItem") String categoryItem);

    int saveAssessedExam(PushExamEntityDto pushExamEntityDto);

    int saveAssessedExamItem(PushExamEntityItemDto pushExamEntityItemDto);

    Long inExistsExam(PushExamEntityDto pushExamEntityDto);

    int getLimitTime(@Param("category") String category, @Param("categoryItem") String categoryItem);
    //todo
    @MapKey("assessedId")
    List<Map<String,Object>> selectStartPostExam();

    int insertPostInfoCard(@Param("assessedId") Long assessedId, @Param("postInfoId") Long postInfoId);
  List<PushCourseEntityDto>  getAdminNo(String name);
  List<String> getExamDetail(String category);
    List<String> getExam( );
  List<String> getCourse();

  List<WeeklyPushEntry>  getWeeklyPush( );

    List<WeeklyPushEntry>  getWeeklyPushBydepartment( );
    WeeklyPushEntry getMasterEmployeeNo(String zu_id);
    WeeklyPushEntry getDepartment(String zu_id);

    List<String>   getSopDeadTime(String date);

    List<WeeklyPushEntry> getSopSum(List<String>employeeNo);

    List<ReservationWx> getReservationEmployeeno();
    String getTime();
    List<Student>getCollegeTrainAssessed();

    List<String> getRole();

}
