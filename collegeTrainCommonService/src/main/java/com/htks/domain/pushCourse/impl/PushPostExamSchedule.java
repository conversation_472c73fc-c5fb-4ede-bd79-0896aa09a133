package com.htks.domain.pushCourse.impl;

import com.htks.common.config.WeChatConfig;
import com.htks.common.utils.WeChatUtils;
import com.htks.domain.ExamInformation.service.SatisfactionCountService;
import com.htks.domain.ExamInformation.service.impl.*;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.domain.pushCourse.repository.hana.PushCourseRepository;
import com.htks.domain.student.dto.ReservationWx;
import com.htks.domain.student.dto.Student;
import com.htks.domain.student.dto.WeeklyPushEntry;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpMessageServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.http.client.methods.HttpUriRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/05/15 08:59
 **/
@Service
@Slf4j
public class PushPostExamSchedule {
    @Autowired
    private WeChatConfig weChatConfig;
    @Autowired
    private PushCourseRepository pushCourseRepository;
    @Autowired
    private SatisfactionCountService service;

//    @Scheduled(cron = "0 * 14 * * ?")

    @Value("${spring.profiles.active}")
    private String activeYml;
//    @Scheduled(cron = "0 * 14 * * ?")
    //手动执行抛送上岗证考试，不要定时任务
    @XxlJob("pushPostExam")
    public void pushPostExam() {
        log.info("开始执行定时任务抛送上岗证考试");
        List<Map<String, Object>> mapList = pushCourseRepository.selectStartPostExam();
        if (CollectionUtils.isEmpty(mapList)) {
            return;
        }
        for (Map<String, Object> map : mapList) {
            Long assessedId = Long.parseLong(map.get("ASSESSEDID").toString());
            Long postInfoId = Long.parseLong(map.get("POSTINFOID").toString());
            pushCourseRepository.insertPostInfoCard(assessedId, postInfoId);
        }
    }


//    @Scheduled(cron = "0 0 17 * * ?")
    @XxlJob("pushWeekly")
    public void pushWeekly() {
        if(!"prod".equals(activeYml)){
            return;
        }
        if (judgeWeekly()) {
            log.info("开始执行定时任务推送未批阅周报信息");
            LocalDate currentDate = LocalDate.now();
            // 获取当前年份的字符串形式
            List<WeeklyPushEntry> mapList = pushCourseRepository.getWeeklyPush();
            if (CollectionUtils.isEmpty(mapList)) {
                return;
            } else {
                final WxCpService wxCpService = weChatConfig.getWxCpService();
                final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
                // 开启微信消息服务
                final WxCpMessage wxCpMessage = new WxCpMessage();
                String time = pushCourseRepository.getTime();

                LocalDate targetDate = LocalDate.parse(time);
                LocalDate nextWeek = targetDate.plusWeeks(1);

                if (nextWeek.getDayOfWeek() == DayOfWeek.TUESDAY || nextWeek.getDayOfWeek() == DayOfWeek.WEDNESDAY) {
                for (WeeklyPushEntry weeklyPushEntry : mapList) {
                    WeChatUtils weChatUtils = new WeChatUtils();
                    WeeklyPushEntry weeklyPushEntry1 = pushCourseRepository.getMasterEmployeeNo(weeklyPushEntry.getArea());
                    EmployeeEntity employeeEntity = service.getWechatId(weeklyPushEntry1.getEmployeeNo());//weeklyPushEntry1.getEmployeeNo()
                    weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                            "您好," + weeklyPushEntry1.getEmployeeName() + "主管，你还有" + weeklyPushEntry.getSum() + "份学员的周报未评价，请及时登录企业微信端进行评价"
                    );
                }
            }
                StringBuilder sb = new StringBuilder();
                sb.append("您好" + "孙松" + "专员，截止目前各部门的周报未评价明细如下" + "\r\n");
                List<WeeklyPushEntry> weeklyByDepartment = pushCourseRepository.getWeeklyPushBydepartment();
                if (weeklyByDepartment.size() > 0) {
                    for (WeeklyPushEntry weeklyPushEntry : weeklyByDepartment) {
                        sb.append(weeklyPushEntry.getDepartmentName() + ":" + weeklyPushEntry.getSum() + "份");
                    }
                    WeChatUtils weChatUtils = new WeChatUtils();
                    EmployeeEntity employeeEntity = service.getWechatId("32560");
                    weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                            sb.toString());
                }
            }
        }

    }

//    @Scheduled(cron = "0 0 16 * * ?")
//    @Scheduled(cron = "0 0 16 * * ?")
    @XxlJob("pushSop")
    public void pushSop() {
        LocalDate today = LocalDate.now();

        // 获取昨天的日期
        LocalDate yesterday = today.minusDays(1);

        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将昨天的日期格式化成字符串
        String yesterdayStr = yesterday.format(formatter);

        List<String> employeeNo = pushCourseRepository.getSopDeadTime(yesterdayStr);
        if (employeeNo.size() > 0) {
            List<WeeklyPushEntry> list = pushCourseRepository.getSopSum(employeeNo);
            final WxCpService wxCpService = weChatConfig.getWxCpService();
            final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
            // 开启微信消息服务
            final WxCpMessage wxCpMessage = new WxCpMessage();
            if (list.size() > 0) {
                for (WeeklyPushEntry weeklyPushEntry : list) {
                    WeeklyPushEntry weeklyPushEntry1 = pushCourseRepository.getMasterEmployeeNo(weeklyPushEntry.getArea());
                    WeChatUtils weChatUtils = new WeChatUtils();
                    EmployeeEntity employeeEntity = service.getWechatId("31973");//weeklyPushEntry1.getEmployeeNo()
                    weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                            "您好" + weeklyPushEntry1.getEmployeeName() + "主管，你还有" + weeklyPushEntry.getSum() + "份学员的SOP报告未评价，请及时登录企业微信端进行评价"
                    );
                }
            }
        }
    }

    public void sendReservation() {
        List<ReservationWx> list = pushCourseRepository.getReservationEmployeeno();
        if (list.size() > 0) {
            for (ReservationWx reservationWx : list) {
                final WxCpService wxCpService = weChatConfig.getWxCpService();
                final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
                // 开启微信消息服务
                final WxCpMessage wxCpMessage = new WxCpMessage();
                WeChatUtils weChatUtils = new WeChatUtils();
                EmployeeEntity employeeEntity = service.getWechatId(reservationWx.getEmployeeNo());
                weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                        //todo
                        "您好," + reservationWx.getEmployeeName() + ",您本次工程实操考试得分为" + reservationWx.getScore() + "分，未通过，请尽快在企业微信端再次进行实操预约，请知悉"
                );
            }
        }
    }

    public boolean judgeWeekly() {
        // 创建一个 Calendar 实例
        Calendar calendar = Calendar.getInstance();
        // 获取当前日期是星期
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

        // 判断是否是周二或周三
        if (dayOfWeek == Calendar.TUESDAY || dayOfWeek == Calendar.WEDNESDAY) {
            return true;
        } else {
            return false;
        }
    }

//    @Scheduled(cron = "0 0 17 * * ?")
    @XxlJob("sendaaaa")
    public void sendaaaa(){
      /*  if(!"prod".equals(activeYml)){
            return;
        }*/
        List<Student> list=pushCourseRepository.getCollegeTrainAssessed();

        for (Student student:list) {
            LocalDate hireDate = LocalDate.parse(student.getDate(),DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 计算第五周周一
            LocalDate fifthWeekMonday = hireDate.plusWeeks(4).with(DayOfWeek.MONDAY);

            // 计算第九周周一
            LocalDate ninthWeekMonday = hireDate.plusWeeks(8).with(DayOfWeek.MONDAY);

            // 计算第12周周一
            LocalDate twelfthWeekMonday = hireDate.plusWeeks(11).with(DayOfWeek.MONDAY);
            // 计算第五周周一

            LocalDate fifthWeekFriday = hireDate.plusWeeks(4).with(DayOfWeek.FRIDAY);

            // 计算第九周周一
            LocalDate ninthWeekFriday = hireDate.plusWeeks(8).with(DayOfWeek.FRIDAY);

            // 计算第12周周一
            LocalDate twelfthWeekFriday = hireDate.plusWeeks(11).with(DayOfWeek.FRIDAY);

            if (ninthWeekMonday.isEqual(LocalDate.now()) || fifthWeekMonday.isEqual(LocalDate.now()) || twelfthWeekMonday.isEqual(LocalDate.now())) {
                final WxCpService wxCpService = weChatConfig.getWxCpService();
                final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
                // 开启微信消息服务
                final WxCpMessage wxCpMessage = new WxCpMessage();
                WeChatUtils weChatUtils = new WeChatUtils();
                EmployeeEntity employeeEntity = service.getWechatId(student.getEmpployeeNo());
                weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                        //todo
                        "您好," + student.getEmpployeeName() + "学员,请进入企业微信端满意度调查-大学生满意度调查进行本周期的满意度调查，谢谢配合"
                );
            }
        }

        if (list.size()>0) {
            LocalDate hireDate = LocalDate.parse(list.get(0).getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            List<String> list1 = pushCourseRepository.getRole();
            for (String student : list1) {

                // 计算第五周周一
                LocalDate fifthWeekMonday = hireDate.plusWeeks(4).with(DayOfWeek.MONDAY);

                // 计算第九周周一
                LocalDate ninthWeekMonday = hireDate.plusWeeks(8).with(DayOfWeek.MONDAY);

                // 计算第12周周一
                LocalDate twelfthWeekMonday = hireDate.plusWeeks(11).with(DayOfWeek.MONDAY);

                // 计算第五周周一
                LocalDate fifthWeekFriday = hireDate.plusWeeks(4).with(DayOfWeek.FRIDAY);

                // 计算第九周周一
                LocalDate ninthWeekFriday = hireDate.plusWeeks(8).with(DayOfWeek.FRIDAY);

                // 计算第12周周一
                LocalDate twelfthWeekFriday = hireDate.plusWeeks(11).with(DayOfWeek.FRIDAY);

                if (ninthWeekFriday.isEqual(LocalDate.now()) || fifthWeekFriday.isEqual(LocalDate.now()) || twelfthWeekFriday.isEqual(LocalDate.now())) {
                    final WxCpService wxCpService = weChatConfig.getWxCpService();
                    final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
                    // 开启微信消息服务
                    final WxCpMessage wxCpMessage = new WxCpMessage();
                    WeChatUtils weChatUtils = new WeChatUtils();
                    EmployeeEntity employeeEntity = service.getWechatId(student);
                    weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                            //todo
                            "您好," + student + "专员,本周期的大学生满意度评价已结束，请登录系统查看评价结果，谢谢！"
                    );
                }
            }
        }

    }
}
