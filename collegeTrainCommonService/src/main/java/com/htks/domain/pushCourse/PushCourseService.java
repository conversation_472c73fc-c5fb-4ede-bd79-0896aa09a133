package com.htks.domain.pushCourse;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.course.dto.CourseCondition;
import com.htks.domain.pushCourse.dto.PushCourseCondition;
import com.htks.domain.pushCourse.dto.PushCourseEntityDto;
import com.htks.domain.pushCourse.dto.PushExamCondition;
import com.htks.domain.pushCourse.dto.PushExamEntityDto;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.web.JsonPagedVO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PushCourseService {

    JsonPagedVO<PushCourseEntityDto> queryPushCourseList(PushCourseCondition condition);

    JsonPagedVO<PushExamEntityDto> queryPushExamList(PushExamCondition condition);

    ResultVO sendCourse(PushCourseEntityDto condition);

    ResultVO sendExam(PushExamEntityDto condition);

    List<PushCourseEntityDto> getAdminNo(String name);

    List<String> getExamDetail(String category);
    List<String> getExam( );
    List<String> getCourse();

}
