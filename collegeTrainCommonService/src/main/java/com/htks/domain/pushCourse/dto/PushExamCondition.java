package com.htks.domain.pushCourse.dto;

import com.htks.domain.AbstractPaginationQueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PushExamCondition extends AbstractPaginationQueryCondition {

    @ApiModelProperty(value = "类别")
    private String category;

    @ApiModelProperty(value = "培训项目")
    private List<String> categoryItem;

    @ApiModelProperty(value = "抛送人")
    private String creator;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "抛送类别")
    private String pushObject;

    @ApiModelProperty("厂别")
    private List<String> factoryFlagList;
}
