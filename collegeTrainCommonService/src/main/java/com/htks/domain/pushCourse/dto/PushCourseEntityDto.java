package com.htks.domain.pushCourse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PushCourseEntityDto {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "类别")
    private String category;

    @ApiModelProperty(value = "推送对象")
    private String pushObject;

    @ApiModelProperty(value = "抛送人")
    private String creator;

    @ApiModelProperty(value = "抛送人姓名")
    private String createName;

    @ApiModelProperty("有效截止时间")
    private String deadTime;

    @ApiModelProperty(".送时间")
    private String pushTime;

    @ApiModelProperty("厂别")
    private String factoryFlag;

}
