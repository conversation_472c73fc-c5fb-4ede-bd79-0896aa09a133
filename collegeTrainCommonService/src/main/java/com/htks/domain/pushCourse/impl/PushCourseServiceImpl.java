package com.htks.domain.pushCourse.impl;

import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.DateUtils;
import com.htks.domain.course.CourseService;
import com.htks.domain.course.dto.*;
import com.htks.domain.course.repository.hana.CourseRepository;
import com.htks.domain.pushCourse.PushCourseService;
import com.htks.domain.pushCourse.dto.*;
import com.htks.domain.pushCourse.repository.hana.PushCourseRepository;
import com.htks.domain.question.dto.Category;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.web.JsonPagedVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/05/08 16:44
 **/
@Slf4j
@Service
public class PushCourseServiceImpl implements PushCourseService {

    @Autowired
    private PushCourseRepository pushCourseRepository;

    @Override
    public JsonPagedVO<PushCourseEntityDto> queryPushCourseList(PushCourseCondition condition) {
        final int recordCount = pushCourseRepository.queryPushCourseCount(condition);
        final List<PushCourseEntityDto> result = pushCourseRepository.queryPushCourseList(condition);
        if(result !=null){
            return new JsonPagedVO(result,recordCount);
        }
        return new JsonPagedVO(new ArrayList<>(),recordCount);
    }

    @Override
    public JsonPagedVO<PushExamEntityDto> queryPushExamList(PushExamCondition condition) {
        final int recordCount = pushCourseRepository.queryPushExamCount(condition);
        final List<PushExamEntityDto> result = pushCourseRepository.queryPushExamList(condition);
        if(result !=null){
            return new JsonPagedVO(result,recordCount);
        }
        return new JsonPagedVO(new ArrayList<>(),recordCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVO  sendCourse(PushCourseEntityDto condition) {
        List<HashMap<String,Object>> studentList = pushCourseRepository.getStudentIdByBatch(condition.getBatch(),condition.getFactoryFlag());
        List<CourseEntity> courseList = pushCourseRepository.getCourseIdByCategory(condition.getCategory(),condition.getFactoryFlag());
        if(CollectionUtils.isEmpty(studentList) || CollectionUtils.isEmpty(courseList)){
            return new ResultVO(ResultCode.ERROR.getCode(),"没有查询到该批次学员，或者没查到课程");
        }

        PushCourseEntityDto pushCourseEntityDto = pushCourseRepository.selectByCategoryAndBatch(condition);
        if(pushCourseEntityDto==null || pushCourseEntityDto.getId()==null){
            pushCourseRepository.saveAssessedCourse(condition);
        }else{
            condition.setId(pushCourseEntityDto.getId());
            pushCourseRepository.updatePushCourseTime(condition);
        }

        for(HashMap<String,Object> map : studentList){
            Long studentId = Long.parseLong(map.get("ID").toString());

            String departmentName = map.get("DEPARTMENT")==null? "" : map.get("DEPARTMENT").toString();
            List<CourseEntity> courseEntities = new ArrayList<>();
            if("批次内全员".equals(condition.getPushObject())||condition.getCategory().equals("HR公开课")){
                courseEntities = courseList;
            }else if("批次内按实习部门".equals(condition.getPushObject())){
                if(!departmentName.equalsIgnoreCase("")){
                    courseEntities = courseList.stream().filter(e->e.getDepartmentName().equals(departmentName)).collect(Collectors.toList());
                }
            }else{
                throw new RuntimeException("抛送对象错误");
            }
            for(CourseEntity courseEntity : courseEntities){
                PushCourseEntityItemDto pushCourseEntityItemDto = new PushCourseEntityItemDto();
                pushCourseEntityItemDto.setCourseId(courseEntity.getId());
                pushCourseEntityItemDto.setAssessedId(studentId);
                pushCourseEntityItemDto.setAssessedCourseId(condition.getId());
                if(pushCourseRepository.isExists(pushCourseEntityItemDto) == 0){
                    //新增
                    pushCourseRepository.saveAssessedCourseItem(pushCourseEntityItemDto);
                }else {

                }
            }
        }
        return new ResultVO(ResultCode.SUCCESS.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVO sendExam(PushExamEntityDto condition) {
        List<HashMap<String,Object>> studentList = pushCourseRepository.getStudentIdByBatch(condition.getBatch(),condition.getFactoryFlag());
        List<Category> categoryList = pushCourseRepository.getCategoryIdForExam(condition.getCategory(),condition.getCategoryItem());
        if(CollectionUtils.isEmpty(studentList) || CollectionUtils.isEmpty(categoryList)){
            return new ResultVO(ResultCode.ERROR.getCode(),"没有查询到该批次学员，或者没查到考试");
        }
        if(categoryList.size()>1){
            return new ResultVO(ResultCode.ERROR.getCode(),"查到多个考试");
        }
        Long id = pushCourseRepository.inExistsExam(condition);
        int limitTimeMinutes = pushCourseRepository.getLimitTime(condition.getCategory(),condition.getCategoryItem());
        condition.setExamEndTime(DateUtils.addMinutes(condition.getExamStartTime(),limitTimeMinutes));

        if(id != null && id > 0){
            condition.setId(id);
            pushCourseRepository.updatePushExamTime(condition);
        }else{
            pushCourseRepository.saveAssessedExam(condition);
        }


        for(HashMap<String,Object> map : studentList){
            Long studentId = Long.parseLong(map.get("ID").toString());
            PushExamEntityItemDto pushExamEntityItemDto = new PushExamEntityItemDto();
            pushExamEntityItemDto.setAssessedExamId(condition.getId());
            pushExamEntityItemDto.setAssessedId(studentId);
            pushExamEntityItemDto.setExamCategoryId(categoryList.get(0).getId());
            if(pushCourseRepository.isExistsExam(pushExamEntityItemDto) == 0){
                pushCourseRepository.saveAssessedExamItem(pushExamEntityItemDto);
            }
        }
        return new ResultVO(ResultCode.SUCCESS.getCode());
    }

    @Override
    public List<PushCourseEntityDto> getAdminNo(String name) {
        return pushCourseRepository.getAdminNo(name);
    }

    @Override
    public List<String> getExamDetail(String category) {
        return pushCourseRepository.getExamDetail(category);
    }

    @Override
    public List<String> getExam() {
        return pushCourseRepository.getExam();
    }


    @Override
    public List<String> getCourse() {
        return pushCourseRepository.getCourse();
    }


}
