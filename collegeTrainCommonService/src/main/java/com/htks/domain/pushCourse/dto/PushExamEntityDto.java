package com.htks.domain.pushCourse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PushExamEntityDto {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "培训类别",example = "HR公开课")
    private String category;

    @ApiModelProperty(value = "培训项目",example = "HR公开课理论考试①")
    private String categoryItem;

    @ApiModelProperty(value = "推送对象 批次内全员、批次内按实习部门",example = "批次内全员")
    private String pushObject;

    @ApiModelProperty(value = "抛送人")
    private String creator;

    @ApiModelProperty("考试开始时间")
    private String examStartTime;

    @ApiModelProperty("考试结束时间")
    private String examEndTime;

    @ApiModelProperty(".送时间")
    private String pushTime;

    @ApiModelProperty("厂别：华天昆山、华天江苏")
    private String factoryFlag;
}
