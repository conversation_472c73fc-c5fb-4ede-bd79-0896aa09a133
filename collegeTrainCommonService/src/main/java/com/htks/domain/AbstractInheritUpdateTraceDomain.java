package com.htks.domain;

import static com.google.common.base.MoreObjects.toStringHelper;

import lombok.Getter;
import lombok.Setter;

/**
 * 域Abstract类
 *
 * <AUTHOR>
 * @date 2010/09/09.
 */
@Getter
@Setter
public abstract class AbstractInheritUpdateTraceDomain extends AbstractUpdateTraceDomain {

  private static final long serialVersionUID = 2481070821589404280L;

  private Long parentId;

  private String parentName;

  private String description;

  @Override
  public String toString() {
    return toStringHelper(this)
        .add("parentId", parentId)
        .add("parentName", parentName)
        .add("description", description)
        .toString();
  }
}
