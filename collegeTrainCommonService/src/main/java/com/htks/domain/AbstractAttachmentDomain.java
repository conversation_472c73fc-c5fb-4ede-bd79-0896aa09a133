package com.htks.domain;

import static com.google.common.base.MoreObjects.toStringHelper;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

/**
 * 附件Abstract类
 *
 * <AUTHOR>
 * @date 2010/09/09.
 */
@Getter
@Setter
public abstract class AbstractAttachmentDomain extends AbstractUpdateTraceDomain {

  private static final long serialVersionUID = -2947161729295420502L;

  /**
   * 业务ID
   */
  private Long businessId;

  /**
   * 文档ID(返回主键)
   */
  private Long documentId;

  /**
   * 附件名称
   */
  @Length(max = 128)
  private String fileName;

  /**
   * 附件大小(单位MB)
   */
  @Length(max = 64)
  private String fileSize;

  /**
   * 附件类型(PDF, DOC, ZIP, XML TXT, JPG等)
   */
  @Length(max = 16)
  private String fileType;

  /**
   * 附件URI
   */
  @Length(max = 256)
  private String fileUri;

  /**
   * 附件备注
   */
  @Length(max = 256)
  private String fileRemark;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("businessId", businessId).
        add("documentId", documentId).
        add("fileName", fileName).
        add("fileSize", fileSize).
        add("fileType", fileType).
        add("fileUri", fileUri).
        add("fileRemark", fileRemark).
        toString();
  }
}
