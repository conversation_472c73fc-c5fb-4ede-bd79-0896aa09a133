package com.htks.domain.student.repository.hana;

import com.htks.domain.student.dto.SatisfactionDegree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Mapper
@Repository
public interface SatisfactionDegreeRepository {
    void inserDegree(@Param("sd") SatisfactionDegree satisfactionDegree,Long ASSESSED_ID);

    String onboardingTime(String employeeNo);

    Integer appraiseIsNull(String employeeNo);

    //void updateDegree(@Param("aa") SatisfactionDegree satisfactionDegree);

    Long getAccessId(String STUDENT_ID);

    Integer judgeIsfirst(Long id);
    Integer getNewScore(String employeeNo);

    String newDate(String employeeNo);

}
