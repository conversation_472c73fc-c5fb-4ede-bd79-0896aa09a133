package com.htks.domain.student.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class AppointMent {

    @ApiModelProperty(value = "NO")
    private String employeeNo;

    @ApiModelProperty(value = "预约开始日期")
    private String aStartDate;

    @ApiModelProperty(value = "预约结束日期")
    private String aEndDate;

    @ApiModelProperty(value = "预约开始时间")
    private String aStartTime;

    @ApiModelProperty(value = "预约结束时间")
    private String aEndTime;

    @ApiModelProperty(value = "评价类型 1：工程类 2：异常答辩")
    private Integer type;
    @ApiModelProperty(value = "厂别")
    private Integer companyType;




}
