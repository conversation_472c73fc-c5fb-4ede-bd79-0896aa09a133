package com.htks.domain.student.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
//考试答案上传参数

public class BodyAnswer {
    @ApiModelProperty(value = "学生工号")
    private String employeeNo;

    @ApiModelProperty(value = "上岗证id")
    private String postInfoId;

    @ApiModelProperty(value = "培训考试项目名称")
    private String categoryItem;

    @ApiModelProperty(value = "答案")
    private List<Answer> list;
    @ApiModelProperty(value = "答案")
    private String type;
}
