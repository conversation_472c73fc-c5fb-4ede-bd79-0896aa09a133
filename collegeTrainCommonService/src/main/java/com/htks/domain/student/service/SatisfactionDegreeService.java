package com.htks.domain.student.service;

import com.htks.domain.student.dto.SatisfactionDegree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public interface SatisfactionDegreeService {
    void inserDegree(SatisfactionDegree satisfactionDegree,Long ASSESSED_ID);

    String onboardingTime(String employeeNo);

    Integer appraiseIsNull(String employeeNo);

    Date getThisWeekMonday(Date date);
    Long getAccessId(String STUDENT_ID);

//    void updateDegree(@Param("aa") SatisfactionDegree satisfactionDegree);
Integer judgeIsfirst(Long id);

    String newDate(String employeeNo);

    Integer getNewScore(String employeeNo);



}
