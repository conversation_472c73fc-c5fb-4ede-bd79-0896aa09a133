package com.htks.domain.student.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
//满意度评价提交接口参数对象
public class SatisfactionDegree {

    @ApiModelProperty(value = "总分")
    private Integer SCORE;

    @ApiModelProperty(value = "理论培训-授课时长")
    private Integer THEORY_COURSE_DURATION;

    @ApiModelProperty(value = "理论培训-授课专业性")
    private Integer THEORY_MAJOR;

    @ApiModelProperty(value = "课程互动")
    private Integer THEORY_COURSE_INTERACTION;

    @ApiModelProperty(value = "师傅评价-专业能力")
    private Integer MASTER_MAJOR;

    @ApiModelProperty(value = "师傅评价-课程&工作安排")
    private Integer MASTER_COURSE_ARRANGE;

    @ApiModelProperty(value = "师傅评价-实操教学")
    private Integer MASTER_PRACTICAL_OPERATION;

    @ApiModelProperty(value = "师傅评价-耐心负责")
    private Integer MASTER_PATIENCE;

    @ApiModelProperty(value = "领导关心-每周答疑")
    private Integer LEADER_QUESTION;


    @ApiModelProperty(value = "领导关系-座谈会")
    private Integer LEADER_MEET;

    @ApiModelProperty(value = "领导关系-领导关怀")
    private Integer LEADER_CARE;

    @ApiModelProperty(value = "学习感受-理论")
    private Integer STUDY_THEORY;

    @ApiModelProperty(value = "学习感受-实操")
    private Integer STUDY_PRACTICAL_OPERATION;

    @ApiModelProperty(value = "学习感受-报告")
    private Integer STUDY_REPORT;

    @ApiModelProperty(value = "提交时间")
    private String CREATED_TIME;

    @ApiModelProperty(value = "学生id")
    private String employeeNo;
}

