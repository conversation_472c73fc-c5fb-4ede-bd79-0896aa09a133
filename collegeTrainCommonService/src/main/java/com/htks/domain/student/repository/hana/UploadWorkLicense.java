package com.htks.domain.student.repository.hana;


import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.common.dto.PostCardEntity;
import com.htks.domain.common.dto.SopDetailEntity;
import com.htks.domain.school.base.BaseEntity;
import com.htks.domain.school.dto.CollegeTrainDepartment;
import com.htks.domain.school.dto.CollegeTrainSopUpload;
import com.htks.domain.school.dto.CollegeTrainSubjectiveJudge;
import com.htks.domain.school.dto.CollegeTrainWeeklyReport;
import com.htks.domain.student.dto.AppointTime;
import com.htks.domain.student.dto.PostCard;
import com.htks.domain.student.dto.PostCardTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.List;


@Mapper
@Repository
public interface UploadWorkLicense {
    List<String> getWorkLicense(String area,String employeeNo);

    List<String> getWorkLicenseArea(String area,String employeeNo);
    List<String> getAreaList(String employeeNo);
    List<PostCard> findDepartmentCard(String area,String employeeNo);

    List<PostCard> findAreaCard(String area,String employeeNo);

    String postUploadStartTime(String employeeNo);

    String postUploadEndTime(String employeeNo);

    void uploadWorkLicense(String postArea,String postName, Long accessId,String batch,String postId);
    Long getPostCardId(String area,String postName,String no);

    /*void uploadPostInfoCard(Long ASSESSED_ID, Long POST_INFO_ID);

    Long getPostInfoId(String POST_NAME);*/

// 优化测试

    Long getWorkLicense21(String no);
    List<String> getWorkLicense22(Long id);
    List<String>getWorkLicense23(String area, List list);
   //上传上岗证图片
    String getpostCardId(String area,String name,String no);
   long  addPostCardPicture(@Param("AE")AttachmentEntity attachmentEntity,Long postCardId,long accessId) ;
Integer getSopSum(Long id,Integer type);
    String getSopScore(Long id,Integer type);
  void   insertSop(@Param("SOP")CollegeTrainSopUpload collegeTrainSopUpload);
void deleteSop(Long id);
void updateReadAfter(List<CollegeTrainSopUpload> list);
void updateReadAfterByHand(@Param("collegeTrainSopUpload")CollegeTrainSopUpload collegeTrainSopUpload);
   void insertWeeklyReport (List<CollegeTrainWeeklyReport> list);
   void updateWeekly(List<CollegeTrainWeeklyReport> list);
   Integer getWeeklySum(Long assessedId,Integer weekly);
   void   insertSUBJECTIVE ( @Param("collegeTrainSubjectiveJudge")CollegeTrainSubjectiveJudge collegeTrainSubjectiveJudge );
   void updateSubjective(List<CollegeTrainSubjectiveJudge>list );
   Integer getSubjectiveSum(Long assessedId);
List<PostCardEntity>findAllPostCard(String enployeeNo);
    List<PostCardEntity>findAllPostCard1(String enployeeNo);
    String findAllPostCard2(String enployeeNo);
    String findAllPostCard3(String postName,String employeeNo);
    String findAllPostCard4(@Param("employeeNo")String employeeNo,@Param("postId")String postId);
String getFactory(String id);
String getDepartmentName(String id);
    String getDepartmentId(@Param("factoryFlag")String factoryFlag, @Param("department")String department);
void updateSop(String no,String name,Long id,String number);
String getArea(String employeeNo);
SopDetailEntity getSopDetail(Long id);
String getSopEndTime(String employeeNo);
    String getReadAfterEndTime(String employeeNo);
    PostCardTime getPostTime(String employeeNo);

    Integer getSumOne(String id);
    Integer getSumTwo(String id);
    Integer getSumThree(String id);
    Integer getSumFour(String id);

Integer judgePost(String postName,String employeeNo);
}
