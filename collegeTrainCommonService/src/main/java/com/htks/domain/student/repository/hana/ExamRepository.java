package com.htks.domain.student.repository.hana;


import com.htks.domain.question.dto.PaperEntity;
import com.htks.domain.question.dto.QuestionItemEntity;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainAttachment;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.dto.CollegeTrainExamRecordAnswer;
import com.htks.domain.student.dto.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface ExamRepository {

    /* 根据条件查询符合条件的所有考试   0：未考试 1：考试中 2：已考试*/
    List<ExamRecord> getAllExam(String studentNo, String date);

    /*  查询所选区域内所有上岗证考试    0：未考试 1：考试中 2：已考试*/
    List<ExamRecord> getExamByArea(String employeeNo, String area);

    /*  查询已经考试的上岗证考试*/
    List<ExamRecord> getPassExamByArea(String employeeNo,String area);

    /*<!--查看已经考试的试卷信息-->*/
    ExamRecord passedExam(String examNumber);

    /*获取试卷名称*/
    String getPostPaperName(Double id);

    /*查询除了上岗证已经考试的信息*/
    List<ExamRecord> getPassAllExam(String examNumber);
    String getPaperTypeByid(Long id);

    /*新增考试记录*/
    void addExamRecord(@Param("ER")ExamRecord exam,String employeeNo);

    void changeExamRecord(@Param("ER")ExamRecord exam,String employeeNo,Long id);
    /*新增考试记录1*/
    void addExamRecord1(@Param("ER")CollegeTrainExamRecord exam,String postInfoId);
    /*新增··考试答案以及附件记录*/
    void addExamAnswer(List<Answer> list);

    /*获取某个问题的答案附件*/
    Long getAttachmentIdByQuestionId(Long questionId);

    /*新增附件*/
    Long addAttachmentId(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment,@Param("employeeNo")String employeeNo);
    Integer getAttachmentIdSum(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment,@Param("employeeNo")String employeeNo,@Param("time")String time);
    Long getAttachmentId(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment,@Param("employeeNo")String employeeNo,@Param("time")String time);
    Long updateAttachmentById(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment,@Param("id")Long id);

   Long  getAttachmentIdByPath(String path);
   Long getExamId(Long id);
Long getPostExamId(Long id,String postId);
  Long   getExamIdByName(Long id,String examName);
   Integer getexamSum(Long id,String examName);
    /*获取当前学生所在部门下的所有区域*/
    String getAreaByDepartment(String employeeNo);

    /*获取除上岗证外考试试题*/
    PaperQuestionType getExamInformation1(String categoryItem);

    List<Long> examInformation2(Long id);
    Long examInformationByExamName(String name);
    Long examInformationByExamNameBydept(String name,String employeeNo);

    List<ExamQuestion> examInformation3(String questionType, List<Long> list, Integer number);

    List<ExamQuestion> examInformationByPaperId(String questionType, Long paperId, Integer number);

    String getFullScore(String type,String name);
    /*获取上岗证考试题目*/
    List<Long> getPostExam1(String employeeNo);

    List<ExamQuestion> getPostExam2(Long id);
    List<ExamQuestion> getPostExamByList(List<String>list);


    /*获取选项以及对应的答案*/
    List<QuestionItemEntity> getPostQuestionContent(Long id);
    String getAttendPath(long id);

    /*获取学生答案跟标准答案做比较*/
    String getAnswerById(Long questionId);

    /*获取试卷题目类型分布*/
    PaperQuestionType getQuestionNumber(String employeeNo, String categoryItem);

    PaperQuestionType getPostExam(String employeeNo,String postInfo);

    /*获取考试时间以添加考试记录*/
    ExamRecord  afterAddExamLog(String employeeNo, String categoryItem);

   /*    <!--    获取考试时间以添加上岗证考试记录-->*/
    ExamRecord afterAddPostExamLog(String employeeNo, String postInfoId);
 /*   <!--获取学生批次号-->*/
    String getCollegeBatch(String employeeNo);
    /*   <!--获取IQ测试试卷名-->*/
    long getIQExam(String paperName);
    String getFirstBatch(String value);
    Long getPaperIdByPaperName(String paperName);


 /*       @Insert(" INSERT INTO COLLEGE_TRAIN_ATTACHMENT\n ( ATTACHMENT_PATH, ATTACHMENT_TYPE, ATTACHMENT_NAME, ATTACHMENT_MEMO, POST_CARD_ID, QUESTION_ID)\n" +
                "        VALUES( #{CTA.attachmentPath}, #{CTA.attachmentType}, #{CTA.attachmentName}, #{CTA.attachmentMemo}, #{CTA.postCardId},\n" +
                "        #{CTA.questionId});")
        @Options(useGeneratedKeys = true, keyProperty = "ID")
        void insertYourObject(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment);
*/
  void   insertDemo(@Param("CTER")CollegeTrainExamRecordAnswer collegeTrainExamRecordAnswer);

  Long getExamCatyId(String categoryItem );
  String getExamType(String categoryItem);
    Integer getPostCountById(Long id,Long examID,String postId);

  Integer getPostCount(Long id,String postId);
    Long getId(String employeeNo);

  List<QuestDisposition> getQuestDisposition(String examName,String employeNo);
    List<QuestDisposition> getQuestDispositionByDepartment(String examName,String employeNo);

    void updateScore(String score1,String score2,String employeeNo,String examId);
  CollegeTrainAssessed getcollegeTrainAssessed(String no);
  String getArea(String employeeNo);
  String getScore(String employeeNo,String examId);
  String paperName(String id);

    String getDep(String employeeNo);
    String getCompany(String employeeNo);

}
