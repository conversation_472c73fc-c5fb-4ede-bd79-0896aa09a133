package com.htks.domain.student.dto;

import com.htks.domain.course.dto.Attachment;
import com.htks.domain.question.dto.QuestionItemEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter

//具体问题对象
public class ExamQuestion {
 /*   @ApiModelProperty(value = "考试工号")
    private Double employeeNo;*/

    @ApiModelProperty(value = "题目id")
    private Long questionId;

/*    @ApiModelProperty(value = "培训考试项目ID")
    private String examCategoryId;*/

/*    @ApiModelProperty(value = "试卷ID")
    private Double paperId;*/

    @ApiModelProperty(value = "试题类型 1 单选题\n" +
            "2 多选题\n" +
            "3 判断题\n" +
            "4 填空题\n" +
            "5 主观题")
    private String questionType;

    @ApiModelProperty(value = "标准答案, 存ABCD, 多选使用逗号隔开、填空题存正确答案, 主观,不填写")
    private String standardAnswer;


    @ApiModelProperty(value = "答案")
    private String answerText;


    @ApiModelProperty(value = "附件地址")
    private String attachmentPath;

    @ApiModelProperty(value = "选项列表")
    private List<QuestionItemEntity> questionItemEntityList;

    @ApiModelProperty(value = "题目详情")
    private String questionContent;

    @ApiModelProperty(value = "试卷名称")
    private String paperName;

/*    @ApiModelProperty(value = "本题分值")
    private String fullScore;*/
}
