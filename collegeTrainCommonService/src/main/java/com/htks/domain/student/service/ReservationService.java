package com.htks.domain.student.service;

import com.htks.domain.student.dto.AppointMent;
import com.htks.domain.student.dto.AppointTime;
import com.htks.domain.student.dto.Reservation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public interface ReservationService {
    Integer timeAppoitGet(String startDate, String endDate,Integer type, String startTime, String endTime,String companyType);

    List<AppointTime> allInformation(String no);

    Integer allNumber(String no,String companyType);
    Integer assessedNumber(Integer type,String employeeNo,String companyType);
    Integer judgePassed(String no,Integer type,<PERSON><PERSON>an passed);
    Integer judgePassedError(String no,Integer type);

    void addAppointment(AppointMent reservation, Long assessed_id);
    List<Reservation> showAppointment(String no,Integer type);
    Integer isCollegeIng(@Param("no")String no,@Param("type") Integer type);

}
