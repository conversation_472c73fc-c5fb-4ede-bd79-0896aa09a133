package com.htks.domain.student.service;


import com.htks.common.external.wx.ResultVO;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.domain.question.dto.QuestionItemEntity;
import com.htks.domain.school.dto.CollegeTrainAttachment;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.dto.CollegeTrainExamRecordAnswer;
import com.htks.domain.student.dto.Answer;
import com.htks.domain.student.dto.ExamQuestion;
import com.htks.domain.student.dto.ExamRecord;
import com.htks.domain.student.dto.PaperQuestionType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ExamService {
    /* <!--根据条件查询符合条件的所有考试   0：未考试 1：考试中 2：已考试-->*/
    List<ExamRecord> getAllExam(String studentNo, String date);

    /*  <!-- 查询所选区域内所有上岗证考试    0：未考试 1：考试中 2：已考试-->*/
    List<ExamRecord> getExamByArea(String studentNo, String date);

    String getPostPaperName(Double id);

    /*
     */
    /*<!--查看已经考试的试卷信息-->*/

    ExamRecord passedExam(String examNumber);

    /*<!--查询除了上岗证已经考试的信息-->*/
    List<ExamRecord> getPassAllExam(String examNumber);

    /*<!--新增考试记录-->*/
   // void addExamRecord(ExamRecord exam,String employeeNo);
    String getPaperTypeByid(Long id);

    /*新增考试答案等对应信息*/
    void addExamAnswer(List<Answer> list);

    /* <!--获取当前学生所在部门下的所有区域-->*/
    String getAreaByDepartment(String employeeNo);

    /*    获取考试试题-->*/
    PaperQuestionType getExamInformation1(String categoryItem);

    List<Long> examInformation2(Long id);

    List<ExamQuestion> examInformation3(String questionType, List<Long> list, Integer number);

    /* <!--   <获取选项以及对应的答案>-->*/
    List<QuestionItemEntity> getPostQuestionContent(Long id);
    String getAttendPath(long id);
    /*获取上岗证考试*/
    List<Long> getPostExam1(String employeeNo);

    List<ExamQuestion> getPostExam2(Long id);
    List<ExamQuestion> getPostExamByList(List<String>list);

    /*获取学生答案跟标准答案做比较*/
    String getAnswerById(Long questionId);

    /*  获取试卷题目分布*/
    PaperQuestionType getQuestionNumber(String employeeNo, String categoryItem);


    PaperQuestionType getPostExam(String employeeNo,String postInfo);
    /*  查询已经考试的上岗证考试*/
    List<ExamRecord> getPassExamByArea(String employeeNo,String area);
    /*新增附件*/
    Long addAttachmentId(CollegeTrainAttachment collegeTrainAttachment,String employeeNo);
    Integer getAttachmentIdSum(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment,@Param("employeeNo")String employeeNo,@Param("time")String time);
    Long updateAttachmentById(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment,@Param("id")Long id);
    Long getAttachmentId(@Param("CTA")CollegeTrainAttachment collegeTrainAttachment,@Param("employeeNo")String employeeNo,@Param("time")String time);

    Long  getAttachmentIdByPath(String path);

    Long getExamId(Long id );

    Long getExamIdByName(Long id,String examName);

    Long getPostExamId(Long id,String postId);
    /*获取某个问题的答案附件*/
    //Long getAttachmentIdByQuestionId(Long questionId);

    /*获取考试时间以添加考试记录*/
    ExamRecord  afterAddExamLog(String employeeNo, String categoryItem);

    /*    <!--    获取考试时间以添加上岗证考试记录-->*/
    ExamRecord afterAddPostExamLog(String employeeNo, String postInfoId);

    /*   <!--获取学生批次号-->*/
    String getCollegeBatch(String employeeNo);
    String getFirstBatch(String value);

    /*   <!--获取IQ测试试卷名-->*/
    long getIQExam(String paperName);

    //Long getPaperIdByPaperName(String paperName);


    Integer getexamSum(Long id,String examName);
    /*新增考试记录1*/
    void addExamRecord1(CollegeTrainExamRecord exam,String postInfoId);

    void changeExamRecord(ExamRecord exam,String employeeNo,Long id);

    void   insertDemo(@Param("CTER") CollegeTrainExamRecordAnswer collegeTrainExamRecordAnswer);


//获取考试项目id
    Long getExamCatyId(String categoryItem );

    String getExamType(String categoryItem);

    Integer getPostCount(Long id,String postId);

    ResultVO getOtherExamQuestion(String categoryItem,String employeeNo);

    void sortExamQuestions(List<ExamQuestion> questions);

}
