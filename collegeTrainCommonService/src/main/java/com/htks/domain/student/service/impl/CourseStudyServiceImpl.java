package com.htks.domain.student.service.impl;

import cn.hutool.core.date.DateUtil;
import com.htks.domain.course.dto.Attachment;
import com.htks.domain.student.dto.CourseStudy;
import com.htks.domain.student.repository.hana.CourseStudyRepository;
import com.htks.domain.student.service.CourseStudyService;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.poi.xslf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.color.ColorSpace;
import java.awt.geom.Rectangle2D;
import java.awt.image.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.List;

@Service
@Transactional
public class CourseStudyServiceImpl implements CourseStudyService {
    @Autowired
    CourseStudyRepository courseStudyRepository;

    @Override
    public Long getAccessId(String employeeNo) {
        return courseStudyRepository.getAccessId(employeeNo);
    }

    @Override
    public List<CourseStudy> getCourseId(Long id) {
        return courseStudyRepository.getCourseId(id);
    }

    @Override
    public List<CourseStudy> getDeadTime(Long assessedCourseId) {
        return courseStudyRepository.getDeadTime(assessedCourseId);
    }

    @Override
    public List<CourseStudy> getCourseDetail(Long id, String category,String departmentName) {
        return courseStudyRepository.getCourseDetail(id, category,departmentName);
    }

 /*   @Override
    public String getFilePath(Long attachmentId) {
        return courseStudyRepository.getFilePath(attachmentId);
    }*/

    @Override
    public void changeStudyStatus(String studyStatus, String completeTime,Long assessedId,Long courseId) {
        courseStudyRepository.changeStudyStatus(studyStatus, completeTime,assessedId,courseId);
    }

    @Override
    public void addStudyLog(Long courseItemId) {
        courseStudyRepository.addStudyLog(courseItemId);
    }

    @Override
    public Long getCourseItemId(Long assessedId, long courseId) {
        return courseStudyRepository.getCourseItemId(assessedId,courseId);
    }

    @Override
    public Integer getExamScore(Long id, String category) {
        return courseStudyRepository.getExamScore(id, category);
    }

    @Override
    public HashMap<String ,Object> getExamScoreList(String category) {
        return courseStudyRepository.getExamScoreList( category);
    }

    @Override
    public String getVideoUrl(Long id) {
        return courseStudyRepository.getVideoUrl(id);
    }

    @Override
    public List<Attachment> getVideoUrlList() {
        return courseStudyRepository.getVideoUrlList();
    }

    @Override
    public String getDepartmentName(String no) {
        return courseStudyRepository.getDepartmentName(no);
    }
    @Override
    public String getPracticeDepartment(String no) {
        return courseStudyRepository.getPracticeDepartment(no);
    }

    @Override
    public List<String> pdfToPicture(String pptFilePath) throws IOException {
        URL url = new URL(pptFilePath);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        InputStream inputStream = connection.getInputStream();

        XMLSlideShow ppt = new XMLSlideShow(inputStream);
      //  XMLSlideShow ppt = new XMLSlideShow(new FileInputStream(inputStream));
        Dimension dimension = ppt.getPageSize();
        inputStream.close();
        // 创建一个字节数组输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<String> imageByteArrays = new ArrayList<>();
        int pageSize = ppt.getSlides().size();
        // 遍历每个PPT页
        for (int i = 0; i < ppt.getSlides().size(); i++) {
            XSLFSlide slide = ppt.getSlides().get(i);

            // 创建一个BufferedImage对象，并为其设置宽、高和图像类型
            BufferedImage image = new BufferedImage(dimension.width, dimension.height, BufferedImage.TYPE_INT_RGB);

            // 创建一个Graphics2D对象，用于将幻灯片绘制到BufferedImage上
            Graphics2D graphics = image.createGraphics();
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 清除背景
            graphics.setPaint(Color.WHITE);
//// 设置支持中文字符的颜色模型
            graphics.fill(new Rectangle2D.Float(0, 0, dimension.width, dimension.height));
            ColorModel colorModel = new ComponentColorModel(ColorSpace.getInstance(ColorSpace.CS_sRGB),
                    true, false, Transparency.OPAQUE, DataBuffer.TYPE_BYTE);
            WritableRaster raster = colorModel.createCompatibleWritableRaster(dimension.width, dimension.height);
            image.setData(raster);
            // 绘制幻灯片
            slide.draw(graphics);

            // 将图片数据写入字节数组输出流
            ImageIO.write(image, "jpg", outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            String base64EncodedImage = Base64.getEncoder().encodeToString(imageBytes);
            imageByteArrays.add(base64EncodedImage);
            graphics.dispose();
            outputStream.reset();
        }
        ppt.close();
        return imageByteArrays;
    }


    private static void handlerPPTXEncoding(XMLSlideShow ppt, int index) {
        for (XSLFShape shape : ppt.getSlides().get(index).getShapes()) {
            if (shape instanceof XSLFTextShape) {
                XSLFTextShape tsh = (XSLFTextShape) shape;
                for (XSLFTextParagraph p : tsh) {
                    for (XSLFTextRun r : p) {
                        String fontFamily = r.getFontFamily();
                        fontFamily = "宋体";
                        r.setFontFamily(fontFamily);
                    }
                }
            }
        }
    }

}
