package com.htks.domain.student.repository.hana;


import com.htks.domain.course.dto.Attachment;
import com.htks.domain.school.dto.CollegeTrainUser;
import com.htks.domain.student.dto.CourseStudy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;


@Mapper
@Repository
public interface CourseStudyRepository {
    Long getAccessId(String employeeNo);

    List<CourseStudy> getCourseId(Long id);

    List<CourseStudy> getDeadTime(Long assessedCourseId);

    List<CourseStudy> getCourseDetail(Long id, String category,String departmentName);

    String getVideoUrl(Long id);

    List<Attachment> getVideoUrlList();

    void changeStudyStatus(String studyStatus, String completeTime,Long assessedId,Long courseId);

    void addStudyLog(@Param("courseItemId") Long courseItemId);

    Long getCourseItemId(Long assessedId,long courseId);

    Integer getExamScore(Long id, String category);

    HashMap<String ,Object> getExamScoreList(String category);

    String getDepartmentName(String no);
    String getPracticeDepartment(String no);
    String getRoleId(String no);
  List<CollegeTrainUser>  getJudge(String userRoleId);

  String getEmployeeName(String employeeNo);
}
