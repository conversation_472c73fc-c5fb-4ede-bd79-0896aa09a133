package com.htks.domain.student.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter

//考试题型分布
public class PaperQuestionType {
    @ApiModelProperty(value = "培训考试项目ID")
    private Long examCategoryId;


    @ApiModelProperty(value = "单选题题数 ")
    private Integer singleOptionNumber;
    @ApiModelProperty(value = "多选题题数 ")
    private Integer moreOptionNumber;
    @ApiModelProperty(value = "判断题题数 ")
    private Integer judgeNumbers;
    @ApiModelProperty(value = "填空题题数 ")
    private Integer fillNumber;
    @ApiModelProperty(value = "简答题题数 ")
    private Integer shortNumber;
    @ApiModelProperty(value = "单选题满分 ")
    private Double singleOptionAnswer;
    @ApiModelProperty(value = "多选题满分 ")
    private Double moreOptionAnswer;
    @ApiModelProperty(value = "判断题满分 ")
    private Double judgeAnswer;
    @ApiModelProperty(value = "填空题满分 ")
    private Double fillAnswer;
    @ApiModelProperty(value = "简答题满分 ")
    private Double shortAnswer;
    @ApiModelProperty(value = "考试时长")
    private String testTime;
    @ApiModelProperty(value = "考试开始时间")
    private String examStartTime;
}
