package com.htks.domain.student.service;

import com.htks.common.external.FileService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.student.dto.AppointTime;
import com.htks.domain.student.dto.PostCard;
import com.htks.domain.student.dto.PostCardTime;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.HttpURLConnection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public interface UploadPostCardService {

    List<String> getWorkLicense(String area,String employeeNo);
    List<String> getAreaList(String employeeNo);
    List<PostCard> findDepartmentCard(String area,String employeeNo);
    List<String> getWorkLicenseArea(String area,String employeeNo);
    List<PostCard> findAreaCard(String area,String employeeNo);

    String postUploadStartTime(String employeeNo);
    PostCardTime getPostTime(String employeeNo);

    String postUploadEndTime(String employeeNo);

    void uploadWorkLicense(String postArea,String postName, Long accessId,String batch,String postId);
    Long getPostCardId(String area,String postName,String no);
 /*   void uploadPostInfoCard(Long ASSESSED_ID, Long POST_INFO_ID);

    Long getPostInfoId(String POST_NAME);*/

    Long getWorkLicense21(String no);
    List<String> getWorkLicense22(Long id);
    List<String> getWorkLicense23(String area,List list);


    //上传上岗证图片
    long  addPostCardPicture(AttachmentEntity attachmentEntity,Long postCardId,Long  accessId) ;

/*
    public default String getFileNameFromContentDisposition(String disposition) {
        if (disposition != null && disposition.contains("filename=")) {
            int index = disposition.indexOf("filename=");
            String fileName = disposition.substring(index + 9)
                    .replace("\"", "")
                    .replace("'", "");

            return fileName;
        }

        return null;
    }
*/


/*    static String getFileNameFromHeaders(HttpURLConnection connection) {
        String fileName = null;
        String disposition = connection.getHeaderField("Content-Disposition");
        String contentType = connection.getContentType();

        if (disposition != null && disposition.contains("filename=")) {
            int index = disposition.indexOf("filename=");
            fileName = disposition.substring(index + 9)
                    .replace("\"", "")
                    .replace("'", "");
        } else if (contentType != null && contentType.contains("filename=")) {
            int index = contentType.indexOf("filename=");
            fileName = contentType.substring(index + 9)
                    .replace("\"", "")
                    .replace("'", "");
        }

        return fileName;
    }*/

}
