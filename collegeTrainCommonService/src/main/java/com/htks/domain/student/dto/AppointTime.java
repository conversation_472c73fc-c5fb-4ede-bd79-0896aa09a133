package com.htks.domain.student.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter

//实际操作以及异常预约时间
public class AppointTime {
    @ApiModelProperty(value = "实操预约开始")
    private String pStartTime;

    @ApiModelProperty(value = "实操预约结束")
    private String pEndTime;

    @ApiModelProperty(value = "异常预约开始")
    private String eStartTime;

    @ApiModelProperty(value = "异常预约结束")
    private String eEndTime;
}
