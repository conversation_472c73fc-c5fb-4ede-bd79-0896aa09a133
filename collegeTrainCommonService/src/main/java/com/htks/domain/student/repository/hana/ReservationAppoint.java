package com.htks.domain.student.repository.hana;


import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.dto.CommonEmployeeName;
import com.htks.domain.student.dto.AppointMent;
import com.htks.domain.student.dto.AppointTime;
import com.htks.domain.student.dto.Reservation;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;


@Mapper
@Repository
public interface ReservationAppoint {

    Integer timeAppoitGet(@Param("startDate")String startDate,@Param("endDate") String endDate,@Param("type")Integer type, @Param("startTime")String startTime,@Param("endTime") String endTime,@Param("employeeNo")String employeeNo);

    List<AppointTime> allInformation(String no);

    Integer assessedNumber(Integer type,String employeeNo,String companyType);

    Integer allNumber(String no,String companyType);

    Integer judgePassed(@Param("no")String no,@Param("type") Integer type, @Param("passed")Boolean passed);

    Integer judgePassedError(@Param("no")String no,@Param("type") Integer type);

    Integer isCollegeIng(@Param("no")String no,@Param("type") Integer type);

    void addAppointment(@Param("re") AppointMent reservation, Long assessed_id);

    List<Reservation> showAppointment(String no,Integer type);

    CommonEmployeeName getEmployeeInfo(String employeeNo);
}
