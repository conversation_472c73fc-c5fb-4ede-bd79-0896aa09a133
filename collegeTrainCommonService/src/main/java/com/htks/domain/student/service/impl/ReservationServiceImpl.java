package com.htks.domain.student.service.impl;

import com.htks.domain.student.dto.AppointMent;
import com.htks.domain.student.dto.AppointTime;
import com.htks.domain.student.dto.Reservation;
import com.htks.domain.student.repository.hana.ReservationAppoint;
import com.htks.domain.student.service.ReservationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional
public class ReservationServiceImpl implements ReservationService {

    @Resource
    public ReservationAppoint reservationRepository;

    @Override
    public Integer timeAppoitGet(String startDate, String endDate,Integer type, String startTime, String endTime,String companyType) {
        return reservationRepository.timeAppoitGet(startDate,endDate, type,startTime,endTime,companyType);
    }

    @Override
    public List<AppointTime> allInformation(String no) {
        return reservationRepository.allInformation(no);
    }

    @Override
    public Integer allNumber( String no,String s) {
        return reservationRepository.allNumber(no,s);
    }

    @Override
    public Integer assessedNumber( Integer type,String employeeNo,String companyType) {
        return reservationRepository.assessedNumber(type,employeeNo,companyType);
    }

    @Override
    public Integer judgePassed(String no,Integer type,Boolean passed) {
        return reservationRepository.judgePassed(no,type,passed);
    }

    @Override
    public Integer judgePassedError(String no, Integer type) {
        return reservationRepository.judgePassedError(no,type);
    }


    @Override
    public void addAppointment(AppointMent reservation, Long assessed_id) {
        reservationRepository.addAppointment(reservation, assessed_id);
    }

    @Override
    public List<Reservation> showAppointment(String no,Integer type) {
        return reservationRepository.showAppointment(no,type);
    }

    @Override
    public Integer isCollegeIng(String no, Integer type) {
        return reservationRepository.isCollegeIng(no, type);
    }
}
