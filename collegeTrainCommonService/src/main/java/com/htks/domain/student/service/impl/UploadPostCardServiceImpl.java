package com.htks.domain.student.service.impl;

import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.FileService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.student.dto.AppointTime;
import com.htks.domain.student.dto.PostCard;
import com.htks.domain.student.dto.PostCardTime;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.domain.student.service.SatisfactionDegreeService;
import com.htks.domain.student.service.UploadPostCardService;

import io.swagger.annotations.ApiOperation;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class UploadPostCardServiceImpl implements UploadPostCardService {
    @Autowired
    private UploadWorkLicense uploadWorkLicense;

    @Override
    public List<String> getWorkLicense(String area,String employeeNo) {
        return uploadWorkLicense.getWorkLicense(area, employeeNo);
    }

    @Override
    public List<String> getAreaList(String employeeNo) {
        return uploadWorkLicense.getAreaList(employeeNo);
    }

    @Override
    public List<PostCard> findDepartmentCard(String area,String employeeNo) {
        return uploadWorkLicense.findDepartmentCard(area, employeeNo);
    }

    @Override
    public List<String> getWorkLicenseArea(String area, String employeeNo) {
        return uploadWorkLicense.getWorkLicenseArea(area, employeeNo);
    }

    @Override
    public List<PostCard> findAreaCard(String area,String employeeNo) {
        return uploadWorkLicense.findAreaCard(area, employeeNo);
    }

    @Override
    public String postUploadStartTime(String employeeNo) {
        return uploadWorkLicense.postUploadStartTime( employeeNo);
    }

    @Override
    public PostCardTime getPostTime(String employeeNo) {
        return uploadWorkLicense.getPostTime(employeeNo);
    }

    @Override
    public String postUploadEndTime(String employeeNo) {
        return uploadWorkLicense.postUploadEndTime( employeeNo);
    }

    @Override
    public void uploadWorkLicense(String postArea,String postName, Long accessId,String batch,String postId) {
         uploadWorkLicense.uploadWorkLicense(postArea,postName,accessId,batch,postId);
    }

    @Override
    public Long getPostCardId(String area, String postName,String no) {
        return uploadWorkLicense.getPostCardId(area,postName,no);
    }

 /*   @Override
    public void uploadPostInfoCard(Long ASSESSED_ID, Long POST_INFO_ID) {
         uploadWorkLicense.uploadPostInfoCard(ASSESSED_ID,POST_INFO_ID);
    }

    @Override
    public Long getPostInfoId(String POST_NAME) {
        return uploadWorkLicense.getPostInfoId(POST_NAME);
    }*/




//    查询优化测试
    @Override
    public Long getWorkLicense21(String no) {
        return uploadWorkLicense.getWorkLicense21(no);
    }

    @Override
    public List<String> getWorkLicense22(Long id) {
        return uploadWorkLicense.getWorkLicense22(id);
    }

    @Override
    public List<String> getWorkLicense23(String area,List list) {
        return uploadWorkLicense.getWorkLicense23(area,list);
    }

    @Override
    public long addPostCardPicture(AttachmentEntity attachmentEntity,Long postCardId,Long accessId) {
       return uploadWorkLicense.addPostCardPicture(attachmentEntity,postCardId, accessId);
    }


}
