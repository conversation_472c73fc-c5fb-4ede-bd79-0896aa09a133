package com.htks.domain.student.service;


import com.htks.domain.course.dto.Attachment;
import com.htks.domain.student.dto.CourseStudy;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

@Service
public interface CourseStudyService {
    Long getAccessId(String employeeNo);

    List<CourseStudy> getCourseId(Long id);

    List<CourseStudy> getDeadTime(Long assessedCourseId);

    List<CourseStudy> getCourseDetail(Long id, String category,String departmentName);

/*    String getFilePath(Long attachmentId);*/

    void changeStudyStatus(String studyStatus, String completeTime,Long assessedId,Long courseId);

    void addStudyLog(Long courseItemId);
    Long getCourseItemId(Long assessedId,long courseId);

    Integer getExamScore(Long id, String category);
    HashMap<String ,Object> getExamScoreList(String category);

    String getVideoUrl(Long id);
    List<Attachment> getVideoUrlList();
    String getDepartmentName(String no);
    String getPracticeDepartment(String no);
    List<String> pdfToPicture(String filePath) throws IOException;
}
