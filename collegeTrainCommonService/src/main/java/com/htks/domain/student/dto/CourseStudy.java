package com.htks.domain.student.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Date;

@Getter
@Setter

//学生学习进度
public class CourseStudy {

    @ApiModelProperty(value = "培训类别")
    private String category;

    @ApiModelProperty(value = "截止时间")
    private String deadTime;

    @ApiModelProperty(value = "开始时间")
    private String createdTime;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

   /* @ApiModelProperty(value = "学习进度")
    private String studyProgress;*/

    @ApiModelProperty(value = "考试进度")
    private String examProgress;

    @ApiModelProperty(value = "课程状态")
    private String studyStatus;

    @ApiModelProperty(value = "课程标题")
    private String title;

/*    @ApiModelProperty(value = "课程材料")
    private Long attachmentId;*/

    @ApiModelProperty(value = "课程材料地址")
    private String attachmentPath;
    @ApiModelProperty(value = "课程材料地址")
    private String videoUrl;
/*    @ApiModelProperty(value = "课程材料地址id")
    private String filePathNo;*/

    @ApiModelProperty(value = "课程完成时间")
    private String completeTime;


    @ApiModelProperty(value = "历史最高成绩")
    private Integer maxScore;
}
