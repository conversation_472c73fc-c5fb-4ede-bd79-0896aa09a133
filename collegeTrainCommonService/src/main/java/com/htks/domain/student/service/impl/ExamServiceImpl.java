package com.htks.domain.student.service.impl;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.domain.question.dto.QuestionItemEntity;
import com.htks.domain.school.dto.CollegeTrainAttachment;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.dto.CollegeTrainExamRecordAnswer;
import com.htks.domain.student.dto.*;
import com.htks.domain.student.repository.hana.ExamRepository;
import com.htks.domain.student.service.ExamService;
import com.htks.domain.student.service.SatisfactionDegreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class ExamServiceImpl implements ExamService {
    @Autowired
    ExamRepository examRepository;

    @Autowired
    private SatisfactionDegreeService satisfactionDegreeService;

    @Override
    public List<ExamRecord> getAllExam(String studentNo ,String date) {
        return examRepository.getAllExam(studentNo,date);
    }

    @Override
    public List<ExamRecord> getExamByArea(String studentNo ,String date) {
        return examRepository.getExamByArea(studentNo,date);
    }

    @Override
    public String getPostPaperName(Double id) {
        return examRepository.getPostPaperName(id);
    }

    @Override
    public List<ExamRecord> getPassAllExam(String examNumber) {
        return examRepository.getPassAllExam(examNumber);
    }

    @Override
    public ExamRecord passedExam(String examNumber) {
        return examRepository.passedExam(examNumber);
    }

/*
    @Override
    public void addExamRecord(ExamRecord exam,String employeeNo) {
        examRepository.addExamRecord(exam,employeeNo);
    }
*/

    @Override
    public String getPaperTypeByid(Long id) {
        return examRepository.getPaperTypeByid(id);
    }

    @Override
    public void addExamAnswer(List<Answer>list) { examRepository.addExamAnswer(list); }

    @Override
    public String getAreaByDepartment(String employeeNo) {
        return examRepository.getAreaByDepartment(employeeNo);
    }


   @Override
    public PaperQuestionType getExamInformation1(String categoryItem) {
        return examRepository.getExamInformation1(categoryItem);
    }

    @Override
    public List<Long> examInformation2(Long id) {
        return examRepository.examInformation2(id);
    }

    @Override
    public List<ExamQuestion> examInformation3(String questionType, List<Long> list, Integer number) {
        return examRepository.examInformation3(questionType, list, number);
    }

    @Override
    public List<QuestionItemEntity> getPostQuestionContent(Long id) {
        return examRepository.getPostQuestionContent(id);
    }

    @Override
    public String getAttendPath(long id) {
        return examRepository.getAttendPath(id);
    }

    @Override
    public   List<Long> getPostExam1(String employeeNo) {
        return examRepository.getPostExam1(employeeNo);
    }


    @Override
    public List<ExamQuestion> getPostExam2(Long id) {
        return examRepository.getPostExam2(id);
    }

    @Override
    public List<ExamQuestion> getPostExamByList(List<String>listd) {
        return examRepository.getPostExamByList(listd);
    }

    @Override
    public String getAnswerById(Long questionId) {
        return examRepository.getAnswerById(questionId);
    }

    @Override
    public PaperQuestionType getQuestionNumber(String employeeNo, String categoryItem) {
        return examRepository.getQuestionNumber( employeeNo,categoryItem);
    }

    @Override
    public PaperQuestionType getPostExam(String employeeNo, String postInfo) {
        return examRepository.getPostExam(employeeNo, postInfo);
    }

    @Override
    public List<ExamRecord> getPassExamByArea(String employeeNo,String area) {
        return examRepository.getPassExamByArea(employeeNo,area);
    }

    @Override
    public Long addAttachmentId(CollegeTrainAttachment collegeTrainAttachment,String employeeNo) {
       return examRepository.addAttachmentId(collegeTrainAttachment, employeeNo);
    }

    @Override
    public Integer getAttachmentIdSum(CollegeTrainAttachment collegeTrainAttachment, String employeeNo, String time) {
        return examRepository.getAttachmentIdSum(collegeTrainAttachment, employeeNo, time);
    }

    @Override
    public Long updateAttachmentById(CollegeTrainAttachment collegeTrainAttachment, Long id) {
        return examRepository.updateAttachmentById(collegeTrainAttachment, id);
    }

    @Override
    public Long getAttachmentId(CollegeTrainAttachment collegeTrainAttachment, String employeeNo, String time) {
        return examRepository.getAttachmentId(collegeTrainAttachment, employeeNo, time);
    }

    @Override
    public Long getAttachmentIdByPath(String path) {
        return examRepository.getAttachmentIdByPath(path);
    }

    @Override
    public Long getExamId(Long id ) {
        return examRepository.getExamId(id);
    }

    @Override
    public Long getExamIdByName(Long id, String examName) {
        return examRepository.getExamIdByName(id,examName);
    }

    @Override
    public Long getPostExamId(Long id, String postId) {
        return examRepository.getPostExamId(id, postId);
    }

/*    @Override
    public Long getAttachmentIdByQuestionId(Long questionId) {
        return examRepository.getAttachmentIdByQuestionId(questionId);
    }*/

    @Override
    public ExamRecord afterAddExamLog(String employeeNo, String categoryItem) {
        return examRepository.afterAddExamLog(employeeNo,categoryItem);
    }

    @Override
    public ExamRecord afterAddPostExamLog(String employeeNo, String postInfoId) {
        return examRepository.afterAddPostExamLog(employeeNo,postInfoId);
    }

    @Override
    public String getCollegeBatch(String employeeNo) {
        return examRepository.getCollegeBatch(employeeNo);
    }

    @Override
    public String getFirstBatch(String value) {
        return examRepository.getFirstBatch(value);
    }

    @Override
    public long getIQExam(String paperName) {
        return examRepository.getIQExam(paperName);
    }

/*
    @Override
    public Long getPaperIdByPaperName(String paperName) {
        return examRepository.getIQExam(paperName);
    }
*/

    @Override
    public Integer getexamSum(Long id, String examName) {
        return examRepository.getexamSum(id,examName);
    }

    @Override
    public void addExamRecord1(CollegeTrainExamRecord exam,String postInfoId) {
        examRepository.addExamRecord1(exam,postInfoId);
    }

    @Override
    public void changeExamRecord(ExamRecord exam,String employeeNo,Long id) {
        examRepository.changeExamRecord( exam,employeeNo,id);
    }

    @Override
    public void insertDemo(CollegeTrainExamRecordAnswer collegeTrainExamRecordAnswer) {
        examRepository.insertDemo(collegeTrainExamRecordAnswer);
    }

    @Override
    public Long getExamCatyId(String categoryItem) {
        return examRepository.getExamCatyId(categoryItem);
    }

    @Override
    public String getExamType(String categoryItem) {
        return examRepository.getExamType(categoryItem);
    }

    @Override
    public Integer getPostCount(Long id, String postId) {
        return examRepository.getPostCount(id, postId);
    }

    @Override
    public ResultVO getOtherExamQuestion(String categoryItem, String employeeNo) {
        try {
            CollegeTrainExamRecord exam=new CollegeTrainExamRecord();
            List<ExamQuestion> li = new ArrayList<>();
            long examId=1;
            PaperQuestionType question = getExamInformation1(categoryItem);
            if(categoryItem.contains("IQ测试")){
                Date timeToday = new Date();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
                String currentTime2 = simpleDateFormat.format(timeToday);
                String firstBatch=getFirstBatch(currentTime2);
                String batch=getCollegeBatch(employeeNo);
                if(batch.equals(firstBatch)){
                    examId=getIQExam("IQ测试题A卷");
                    long id=1;
                    exam.setExamCategoryId(id);
                }else {
                    examId=getIQExam("IQ测试题B卷");
                    long id=1;
                    exam.setExamCategoryId(id);
                }
                li.addAll(getPostExam2(examId));
                for (int i = 0; i < li.size(); i++) {
                    li.get(i).setAttachmentPath(getAttendPath(li.get(i).getQuestionId()));
                    li.get(i).setQuestionItemEntityList(getPostQuestionContent(li.get(i).getQuestionId()));
                    int count=0;
                    switch (li.get(i).getQuestionType()){
                        case "填空题":
                            List<QuestionItemEntity> list=new ArrayList<>();
                            for (int j = 0; j < 20; j++) {
                                QuestionItemEntity questionItemEntity=new QuestionItemEntity();
                                list.add(questionItemEntity);
                            }
                            li.get(i).setQuestionItemEntityList(list);
                            break;
                    }
                }

            }else {
                exam.setExamCategoryId(getExamCatyId(categoryItem));
                if(question.getSingleOptionNumber()==null||question.equals("")){
                    question.setSingleOptionNumber(0);
                }
                if(question.getMoreOptionNumber()==null||question.equals("")){
                    question.setMoreOptionNumber(0);
                }
                if(question.getJudgeNumbers()==null||question.equals("")){
                    question.setJudgeNumbers(0);
                }
                if(question.getFillNumber()==null||question.equals("")){
                    question.setFillNumber(0);
                }
                if(question.getShortNumber()==null||question.equals("")){
                    question.setShortNumber(0);
                }
                List<QuestDisposition> questDispositions=new ArrayList<>();
                if (categoryItem.contains("工艺公开课理论考试")||categoryItem.contains("英语测试")||categoryItem.contains("IQ测试")||categoryItem.contains("HR公开课理论考试")){
                    questDispositions=examRepository.getQuestDisposition(categoryItem,employeeNo);

                }else {
                    questDispositions=examRepository.getQuestDispositionByDepartment(categoryItem,employeeNo);
                }

                if (questDispositions==null&&questDispositions.size()==0){
                    return new ResultVO(1001, "该考试抽取题目规则不存在，请联系培训专员");
                }
                List<QuestDisposition> finalQuestDispositions = questDispositions;
                for (int i = 0; i < questDispositions.size(); i++) {
                    Long paperId = null;
                    if(categoryItem.contains("BU公共课")||categoryItem.contains("工程结业")){
                        paperId = examRepository.examInformationByExamNameBydept(questDispositions.get(i).getCourseTitle(),employeeNo);
                        if (paperId==null){
                            paperId = examRepository.examInformationByExamName(questDispositions.get(i).getCourseTitle());
                        }
                    }else {
                         paperId = examRepository.examInformationByExamName(questDispositions.get(i).getCourseTitle());
                    }
                    int finalI = i;
                    Integer count = Math.toIntExact(li.stream()
                            .filter(user -> finalQuestDispositions.get(finalI).getQuestionType().equals(user.getQuestionType()))
                            .count());
                    switch (questDispositions.get(i).getQuestionType()){
                        case "单选":
                            if (count<question.getSingleOptionNumber()) {
                                li.addAll(examRepository.examInformationByPaperId(questDispositions.get(i).getQuestionType()+"题", paperId, questDispositions.get(i).getQuestionNumber()));

                            }
                            break;
                        case "多选":if (count<question.getMoreOptionNumber()) {
                            li.addAll(examRepository.examInformationByPaperId(questDispositions.get(i).getQuestionType() + "题", paperId, questDispositions.get(i).getQuestionNumber()));

                        }
                            break;
                        case "判断":if (count<question.getJudgeNumbers()) {
                            li.addAll(examRepository.examInformationByPaperId(questDispositions.get(i).getQuestionType() + "题", paperId, questDispositions.get(i).getQuestionNumber()));

                        }
                            break;
                        case "填空":if (count<question.getFillNumber()) {
                            li.addAll(examRepository.examInformationByPaperId(questDispositions.get(i).getQuestionType() + "题", paperId, questDispositions.get(i).getQuestionNumber()));

                        }
                            break;
                        case "简答":if (count<question.getShortNumber()) {

                            li.addAll(examRepository.examInformationByPaperId(questDispositions.get(i).getQuestionType() + "题", paperId, questDispositions.get(i).getQuestionNumber()));

                        }
                            break;
                    }
                    // if (count<question.getFillNumber()) {
                    //       }
                }
                Map<String, List<ExamQuestion>> map = li.stream().collect(Collectors.groupingBy(ExamQuestion::getQuestionType));
                li.clear();
                for (Map.Entry<String, List<ExamQuestion>> m : map.entrySet()) {
                    switch (m.getKey()){
                        case "单选题":
                            if (m.getValue().size()>question.getSingleOptionNumber()){
                                int a=m.getValue().size();
                                for (int i = 0; i < a-question.getSingleOptionNumber(); i++) {
                                    if (m.getValue().size()>question.getSingleOptionNumber()){
                                        m.getValue().remove(i);
                                    }
                                }
                            }if (m.getValue().size()<question.getSingleOptionNumber()){
                            List<Long> id = examInformation2(question.getExamCategoryId());
                            m.getValue().addAll(examInformation3("单选题", id, (question.getSingleOptionNumber()-m.getValue().size())));
                        }
                            li.addAll(m.getValue());
                            break;
                        case "多选题":
                            if (m.getValue().size()>question.getMoreOptionNumber()){
                                int a=m.getValue().size();
                                for (int i = 0; i < a-question.getMoreOptionNumber(); i++) {
                                    m.getValue().remove(i);
                                }
                            }if (m.getValue().size()<question.getMoreOptionNumber()){
                            List<Long> id = examInformation2(question.getExamCategoryId());
                            m.getValue().addAll(examInformation3("多选题", id, (question.getMoreOptionNumber()-m.getValue().size())));
                        }
                            li.addAll(m.getValue());
                            break;
                        case "判断题":
                            if (m.getValue().size()>question.getJudgeNumbers()){
                                int a=m.getValue().size();
                                for (int i = 0; i < a-question.getJudgeNumbers(); i++) {
                                    m.getValue().remove(i);
                                }
                            }if (m.getValue().size()<question.getJudgeNumbers()){
                            List<Long> id = examInformation2(question.getExamCategoryId());
                            m.getValue().addAll(examInformation3("判断题", id, (question.getJudgeNumbers()-m.getValue().size())));
                        }
                            li.addAll(m.getValue());
                            break;
                        case "填空题":
                            if (m.getValue().size()>question.getFillNumber()){
                                int a=m.getValue().size();
                                for (int i = 0; i < a-question.getFillNumber(); i++) {
                                    m.getValue().remove(i);
                                }
                            }if (m.getValue().size()<question.getFillNumber()){
                            List<Long> id = examInformation2(question.getExamCategoryId());
                            m.getValue().addAll(examInformation3("填空题", id, (question.getFillNumber()-m.getValue().size())));
                        }
                            li.addAll(m.getValue());
                            break;
                        case "简答题":
                            if (m.getValue().size()>question.getShortNumber()){
                                int a=m.getValue().size();
                                for (int i = 0; i < a-question.getShortNumber(); i++) {
                                    m.getValue().remove(i);
                                }
                            }if (m.getValue().size()<question.getShortNumber()){
                            List<Long> id = examInformation2(question.getExamCategoryId());
                            m.getValue().addAll(examInformation3("简答题", id, (question.getShortNumber()-m.getValue().size())));
                        }
                            li.addAll(m.getValue());
                            break;
                    }
                }
           /*     List<Long> id = examInformation2(question.getExamCategoryId());
                li.addAll(examInformation3("单选题", id, question.getSingleOptionNumber()));
                li.addAll(examInformation3("多选题", id, question.getMoreOptionNumber()));
                li.addAll(examInformation3("判断题", id, question.getJudgeNumbers()));
                li.addAll(examInformation3("填空题", id, question.getFillNumber()));
                li.addAll(examInformation3("简答题", id, question.getShortNumber()));*/
                for (int i = 0; i < li.size(); i++) {
                    //sql查询优化
                    int count=0;
                    li.get(i).setQuestionItemEntityList(getPostQuestionContent(li.get(i).getQuestionId()));
                    switch (li.get(i).getQuestionType()){
                        case "填空题":
                            if(li.get(i).getQuestionContent().endsWith("_")){
                                count = li.get(i).getQuestionContent().split("_").length;
                            }else{
                                count = li.get(i).getQuestionContent().split("_").length - 1;
                            }
                            if (count==0){
                                count=li.get(i).getQuestionContent().split("\\(").length-1;
                            }
                            if (count==0){
                                count=1;
                            }else {
                                count=1;
                            }
                            List<QuestionItemEntity> list=new ArrayList<>();
                            for (int j = 0; j < count; j++) {
                                QuestionItemEntity questionItemEntity=new QuestionItemEntity();
                                list.add(questionItemEntity);
                            }
                            li.get(i).setQuestionItemEntityList(list);
                            break;
                    }
                }
            }

            Long access_id = satisfactionDegreeService.getAccessId(employeeNo);
            Date timeToday = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            String currentTime2 = simpleDateFormat.format(timeToday);
            exam.setAssessedId(access_id);
            exam.setExamName(categoryItem+currentTime2);
            exam.setExamType(getExamType(categoryItem));
            sortExamQuestions(li);
            return new ResultVO(1000, "查询成功", li);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }

    public void sortExamQuestions(List<ExamQuestion> questions) {
        // 定义 questionType 的期望顺序
        String[] order = {"单选题", "多选题", "判断题", "填空题", "主观题"};

        // 使用自定义比较器对列表进行排序
        Collections.sort(questions, new Comparator<ExamQuestion>() {
            @Override
            public int compare(ExamQuestion q1, ExamQuestion q2) {
                String type1 = q1.getQuestionType();
                String type2 = q2.getQuestionType();
                return Integer.compare(indexOf(type1, order), indexOf(type2, order));
            }

            // 获取 questionType 在顺序数组中的索引的辅助方法
            private int indexOf(String str, String[] arr) {
                for (int i = 0; i < arr.length; i++) {
                    if (arr[i].equals(str)) {
                        return i;
                    }
                }
                return -1; // 如果未找到，默认为末尾
            }
        });
    }
}
