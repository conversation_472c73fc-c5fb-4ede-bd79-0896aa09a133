package com.htks.domain.student.dto;


import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
//考试记录
public class ExamRecord {
    private Long id;

    @ApiModelProperty(value = "培训考试项目ID")
    private Long examCategoryId;

    @ApiModelProperty(value = "培训考试项目名称")
    private String categoryItem;

    @ApiModelProperty(value = "考试开始时间")
    private String examStartTime;

    @ApiModelProperty(value = "考试结束时间")
    private String examEndTime;

    @ApiModelProperty(value = "考试名称")
    private String examName;

    @ApiModelProperty(value = "考试编号")
    private String examNumber;

    @ApiModelProperty(value = "考试状态 0：未考试 1：考试中 2：已考试且通过 3：已考试不通过")
    private String examStatus;

    /*@ApiModelProperty(value = "部门")
    private String department;*/

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "考试时长")
    private String testTime;

    @ApiModelProperty(value = "试卷类型")
    private String paperType;

    @ApiModelProperty(value = "试卷名称")
    private String paperName;

    @ApiModelProperty(value = "是否及格")
    private String isPassed;

    @ApiModelProperty(value = "主观题得分")
    private String subjectiveScore;

    @ApiModelProperty(value = "客观题得分")
    private Double objectiveScore;

    @ApiModelProperty(value = "总分")
    private Double totalScore;

    @ApiModelProperty(value = "上岗证明细表ID COLLEGE_TRAIN_POST_INFO")
    private String postInfoId;

    @ApiModelProperty(value = "试卷编号")
    private String paperNumber;

    @ApiModelProperty(value = "考试类型")
    private String examType;

 /*   @ApiModelProperty(value = "分配状态 已分配：未分配 ")
    private String allocationStatus;*/

    @ApiModelProperty(value = "单选题满分 ")
    private Integer singleOptionAnswer;
    @ApiModelProperty(value = "多选题满分 ")
    private Integer moreOptionAnswer;
    @ApiModelProperty(value = "判断题满分 ")
    private Integer judgeAnswer;
    @ApiModelProperty(value = "填空题满分 ")
    private Integer fillAnswer;
    @ApiModelProperty(value = "简答题满分 ")
    private Integer shortAnswer;
    @ApiModelProperty(value = "实际考试开始时间 ")
    private String createdTime;
    @ApiModelProperty(value = "实际考试结束时间 ")
    private String updatedTime;
    @ApiModelProperty(value ="是否核心 ")
    private String isCore;

}
