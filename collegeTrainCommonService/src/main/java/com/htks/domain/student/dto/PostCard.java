package com.htks.domain.student.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
//上岗证
public class PostCard {
    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "工号")
    private String STUDENT_NO;


    @ApiModelProperty(value = "区域")
    private String postArea;

    @ApiModelProperty(value = "上岗证名称及编号")
    private String postName;

    @ApiModelProperty(value = "上岗证图片地址")
    private String path;

    private String batch;
}
