package com.htks.domain.student.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class Answer {
    @ApiModelProperty(value = "学生工号")
    private String employeeNo;

    @ApiModelProperty(value = "题目id")
    private Long questionId;


    @ApiModelProperty(value = "试卷ID")
    private Double paperId;

    @ApiModelProperty(value = "培训考试项目名称")
    private String categoryItem;

    @ApiModelProperty(value = "试题类型 1 单选题\n" +
            "2 多选题\n" +
            "3 判断题\n" +
            "4 填空题\n" +
            "5 简答题")
    private String questionType;

    @ApiModelProperty(value = "分数")
    private Double totalScore;

    @ApiModelProperty(value = "考试id")
    private Long examId;

    @ApiModelProperty(value = "答案")
    private String answerText;

    @ApiModelProperty(value = "附件ID")
    private Long attachmentId;

}
