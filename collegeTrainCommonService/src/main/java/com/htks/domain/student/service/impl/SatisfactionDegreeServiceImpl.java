package com.htks.domain.student.service.impl;


import com.htks.domain.AbstractBaseServiceImpl;
import com.htks.domain.student.dto.SatisfactionDegree;
import com.htks.domain.student.repository.hana.SatisfactionDegreeRepository;
import com.htks.domain.student.service.SatisfactionDegreeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SatisfactionDegreeServiceImpl extends AbstractBaseServiceImpl implements SatisfactionDegreeService {
    @Autowired
    private SatisfactionDegreeRepository repository;

    @Override
    public void inserDegree(SatisfactionDegree satisfactionDegree,Long ASSESSED_ID) {
        repository.inserDegree(satisfactionDegree,ASSESSED_ID);
    }

    @Override
    public String onboardingTime(String employeeNo) {
        return repository.onboardingTime(employeeNo);
    }

    @Override
    public Integer appraiseIsNull(String employeeNo) {
        return repository.appraiseIsNull(employeeNo);

    }

    @Override
    public Date getThisWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    @Override
    public Long getAccessId(String STUDENT_ID) {
        return repository.getAccessId(STUDENT_ID);
    }

    @Override
    public Integer judgeIsfirst(Long id) {
        return repository.judgeIsfirst(id);
    }

   /* @Override
    public void updateDegree(SatisfactionDegree satisfactionDegree) {
//        repository.updateDegree(satisfactionDegree);
    }*/

    @Override
    public String newDate(String employeeNo) {
        return repository.newDate(employeeNo);
    }

    @Override
    public Integer getNewScore(String employeeNo) {
        return repository.getNewScore(employeeNo);
    }



}
