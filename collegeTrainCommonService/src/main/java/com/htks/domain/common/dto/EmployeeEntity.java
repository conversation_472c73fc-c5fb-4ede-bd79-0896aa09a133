package com.htks.domain.common.dto;

import com.google.common.base.MoreObjects;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 公共 - 员工信息
 *
 * <AUTHOR>
 * @date 2022/10/9.
 */
@Getter
@Setter
public class EmployeeEntity implements Serializable {

    private static final long serialVersionUID = -1614129075974273525L;

    /**
     * 姓名
     */
    private String employeeName;

    /**
     * 工号
     */
    private String employeeNumber;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信id
     */
    private String userWeChatId;

    /**
     * 职等
     */
    private String  positionGrade;

    /**
     * 职务
     */
    private String  positionName;

    /**
     * 直属上级工号
     */
    private String firstSupervisorNumber;

    /**
     * 性别
     */
    private String gender;

    /**
     * 事业部名称
     */
    private String businessUnitName;

    /**
     * 事业部ID
     */
    private String businessUnitId;

    /**
     * 区/组名称
     */
    private String classBanName;

    /**
     * 区/组ID
     */
    private String classBanId;

    /**
     * 职能名称
     */
    private String znName;

    /**
     * 职能ID
     */
    private String znId;

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("employeeName", employeeName)
                .add("employeeNumber", employeeNumber)
                .add("departmentName", departmentName)
                .add("departmentId", departmentId)
                .add("positionGrade", positionGrade)
                .add("positionName", positionName)
                .add("positionName", businessUnitName)
                .add("positionName", businessUnitId)
                .add("positionName", classBanName)
                .add("positionName", classBanId)
                .add("positionName", znName)
                .add("positionName", znId)
                .toString();
    }
}
