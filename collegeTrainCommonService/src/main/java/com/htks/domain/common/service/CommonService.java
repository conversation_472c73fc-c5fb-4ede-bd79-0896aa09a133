package com.htks.domain.common.service;

import com.htks.domain.common.dto.*;
import com.htks.domain.student.dto.FileEntry;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 公共数据业务service层
 *
 * <AUTHOR>
 * @date 2022/9/21.
 */
public interface CommonService {

    /**
     * 查询 - 参数下拉框选项数据
     *
     * @param paramType 参数类型
     * @return 选项数据
     */
    List<Map<String, String>> getParameterDropDown(String paramType);

    /**
     * 查询 - 员工基本信息
     *
     * @param employeeNumber 员工工号
     * @return 员工基本信息
     */
    EmployeeEntity getEmployeeBaseInfo(String employeeNumber);

    /**
     * 查询 - 员工基本信息
     *
     * @param weChatId 企业微信id
     * @return 员工基本信息
     */
    EmployeeEntity getEmployeeBaseInfoByWx(String weChatId);

    /**
     * 查询 - 字典表根据类型查询值
     *
     * @param type 类型
     * @return 同类型数据列表
     */
    List<UpgradeDictionaryEntity> getDictionary(String type);

    /**
     * 查询 - 职能工作技能字典表根据类型查询值
     *
     * @param znId 职能ID
     * @return 同类型数据列表
     */
    List<UpgradeBuZnSkillEntity> getSkillDictionary(Long znId);

    /**
     * 查询 - 职能工作技能字典表根据类型查询值
     *
     * @param znId 职能ID
     * @return 同类型数据列表
     */
    List<SkillGroupDicEntity> getSkillGroupDictionary(Long znId,String scoreType);

    /**
     * 查询 - 全部事业部
     *
     * @return 全部事业部
     */
    List<UpgradeBusinessUnitEntity> getBusinessUnit();

    /**
     * 查询 - 部门
     *
     * @param businessUnitId 事业部ID
     * @return 全部事业部
     */
    List<UpgradeDepartmentEntity> getDepartment(Long businessUnitId);

    /**
     * 查询 - 区/组
     *
     * @param departmentId 部门ID
     * @return 全部事业部
     */
    List<UpgradeClassBanEntity> getClassBan(Long departmentId);

    /**
     * 查询 - 职能
     *
     * @param classBanId 区/组ID
     * @return 全部事业部
     */
    List<UpgradeZnEntity> getZn(Long classBanId);


    /**
     * 保存 - 附件信息到数据库
     *
     * @param attachmentEntity 附件信息
     * @return 全部事业部
     */
    Boolean addAttachmentInfoToDB(AttachmentEntity attachmentEntity);


    List<CommonRoleEntity> getCommonRoleInfo(String systemIdentifier);

    List<AttachmentEntity> getAttachmentByAnswerId(Long answerId);


    String getEmployeeNoByUserId(String userId);

    List<String> geyRoleByEmployeeNo(String employeeNo);
    Integer getSumByCollegeNo(String employeeNo);

    FileEntry fileUpload(MultipartFile file) throws IOException;
    FileEntry filePostUpload(MultipartFile file,String employeeNo) throws IOException;
}
