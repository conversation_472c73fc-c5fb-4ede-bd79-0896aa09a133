package com.htks.domain.common.dto;

import com.google.common.base.MoreObjects;
import com.htks.domain.AbstractUpdateTraceDomain;
import lombok.Getter;
import lombok.Setter;

/**
 * 附件dto
 *
 * <AUTHOR>
 * @date 2022/9/29.
 */
@Getter
@Setter
public class AttachmentEntity extends AbstractUpdateTraceDomain {

    private static final long serialVersionUID = 5460429325363677818L;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 附件路径
     */
    private String attachmentPath;

    /**
     * 备注
     */
    private String attachmentMemo;


    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("attachmentName", attachmentName)
                .add("attachmentType", attachmentType)
                .add("attachmentPath", attachmentPath)
                .add("attachmentMemo", attachmentMemo)
                .toString();
    }
}
