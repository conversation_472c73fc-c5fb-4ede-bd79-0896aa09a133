package com.htks.domain.common.dto;

import com.htks.domain.AbstractUpdateTraceDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 登录人员表
 *
 */
@Getter
@Setter
public class UpgradeUserAdminEntity extends AbstractUpdateTraceDomain {
    @ApiModelProperty(value = "职能ID")
    private Long znId;
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;
    @ApiModelProperty(value = "员工名称")
    private String employeeName;
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
    @ApiModelProperty(value = "权限")
    private String roleId;
}
