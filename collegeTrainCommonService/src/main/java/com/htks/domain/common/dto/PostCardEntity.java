package com.htks.domain.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
//上岗证
public class PostCardEntity {

    @ApiModelProperty(value = "部门")
    private String department;


    @ApiModelProperty(value = "区域")
    private String postArea;

    @ApiModelProperty(value = "上岗证名称及编号")
    private String postName;
    @ApiModelProperty(value = "上岗证id")
    private String postId;
    @ApiModelProperty(value = "上岗证图片地址")
    private String path;

    @ApiModelProperty(value = "上岗证上传时间")
    private String time;
}
