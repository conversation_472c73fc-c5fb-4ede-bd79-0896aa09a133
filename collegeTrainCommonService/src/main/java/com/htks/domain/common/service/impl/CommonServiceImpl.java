package com.htks.domain.common.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.htks.domain.common.dto.*;
import com.htks.domain.common.repository.hana.CommonRepository;
import com.htks.domain.common.service.CommonService;
import com.htks.domain.student.dto.FileEntry;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.errors.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.springframework.http.*;

import static com.google.common.base.MoreObjects.firstNonNull;

/**
 * 公共数据业务service实现类
 *
 * <AUTHOR>
 * @date 2022/9/21.
 */
@Service
@Transactional
public class CommonServiceImpl implements CommonService {

    @Autowired
    private CommonRepository commonRepository;
    @Value("${FileUpload}")
    private String FileUpload;

    /**
     * 查询 - 参数下拉框选项数据
     *
     * @param paramType 参数类型
     * @return 选项数据
     */
    @Override
    @Transactional(readOnly = true)
    public List<Map<String, String>> getParameterDropDown(String paramType) {

        return commonRepository.getParameterDropDown(paramType);
    }

    /**
     * 查询 - 员工基本信息
     *
     * @param employeeNumber 员工工号
     * @return 员工基本信息
     */
    @Override
    @Transactional(readOnly = true)
    public EmployeeEntity getEmployeeBaseInfo(String employeeNumber) {

        return firstNonNull(commonRepository.getEmployeeBaseInfo(employeeNumber), new EmployeeEntity());
    }

    /**
     * 查询 - 员工基本信息
     *
     * @param weChatId 企业微信id
     * @return 员工基本信息
     */
    @Override
    @Transactional(readOnly = true)
    public EmployeeEntity getEmployeeBaseInfoByWx(String weChatId) {

        return firstNonNull(commonRepository.getEmployeeBaseInfoByWx(weChatId), new EmployeeEntity());
    }

    @Override
    @Transactional(readOnly = true)
        public List<UpgradeDictionaryEntity> getDictionary(String type) {
        return commonRepository.getDictionary(type);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UpgradeBuZnSkillEntity> getSkillDictionary(Long znId) {
        return commonRepository.getSkillDictionary(znId);
    }

    @Override
    public List<SkillGroupDicEntity> getSkillGroupDictionary(Long znId,String scoreType) {
        return commonRepository.getSkillGroupDictionary(znId,scoreType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UpgradeBusinessUnitEntity> getBusinessUnit() {
        return commonRepository.getBusinessUnit();
    }

    @Override
    @Transactional(readOnly = true)
    public List<UpgradeDepartmentEntity> getDepartment(Long businessUnitId) {
        return commonRepository.getDepartment(businessUnitId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UpgradeClassBanEntity> getClassBan(Long departmentId) {
        return commonRepository.getClassBan(departmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UpgradeZnEntity> getZn(Long classBanId) {
        return commonRepository.getZn(classBanId);
    }

    @Override
    public Boolean addAttachmentInfoToDB(AttachmentEntity attachmentEntity) {
        return commonRepository.addAttachment(attachmentEntity)>0;
    }

    @Override
    public List<CommonRoleEntity> getCommonRoleInfo(String systemIdentifier) {
        return commonRepository.getRoleInfo(systemIdentifier);
    }

    @Override
    public List<AttachmentEntity> getAttachmentByAnswerId(Long answerId) {
        return commonRepository.getAttachmentByAnswerId(answerId);
    }

    @Override
    public String getEmployeeNoByUserId(String userId) {
        return commonRepository.getEmployeeNoByUserId(userId);
    }

    @Override
    public List<String> geyRoleByEmployeeNo(String employeeNo) {
        return commonRepository.geyRoleByEmployeeNo(employeeNo);
    }

    @Override
    public Integer getSumByCollegeNo(String employeeNo) {
        return commonRepository.getSumByCollegeNo(employeeNo);
    }

    @Override
    public FileEntry fileUpload(MultipartFile multipartFile) throws IOException {
        String url = FileUpload;

        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 创建请求体参数表单
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", multipartFile.getResource());

        // 创建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("keys", "4271664f3d594b8cbb766c0271115924");

        // 创建请求实体对象
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        // 发送 POST 请求，并获取响应
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

        // 获取响应的字符串
        String response = responseEntity.getBody();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(response);
        String downloadUrl = jsonNode.get("data").get("downloadurl").asText();
        String name = jsonNode.get("data").get("newfilename").asText();

        FileEntry fileEntry=new FileEntry();
        fileEntry.setPath(downloadUrl);
        fileEntry.setFIleNewName(name);
        fileEntry.setFileName(jsonNode.get("data").get("filename").asText());

        return fileEntry;
    }

    @Override
    public FileEntry filePostUpload(MultipartFile file,String employeeNo) throws IOException {

        String randomUUID = UUID.randomUUID().toString().substring(0, 16);

        // 在文件名中添加随机 UUID
        String fileName = randomUUID + "_" + file.getOriginalFilename();
        // 定义目标文件存储路径
        String targetFilePath = "/home/<USER>/collegeSystem/postCard/"+employeeNo+"/"+ fileName;

        // 创建目标文件对象
        File targetFile = new File(targetFilePath);
        if (!targetFile.getParentFile().exists()) {
            boolean directoriesCreated = targetFile.getParentFile().mkdirs();
            if (directoriesCreated) {
                System.out.println("父目录已创建");
            } else {
                System.out.println("无法创建父目录");
            }
        }
        // 将文件写入目标文件
        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
            fos.write(file.getBytes());
        } catch (IOException e) {
            // 处理写入文件时可能发生的异常
            e.printStackTrace();
            // 或者抛出自定义异常
            throw new IOException("文件上传失败");
        }
        FileEntry fileEntry = new FileEntry();
        fileEntry.setFileName(fileName);
        fileEntry.setPath(targetFilePath);
        fileEntry.setFIleNewName(fileName);
        // 返回目标文件的相关信息或做进一步的操作
        return fileEntry;
    }


    static class ResponseModel {
        private List<com.htks.domain.common.dto.UoloadVo> data;

        public List<com.htks.domain.common.dto.UoloadVo> getData() {
            return data;
        }
    }
}
