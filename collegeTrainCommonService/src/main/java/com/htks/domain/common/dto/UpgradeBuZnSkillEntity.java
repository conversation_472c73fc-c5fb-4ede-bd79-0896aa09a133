package com.htks.domain.common.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 职能工作技能字典表
 *
 */
@Getter
@Setter
public class UpgradeBuZnSkillEntity {
    @ApiModelProperty(hidden = true)
    private Long id;

    @ApiModelProperty(value = "职能ID")
    private String znId;

    @ApiModelProperty(value = "评价方式")
    private String scoreType;

    @ApiModelProperty(value = "工作技能名称")
    private String name;

    @ApiModelProperty(value = "工作技能名称")
    private String service;

    @ApiModelProperty(value = "工作技能名称")
    private String param;

    @ApiModelProperty(value = "工作技能编号")
    private String skillCode;

    @ApiModelProperty(value = "技能唯一名称")
    private String groupName;
}


