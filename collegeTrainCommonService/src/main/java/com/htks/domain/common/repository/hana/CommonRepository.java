package com.htks.domain.common.repository.hana;

import com.htks.domain.common.dto.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 公共数据持久化
 *
 * <AUTHOR>
 * @date 2022/9/21.
 */
@Mapper
@Repository
public interface CommonRepository {

    /**
     * 查询 - 参数下拉框选项数据
     *
     * @param paramType 参数类型
     * @return 选项数据
     */
    @MapKey("paramType")
    List<Map<String, String>> getParameterDropDown(String paramType);

    /**
     * 查询 - 员工基本信息
     *
     * @param employeeNumber 员工工号
     * @return 员工基本信息
     */
    EmployeeEntity getEmployeeBaseInfo(String employeeNumber);

    /**
     * 查询 - 员工基本信息
     *
     * @param weChatId 企业微信id
     * @return 员工基本信息
     */
    EmployeeEntity getEmployeeBaseInfoByWx(String weChatId);

    List<UpgradeDictionaryEntity> getDictionary(String type);

    List<UpgradeBuZnSkillEntity> getSkillDictionary(Long znId);

    List<SkillGroupDicEntity> getSkillGroupDictionary(Long znId,String scoreType);

    List<UpgradeBusinessUnitEntity> getBusinessUnit();

    List<UpgradeDepartmentEntity> getDepartment(Long businessUnitId);

    List<UpgradeClassBanEntity> getClassBan(Long departmentId);

    List<UpgradeZnEntity> getZn(Long classBanId);

    Integer addAttachment(AttachmentEntity attachmentEntity);

    List<CommonRoleEntity> getRoleInfo(String systemIdentifier);

    List<AttachmentEntity> getAttachmentByAnswerId(Long answerId);


    String getEmployeeNoByUserId(String userId);

    List<String> geyRoleByEmployeeNo(String employeeNo);

    Integer getSumByCollegeNo(String employeeNo);
}
