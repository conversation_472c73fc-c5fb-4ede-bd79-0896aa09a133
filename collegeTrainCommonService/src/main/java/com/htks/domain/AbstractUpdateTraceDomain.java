package com.htks.domain;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.common.utils.DateUtils;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 域Abstract类
 *
 * <AUTHOR>
 * @date 2010/09/09.
 */
@Getter
@Setter
public abstract class AbstractUpdateTraceDomain extends AbstractNamedDomain {

  private static final long serialVersionUID = 3678498451436028291L;

  @ApiModelProperty(hidden = true)
  private String updatedUser;

  private String createdUser;
  @ApiModelProperty(hidden = true)
  private Date updatedTime;
  @ApiModelProperty(hidden = true)
  private Date createdTime;


  protected Date obtainValidDate(final Date srcDate) {
    return DateUtils.obtainValidDate(srcDate);
  }
  
  @Override
  public String toString() {
    return toStringHelper(this).
        add("id", getId()).
        add("name", getName()).
        add("updatedUser", updatedUser).
        add("createdUser", createdUser).
        add("updatedTime", updatedTime).
        add("createdTime", createdTime).
        toString();
  }
}
