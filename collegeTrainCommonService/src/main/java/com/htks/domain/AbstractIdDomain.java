package com.htks.domain;

import static com.google.common.base.MoreObjects.toStringHelper;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 域Abstract类
 *
 * <AUTHOR>
 * @date 2010/09/09.
 */
@Getter
@Setter
public abstract class AbstractIdDomain extends AbstractBaseDomain {

  private static final long serialVersionUID = 4157067139266771854L;
  @ApiModelProperty(hidden = true)
  private Long id;

  public AbstractIdDomain() {
    super();
    id = null;
  }

  @Override
  public Boolean isNullObject() {
    return this.getId() == null;
  }

  @Override
  public String toString() {
    return toStringHelper(this).
        add("id", id).
        toString();
  }
}
