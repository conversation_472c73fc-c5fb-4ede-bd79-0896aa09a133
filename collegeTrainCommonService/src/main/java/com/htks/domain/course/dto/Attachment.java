package com.htks.domain.course.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/02/10 10:10
 **/
@Data
public class Attachment {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("上岗证主键ID")
    private Long postCardId;
    @ApiModelProperty("试题ID")
    private Long questionId;
    @ApiModelProperty("附件相对路径")
    private String attachmentPath;
    @ApiModelProperty("附件后缀名")
    private String attachmentType;
    @ApiModelProperty("附件原文件名")
    private String attachmentName;
    @ApiModelProperty("存附件变更后的真实文件名，用来拼接在url后面")
    private String attachmentMemo;
}
