package com.htks.domain.course.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CourseEntityDto {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "课程标题")
    private String title;

    @ApiModelProperty(value = "课程类别")
    private String category;

    @ApiModelProperty(value = "附件ID")
    private Long attachmentId;

    @ApiModelProperty(value = "部门")
    private String departmentName;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty("上岗证主键ID")
    private Long postCardId;
    @ApiModelProperty("试题ID")
    private Long questionId;
    @ApiModelProperty("附件相对路径")
    private String attachmentPath;
    @ApiModelProperty("附件后缀名")
    private String attachmentType;
    @ApiModelProperty("附件原文件名")
    private String attachmentName;
    @ApiModelProperty("存附件变更后的真实文件名，用来拼接在url后面")
    private String attachmentMemo;
}
