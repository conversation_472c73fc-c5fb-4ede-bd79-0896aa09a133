package com.htks.domain.course;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.course.dto.CourseCondition;
import com.htks.domain.course.dto.CourseSaveCondition;
import com.htks.domain.question.dto.PaperCondition;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.domain.question.dto.PaperSaveCondition;
import com.htks.web.JsonPagedVO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface CourseService {

    JsonPagedVO<PaperEntity> queryCourseList(CourseCondition condition);

    ResultVO saveCourse(CourseSaveCondition condition);

    ResultVO deleteCourse(Long courseId);


}
