package com.htks.domain.course.impl;

import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.course.CourseService;
import com.htks.domain.course.dto.*;
import com.htks.domain.course.repository.hana.CourseRepository;
import com.htks.domain.question.QuestionService;
import com.htks.domain.question.dto.*;
import com.htks.domain.question.repository.hana.QuestionRepository;
import com.htks.web.JsonPagedVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/05/08 16:44
 **/
@Slf4j
@Service
public class CourseServiceImpl implements CourseService {

    @Autowired
    private CourseRepository courseRepository;

    @Override
    public JsonPagedVO<PaperEntity> queryCourseList(CourseCondition condition) {
        final int recordCount = courseRepository.queryCourseCount(condition);
        final List<CourseEntityDto> result = courseRepository.queryCourseList(condition);
        if(result !=null){
            return new JsonPagedVO(result,recordCount);
        }
        return new JsonPagedVO(new ArrayList<>(),recordCount);
    }

    @Override
    public ResultVO saveCourse(CourseSaveCondition condition) {
        Attachment attachment = condition.getAttachment();
        courseRepository.insertAttachment(attachment);
        condition.setAttachmentId(attachment.getId());
        CourseEntity courseEntity = new CourseEntity();
        BeanUtils.copyProperties(condition, courseEntity);

        if(condition.getId() == null || condition.getId() == 0){
            courseRepository.saveCourse(courseEntity);
        }else{
            courseRepository.updateCourse(courseEntity);
        }
        return new ResultVO(ResultCode.SUCCESS.getCode());
    }

    @Override
    public ResultVO deleteCourse(Long courseId) {
        return new ResultVO(courseRepository.deleteCourse(courseId));
    }

}
