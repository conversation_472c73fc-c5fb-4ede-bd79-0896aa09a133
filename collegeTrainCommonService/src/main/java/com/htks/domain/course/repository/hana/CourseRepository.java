package com.htks.domain.course.repository.hana;

import com.htks.domain.course.dto.Attachment;
import com.htks.domain.course.dto.CourseCondition;
import com.htks.domain.course.dto.CourseEntity;
import com.htks.domain.course.dto.CourseEntityDto;
import com.htks.domain.question.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CourseRepository {
    int queryCourseCount(CourseCondition condition);

    List<CourseEntityDto> queryCourseList(CourseCondition condition);

    int saveCourse(CourseEntity condition);

    int updateCourse(CourseEntity condition);

    int insertAttachment(Attachment condition);

    int deleteCourse(Long id);
}
