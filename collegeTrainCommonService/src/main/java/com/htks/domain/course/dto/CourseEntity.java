package com.htks.domain.course.dto;

import com.htks.domain.question.dto.QuestionEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CourseEntity {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "课程标题")
    private String title;

    @ApiModelProperty(value = "课程类别")
    private String category;

    @ApiModelProperty(value = "部门")
    private String departmentName;

    @ApiModelProperty(value = "附件ID")
    private Long attachmentId;

    @ApiModelProperty(value = "创建人")
    private String creator;

}
