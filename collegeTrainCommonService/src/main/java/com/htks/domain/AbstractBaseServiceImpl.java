package com.htks.domain;

import static com.google.common.collect.Maps.newHashMap;
import static java.util.Collections.unmodifiableList;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.Errors;
import org.springframework.validation.MapBindingResult;
import org.springframework.validation.SmartValidator;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;


/**
 * Base Service实现Abstract类
 *
 * <AUTHOR>
 * @date 2010/09/09.
 */
public abstract class AbstractBaseServiceImpl {

  @Autowired
  private Validator validator;

  @Autowired
  private SmartValidator smartValidator;

  protected final ValidateResult beanValidator(AbstractBaseDomain domain) {

    final Errors errors = new MapBindingResult(newHashMap(), domain.getClass().getName());
    validator.validate(domain, errors);

    final Boolean hasError = errors.hasErrors();
    final ValidateResult validateResult = new ValidateResult(hasError);
    if (hasError) {
      final String builder = errors.getFieldErrors().stream()
          .map(error -> error.getDefaultMessage() + "\r\n")
          .collect(Collectors.joining());

      validateResult.setError(builder);
    }

    return validateResult;
  }

  protected final ValidateResult beanValidator(AbstractBaseDomain domain, Class<?>... groups) {

    final Errors errors = new MapBindingResult(newHashMap(), domain.getClass().getName());
    validator.validate(domain, errors);

    final Boolean hasError = errors.hasErrors();
    final ValidateResult validateResult = new ValidateResult(hasError);
    if (hasError) {
      final String builder = errors.getFieldErrors().stream()
          .map(error -> error.getField() + ":" + error.getDefaultMessage() + "\r\n")
          .collect(Collectors.joining());

      validateResult.setError(builder);
    }

    return validateResult;
  }

  protected final ValidateResult beanCollectionValidator(
      List<? extends AbstractBaseDomain> domains) {
    ValidateResult validateResult = new ValidateResult(Boolean.FALSE);
    for (AbstractBaseDomain domain : unmodifiableList(domains)) {
      validateResult = beanValidator(domain);

      if (validateResult.getHasError()) {
        return validateResult;
      }
    }

    return validateResult;

  }

  /**
   * 支持分组校验
   *
   * @param domain domain
   * @param groups groups
   * @return ValidateResult
   */
  protected final ValidateResult beanSmartValidator(AbstractBaseDomain domain, Class<?>... groups) {
    final Errors errors = new MapBindingResult(newHashMap(), domain.getClass().getName());

    ValidationUtils.invokeValidator(smartValidator, domain, errors, (Object[]) groups);
    final Boolean hasError = errors.hasErrors();
    final ValidateResult validateResult = new ValidateResult(hasError);
    if (hasError) {
      final String builder = errors.getFieldErrors().stream()
          .map(error -> error.getField() + ":" + error.getDefaultMessage() + "\r\n")
          .collect(Collectors.joining());

      validateResult.setError(builder);
    }

    return validateResult;
  }

  /**
   * 将员工和员工工号的字符串转成工号集合
   *
   * @param userCodesStr
   * @return
   */
  public List<String> stringToList(String userCodesStr) {
    List<String> list = Lists.newArrayList();
    String[] arr = userCodesStr.split(",");
    for (String userCode : arr) {
      String code = userCode.split("-")[1];
      list.add(code);
    }
    return list;
  }

  /**
   * 工号集合转工号字符串
   *
   * @param list
   * @return
   */
  public String listToString(List<String> list) {
    String userCodesStr = "";
    if (!CollectionUtils.isEmpty(list)) {
      userCodesStr = StringUtils.join(list, ",");
    }
    return userCodesStr;
  }
}
