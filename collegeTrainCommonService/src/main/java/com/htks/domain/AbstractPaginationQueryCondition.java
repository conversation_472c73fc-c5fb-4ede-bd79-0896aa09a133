package com.htks.domain;

import static com.google.common.base.MoreObjects.toStringHelper;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 分页查询条件Abstract类
 *
 * <AUTHOR>
 * @date 2010/09/09.
 */
@Getter
@Setter
public abstract class AbstractPaginationQueryCondition extends AbstractBaseCondition {

  @ApiModelProperty("第几页")
  private Integer pageNum;

  @ApiModelProperty("每页显示数量")
  private Integer pageSize;

  protected AbstractPaginationQueryCondition(Integer pageNum, Integer pageSize) {
    this.pageNum = pageNum;
    this.pageSize = pageSize;
  }

  protected AbstractPaginationQueryCondition() {
    this.pageNum = 0;
    this.pageSize = 0;
  }

  @ApiModelProperty(hidden = true)
  private Integer limit;

  @ApiModelProperty(hidden = true)
  private Integer offset;

  public Integer getLimit() {
    if (null == pageSize){
      return 0;
    }
    return pageSize;
  }

  public Integer getOffset() {
    if (null == pageNum || null == pageSize){
      return 0;
    }
    return (pageNum - 1) * pageSize;
  }

  @Override
  public String toString() {
    return toStringHelper(this)
        .add("pageNum", pageNum)
        .add("pageSize", pageSize)
        .toString();
  }
}
