package com.htks.domain.examiner.repository.hana;

import com.htks.domain.examiner.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface ExaminerRepository {

    Colleger startApproval(String studentID);


    Integer approvalComplete(@Param("ex") Examiner examiner,Long AppointRecordId,String EVALUATOR_NAME,String EVALUATOR_TYPE);

    //第一次检测目前阅卷师是否已评价
    List<String> checkApprovalFirst(String evaluatorNo,String employeeNo,Integer type);

    List<String> checkApprovalNew(String evaluatorNo,String employeeNo,Integer type);
    String getCheckApprovalNo(String evaluatorNo,String employeeNo,Integer type);

    //第二次检测本部门是否已评价
    String checkApprovalSecond(String no);

    //--修改预约表成绩前先去查询是否三个部门都已经完成评价，都完成再去更新预约表
    void updateAPPOINT(@Param("ex") AppointGoal examiner,Integer type);

    // 查询是否三个部门都已经完成评价
    Integer checkApprovalThird(String employeeNo, Integer type);

    Double calculateEachGrades(String evaluatorNo,Integer  type,String employeeNo);

    Long sureAppointRecordId(String employeeNo,Integer type);
    Integer getApprovalSum(String employeeNo,Integer type);
    String getApprovalDate(String employeeNo,Integer type);
    String collegeName(String employeeNo);
    List<SatisfactionOrAbnormalDefenseCriteria> getSatisfactionOrAbnormalDefenseCriteria(Integer type);
    List<SopOrHandsOnCriteria> getSopOrHandsOnCriteria(Integer type);
    String getAsssNo(String no);
    Integer getSum(String employeeNo,Integer type);
}
