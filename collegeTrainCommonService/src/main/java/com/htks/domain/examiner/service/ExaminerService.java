package com.htks.domain.examiner.service;

import com.htks.domain.examiner.dto.*;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ExaminerService {
    //根据工号，信息自动获取 工程实操评价和异常评价可复用
    Colleger startApproval(String studentID);
    //增加评价记录
    Integer approvalComplete(@Param("aa") Examiner examiner,Long AppointRecordId,String  EVALUATOR_NAME,String EVALUATOR_TYPE);


    //第一次检测目前阅卷师是否已评价
    List<String> checkApprovalFirst (String evaluatorNo ,String examiner,Integer type);
    //第二次检测本部门是否已评价
    String checkApprovalSecond (String no);
    List<String> checkApprovalNew(String evaluatorNo,String employeeNo,Integer type);
    String getCheckApprovalNo(String evaluatorNo,String employeeNo,Integer type);

    //--修改预约表成绩前先去查询是否三个部门都已经完成评价，都完成再去更新预约表
    void updateAPPOINT( AppointGoal examiner,Integer type);
    // 查询是否三个部门都已经完成评价
    Integer checkApprovalThird(String employeeNo,Integer type);
   //查询三个部门的评分
    double calculateEachGrades(String employeeno ,Integer type,String No);

    Long sureAppointRecordId(String employeeNo,Integer type);

    String collegeName(String employeeNo);

    List<SatisfactionOrAbnormalDefenseCriteria> getSatisfactionOrAbnormalDefenseCriteria(Integer type);
    List<SopOrHandsOnCriteria> getSopOrHandsOnCriteria(Integer type);
    Integer getSum(String employeeNo,Integer type);


}
