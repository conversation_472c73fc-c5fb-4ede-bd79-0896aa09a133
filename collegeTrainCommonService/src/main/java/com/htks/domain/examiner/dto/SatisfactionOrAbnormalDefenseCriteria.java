package com.htks.domain.examiner.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SatisfactionOrAbnormalDefenseCriteria {
    //1:异常答辩评分标准 2：满意度评分表
    private Integer type;

    //评价项目
private String category;
//评价标准
    private String evaluateStandard;
    //分值
    private String score;
    //标准1 100%满分
    private String standard1;
    //标准2 80%-100%
    private String standard2;
    //标准3 60%-80%
    private String standard3;
    //标准4 40%-60%
    private String standard4;
    //标准5 0%-40%
    private String standard5;

}
