package com.htks.domain.examiner.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Colleger {

    @ApiModelProperty(value = "姓名")
    private String StudentName;

    @ApiModelProperty(value = "区域")
    private String  StudentArea;

    @ApiModelProperty(value = "选岗部门")
    private String StudentDepartment;

    @ApiModelProperty(value = "实习部门id")
    private String StudentAreaId;

    @ApiModelProperty(value = "学号")
    private String  StudentNo;

}
