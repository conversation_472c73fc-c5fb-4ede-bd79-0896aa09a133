package com.htks.domain.examiner.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AppointGoal {
    @ApiModelProperty(value = "考试状态")
    private String  status;


    @ApiModelProperty(value = "最后分数")
    private Double score=null;

    @ApiModelProperty(value = "通过日期")
    private String passedDate;

    @ApiModelProperty(value = "是否通过")
    private String passed;

    @ApiModelProperty(value = "学生工号")
    private String employeeNo;

    @ApiModelProperty(value = "附加分")
    private String additionalPoints;


}
