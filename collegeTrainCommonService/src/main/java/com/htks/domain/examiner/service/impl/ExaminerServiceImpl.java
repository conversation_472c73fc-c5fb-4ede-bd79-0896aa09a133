package com.htks.domain.examiner.service.impl;

import com.htks.domain.examiner.dto.*;
import com.htks.domain.examiner.repository.hana.ExaminerRepository;
import com.htks.domain.examiner.service.ExaminerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Transactional
public class ExaminerServiceImpl implements ExaminerService {
    @Autowired
    private ExaminerRepository examinerRepository;

    @Override
    public Colleger startApproval(String studentID) {
        return examinerRepository.startApproval(studentID);
    }

    @Override
    public Integer approvalComplete(Examiner examiner,Long AppointRecordId,String EVALUATOR_NAME,String EVALUATOR_TYPE) {
       return examinerRepository.approvalComplete(examiner, AppointRecordId,EVALUATOR_NAME,EVALUATOR_TYPE);
    }

    @Override
    public List<String> checkApprovalFirst(String evaluatorNo,String examiner,Integer type) {
        return examinerRepository.checkApprovalFirst(evaluatorNo,examiner,type);
    }

    @Override
    public String checkApprovalSecond(String no) {
        return examinerRepository.checkApprovalSecond(no);
    }

    @Override
    public List<String> checkApprovalNew(String evaluatorNo, String employeeNo, Integer type) {
        return examinerRepository.checkApprovalNew(evaluatorNo, employeeNo, type);
    }

    @Override
    public String getCheckApprovalNo(String evaluatorNo, String employeeNo, Integer type) {
        return examinerRepository.getCheckApprovalNo(evaluatorNo, employeeNo, type);
    }

    @Override
    public void updateAPPOINT(AppointGoal examiner,Integer type) {
        examinerRepository.updateAPPOINT(examiner,type);
    }

    @Override
    public Integer checkApprovalThird(String employeeNo,Integer type) {
        return examinerRepository.checkApprovalThird(employeeNo,type);
    }

    @Override
    public double calculateEachGrades(String employeeNo, Integer type,String No) {
        return examinerRepository.calculateEachGrades(employeeNo,type,No);
    }

    @Override
    public Long sureAppointRecordId(String employeeNo,Integer type) {
        return examinerRepository.sureAppointRecordId(employeeNo, type);
    }

    @Override
    public String collegeName(String employeeNo) {
        return examinerRepository.collegeName(employeeNo);
    }

    @Override
    public List<SatisfactionOrAbnormalDefenseCriteria> getSatisfactionOrAbnormalDefenseCriteria(Integer type) {
        return examinerRepository.getSatisfactionOrAbnormalDefenseCriteria(type);
    }

    @Override
    public List<SopOrHandsOnCriteria> getSopOrHandsOnCriteria(Integer type) {
        return examinerRepository.getSopOrHandsOnCriteria(type);
    }

    @Override
    public Integer getSum(String employeeNo, Integer type) {
        return examinerRepository.getSum(employeeNo, type);
    }

    public String checkAll(String employeeNo,Integer type, String EVALUATOR_NO,String evaluatorType) {
        String message = "0";

        String name = examinerRepository.collegeName(employeeNo);
        List<String> l = examinerRepository.checkApprovalFirst(EVALUATOR_NO,employeeNo, type);
        List<String>evaluatorTypeList=examinerRepository.checkApprovalNew(EVALUATOR_NO,employeeNo, type);
        String dapartment = examinerRepository.checkApprovalSecond(EVALUATOR_NO);
        if (dapartment == null || dapartment.equals("")) {
            message = "此用户部门为空请查证后重新登录";
            return message;
        }
        else {
        for (int i = 0; i < l.size(); i++) {
            if (l.get(i).equals(EVALUATOR_NO)) {
                if (type==1) {
                    message = "您对学员" + name + "(" + employeeNo + ")" + "的工程类实操评价已完成，不可再次评价！";
                    return message;
                }else {
                    message = "您对学员" + name + "(" + employeeNo + ")" + "的异常答辩评价已完成，不可再次评价！";
                    return message;
                }
            }
        }

/*        if ("1".equals(message)) {

            if (dapartment.equals("质量部") || dapartment.equals("产品工程部") || dapartment.contains("工程部")) {
                message = "0";
            } else {
                message = "不属于阅卷师部门，请检查后重新登录";
                return message;
            }
            if (!dapartment.equals("质量部") && !dapartment.equals("产品工程部")) {
                if (!dapartment.equals(dapartmentStudent)){
                    message = "不属于同一部门";
                    return message;
                }
            }
            if ("0".equals(message)) {
                for (int i = 0; i < l.size(); i++) {
                    String department1 = examinerRepository.checkApprovalSecond(l.get(i));
                    Integer type1 = type;
                    if (dapartment.equals(department1) && type1 == 1) {
                        switch (department1) {
                            case "质量部":
                                message = "质量评委已完成对学员" + name + "(" + employeeNo + ")" + "的工程类实操评价，无需再进行评价，谢谢！";
                                return message;
                            case "产品工程部":
                                message = "NPI评委已完成对学员" + name + "(" + employeeNo + ")" + "的工程类实操评价，无需再进行评价，谢谢！";
                                return message;
                            default:
                                message = "工程部评委已完成对学员" + name + "(" + employeeNo + ")" + "的工程类实操评价，无需再进行评价，谢谢！";
                                return message;
                        }
                    } else if (dapartment.equals(department1) && type1 == 2) {
                        switch (department1) {
                            case "质量部":
                                message = "质量评委已完成对学员" + name + "(" + employeeNo + ")" + "的异常答辩评价，无需再进行评价，谢谢！";
                                return message;
                            case "产品工程部":
                                message = "NPI评委已完成对学员" + name + "(" + employeeNo + ")" + "的异常答辩评价，无需再进行评价，谢谢！";
                                return message;
                            default:
                                message = "工程部评委已完成对学员" + name + "(" + employeeNo + ")" + "的异常答辩评价，无需再进行评价，谢谢！";
                                return message;
                        }
                    }
                }
            }
        }*/
        if (evaluatorTypeList.contains(evaluatorType)){
            if (evaluatorType.equals("1")&&type==1){
                message = "质量评委已完成对学员" + name + "(" + employeeNo + ")" + "的工程类实操评价，无需再进行评价，谢谢！";
                return message;
            }
            if (evaluatorType.equals("3")&&type==1){
                message = "NPI评委已完成对学员" + name + "(" + employeeNo + ")" + "的工程类实操评价，无需再进行评价，谢谢！";
                return message;
            }
            if (evaluatorType.equals("2")&&type==1){
                message = "工程部评委已完成对学员" + name + "(" + employeeNo + ")" + "的工程类实操评价，无需再进行评价，谢谢！";
                return message;
            }
            if (evaluatorType.equals("1")&&type==2){
                message = "质量评委已完成对学员" + name + "(" + employeeNo + ")" + "的异常答辩评价，无需再进行评价，谢谢！";
                return message;
            }
            if (evaluatorType.equals("3")&&type==2){
                message = "NPI评委已完成对学员" + name + "(" + employeeNo + ")" + "的异常答辩评价，无需再进行评价，谢谢！";
                return message;
            }
            if (evaluatorType.equals("2")&&type==2){
                message = "工程部评委已完成对学员" + name + "(" + employeeNo + ")" + "的异常答辩评价，无需再进行评价，谢谢！";
                return message;
            }
        }
    }
        if (examinerRepository.getApprovalSum(employeeNo, type)==0){
            message = "此学员没有预约";
            return message;
        }
        LocalDate today = LocalDate.now();

        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将日期格式化成字符串
        String date1 = today.format(formatter);
        String date2= examinerRepository.getApprovalDate(employeeNo,type);
        LocalDate localDate1 = LocalDate.parse(date1);
        LocalDate localDate2 = LocalDate.parse(date2);
        if (localDate1.compareTo(localDate2) < 0){
            message = "此学员预约考核时间未到";
            return message;
        }
        return message;
    }

}
