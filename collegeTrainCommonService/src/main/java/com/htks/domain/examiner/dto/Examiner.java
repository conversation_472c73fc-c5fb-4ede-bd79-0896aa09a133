package com.htks.domain.examiner.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Examiner {

    @ApiModelProperty(value = "学生id")
    private String employeeNo;


    @ApiModelProperty(value = "分数")
    private double score;

    @ApiModelProperty(value = "工程理论问题")
    private String question;

    @ApiModelProperty(value = "回答")
    private String answer;

    @ApiModelProperty(value = "评价类型 1：工程类 2：异常答辩")
    private Integer type;

    @ApiModelProperty(value = "评价人工号")
    private String evaluatorNo;

    private  String EVALUATOR_TYPE;
}


