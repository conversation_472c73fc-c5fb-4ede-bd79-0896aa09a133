package com.htks.domain.sys.shiro.filter;

import static com.htks.common.SystemConfig.ResponseStatusEnum.LOGIN_FAILURE;

import com.alibaba.fastjson.JSON;
import com.htks.domain.sys.shiro.UserAuthenticationToken;
import com.htks.web.Rest;
import com.htks.web.RestBody;
import java.io.IOException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.springframework.stereotype.Component;

/**
 * 自定义过滤器
 *
 * <AUTHOR>
 * @date 2020/09/07
 */
@Slf4j
@Component("userAuthenticatingFilter")
public final class UserAuthenticatingFilter extends AuthenticatingFilter {

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        //获取请求token
        String token = getRequestToken((HttpServletRequest) request);

        if (StringUtils.isBlank(token)) {
            return null;
        }

        return new UserAuthenticationToken(token);
    }

    /**
     * shiro权限拦截核心方法，代表是否允许访问
     * 返回true表示允许
     * 返回false表示拒绝访问
     *
     * @param request  http请求
     * @param response http响应
     * @param object   请求参数
     * @return boolean 是否允许访问
     */
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object object) {
        return Boolean.FALSE;
    }

    /**
     * 表示当访问拒绝时是否自己处理，
     * 如果返回true表示自己不处理且继续拦截器链执行，
     * 如果返回false表示该拦截器实例已经处理完成了，将直接返回即可。
     *
     * @param request  http请求
     * @param response http响应
     * @return boolean 是否自己处理(true不处理 false处理)
     * @throws Exception 处理异常
     */
    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        //获取请求Token，如果Token不存在，直接返回401
        final String token = getRequestToken((HttpServletRequest) request);
        if (log.isDebugEnabled()) {
            log.debug("获取请求Token:{}", token);
        }
        if (StringUtils.isBlank(token)) {
            final HttpServletResponse httpResponse = (HttpServletResponse) response;
            final String json = JSON.toJSONString(RestBody.failure(HttpStatus.SC_UNAUTHORIZED, "invalid token..."));
            httpResponse.getWriter().print(json);
            return Boolean.FALSE;
        }

        return executeLogin(request, response);
    }

    /**
     * 登录失败调用
     */
    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e, ServletRequest request, ServletResponse response) {
        final HttpServletResponse httpResponse = (HttpServletResponse) response;
        httpResponse.setContentType("application/json;charset=utf-8");
        try {
            if (log.isDebugEnabled()) {
                log.debug("验证是否已登录: 未登录...");
            }
            //处理登录失败的异常返回
            final Throwable throwable = e.getCause() == null ? e : e.getCause();
            final Rest r = RestBody.failure(Integer.parseInt(LOGIN_FAILURE.getStatusCode()), throwable.getMessage());
            final String json = JSON.toJSONString(r);
            httpResponse.getWriter().print(json);
        } catch (IOException ioe) {
            log.error("IOException:", e);
        }
        return Boolean.FALSE;
    }

    /**
     * 登录成功调用
     */
    @Override
    protected boolean onLoginSuccess(AuthenticationToken token, Subject subject, ServletRequest request, ServletResponse response) {
        if (log.isDebugEnabled()) {
            log.debug("验证是否已登录: 已登录...");
        }
        return Boolean.TRUE;
    }

    /**
     * 获取请求的token
     */
    private String getRequestToken(HttpServletRequest request) {
        //从header中获取token
        String token = request.getHeader("token");
        //如果header中不存在token，则从参数中获取token
        if (StringUtils.isBlank(token)) {
            token = request.getParameter("token");
        }
        return token;
    }
}
