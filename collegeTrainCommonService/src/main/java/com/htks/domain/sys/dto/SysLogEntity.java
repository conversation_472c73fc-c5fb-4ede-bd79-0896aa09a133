package com.htks.domain.sys.dto;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.domain.AbstractUpdateTraceDomain;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * 系统日志
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Getter
@Setter
public final class SysLogEntity  {

  private static final long serialVersionUID = -3689500620695288203L;

  private String name;

  private Long id;
  /**
   * 序号
   */
  private String index;

  private String updater;

  private String creater;

  private Long updaterId;

  private Long createrId;

  private Date updateTime;

  private Date createTime;

  private String operatorTime;

  /**
   * 员工姓名
   */
  private String employeeName;

  /**
   * 用户操作
   */
  private String operation;

  /**
   * 请求方法
   */
  private String methodName;

  /**
   * 请求参数
   */
  private String params;

  /**
   * 执行时长(毫秒)
   */
  private Long operationTime;

  /**
   * IP地址
   */
  private String ip;

  /**
   * 操作日期
   */
  private String operationDate;

  /**
   * 系统标识
   */
  private String systemIdentifier;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("employeeName", employeeName).
        add("operation", operation).
        add("methodName", methodName).
        add("params", params).
        add("operationTime", operationTime).
        add("ip", ip).
        add("operationDate", operationDate).
        add("systemIdentifier", systemIdentifier).
        toString();
  }
}
