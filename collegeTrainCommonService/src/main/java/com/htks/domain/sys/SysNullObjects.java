package com.htks.domain.sys;

import com.htks.domain.sys.dto.SysEmployeeEntity;
import com.htks.domain.sys.dto.SysLogEntity;
import com.htks.domain.sys.dto.SysTokenEntity;

/**
 * 定义域的NullObject
 *
 * <AUTHOR>
 * @date 2020/09/08
 */
public final class SysNullObjects {

  private SysNullObjects() {
  }

  public static SysLogEntity nullSysLogEntity() {
    return new SysLogEntity();
  }

  public static SysTokenEntity nullSysTokenEntity() {
    return new SysTokenEntity();
  }

  public static SysEmployeeEntity nullSysEmployeeEntity() {
    return new SysEmployeeEntity();
  }

}
