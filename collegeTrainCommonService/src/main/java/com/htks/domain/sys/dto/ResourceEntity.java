package com.htks.domain.sys.dto;

import com.htks.domain.AbstractUpdateTraceDomain;
import lombok.Getter;
import lombok.Setter;

/**
 * 资源菜单信息实体
 *
 * <AUTHOR>
 * @date 2022/6/24.
 */
@Getter
@Setter
public class ResourceEntity extends AbstractUpdateTraceDomain {

  private static final long serialVersionUID = -2019139635340739467L;

  /**
   * 资源名称
   */
  private String resourceName;

  private String resourceCode;

  /**
   * 资源URI
   */
  private String resourceURL;

  //图标名称
  private String iconClass;

  /**
   * 资源类型ID(关联参数表)
   */
  private Long resourceTypeId;

  /**
   * 父资源ID
   */
  private Long parentId;

  /**
   * 排序
   */
  private Integer listOrder;
}
