package com.htks.domain.sys.repository.mysql;

import com.htks.domain.sys.SysQueryCondition;
import com.htks.domain.sys.dto.SysEmployeeEntity;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 员工信息仓储接口
 *
 * <AUTHOR>
 * @date 2022/03/30.
 */
@Repository
public interface SysEmployeeRepository {

  /**
   * 查询员工邮箱地址
   *
   * @param evaluatorNumberSet 工号
   * @return 返回
   */
  List<SysEmployeeEntity> getEmployeeEmail(@Param("evaluatorNumberSet") final Set<String> evaluatorNumberSet);

  /**
   * 查询员工信息
   *
   * @param employeeNumber 工号
   * @return 返回
   */
  SysEmployeeEntity getEmployeeInfo(final String employeeNumber);

  /**
   * 查询员工信息
   *
   * @param condition 条件
   * @return 返回
   */
  List<SysEmployeeEntity> queryEmployeeByCondition(final SysQueryCondition condition);
}
