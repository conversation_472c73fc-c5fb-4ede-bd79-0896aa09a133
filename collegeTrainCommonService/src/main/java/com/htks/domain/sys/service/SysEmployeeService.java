package com.htks.domain.sys.service;

import com.htks.domain.sys.SysQueryCondition;
import com.htks.domain.sys.dto.SysEmployeeEntity;
import com.htks.domain.sys.dto.SysRoleEntity;

import java.util.List;

/**
 * 员工Service层接口
 *
 * <AUTHOR>
 * @date 2022/06/21.
 */
public interface SysEmployeeService {

  /**
   * 查询员工信息
   *
   * @param employeeNumber 工号
   * @return 返回
   */
  SysEmployeeEntity getEmployeeInfo(final String employeeNumber);

  /**
   * 查询员工信息
   *
   * @param condition 条件
   * @return 返回
   */
  List<SysEmployeeEntity> queryEmployeeByCondition(final SysQueryCondition condition);

  /**
   * 查询角色
   *
   * @param employeeNumber 工号
   * @return 返回
   */
  SysRoleEntity getEmployeeRoleInfo(final String employeeNumber);

  /**
   * 查询角色-学员权限
   *
   * @param employeeNumber 工号
   * @return 返回
   */
  Integer getStudent(final String employeeNumber);

}
