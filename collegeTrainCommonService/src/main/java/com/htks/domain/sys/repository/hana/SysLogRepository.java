package com.htks.domain.sys.repository.hana;

import com.htks.domain.sys.dto.SysLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 系统日志仓储接口
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Mapper
@Repository
public interface SysLogRepository {

  /**
   * 保存系统日志
   *
   * @param sysLog 系统日志
   */
  void saveSysLog(@Param("sysLog") SysLogEntity sysLog);
}
