package com.htks.domain.sys.service.impl;

import static com.google.common.base.MoreObjects.firstNonNull;
import static com.htks.common.SystemConfig.TOKEN_EXPIRE;
import static com.htks.domain.sys.SysNullObjects.nullSysTokenEntity;

import com.htks.common.utils.DateUtils;
import com.htks.common.utils.TokenGenerator;
import com.htks.domain.AbstractBaseServiceImpl;
import com.htks.domain.sys.dto.SysTokenEntity;
import com.htks.domain.sys.repository.hana.SysTokenRepository;
import com.htks.domain.sys.service.SysTokenService;
import java.time.LocalDateTime;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * * Token Service层接口实现类
 *
 * <AUTHOR>
 * @date 2020/09/04
 */
@Slf4j
@Service("sysTokenServiceImpl")
@Transactional(rollbackFor = Exception.class)
public class SysTokenServiceImpl extends AbstractBaseServiceImpl implements SysTokenService {

  private final SysTokenRepository sysTokenRepository;

  @Autowired
  public SysTokenServiceImpl(SysTokenRepository sysTokenRepository) {
    this.sysTokenRepository = sysTokenRepository;
  }

  @Override
  public SysTokenEntity createToken(final String employeeNumber, final String signature, final String ip) {
    //生成一个token
    final String token = TokenGenerator.generateValue(employeeNumber, signature);
    //当前时间
    final LocalDateTime now = LocalDateTime.now();
    //过期时间
    final LocalDateTime expireTime = now.plusSeconds(TOKEN_EXPIRE);

    //判断是否生成过token
    SysTokenEntity tokenEntity = queryTokenByEmployeeNumberAndIp(employeeNumber, ip);
    if (Boolean.TRUE.equals(tokenEntity.isNullObject())) {
      tokenEntity = nullSysTokenEntity();
      tokenEntity.setEmployeeNumber(employeeNumber);
      tokenEntity.setToken(token);
      tokenEntity.setUpdateTime(DateUtils.asDate(now));
      tokenEntity.setExpireTime(DateUtils.asDate(expireTime));
      tokenEntity.setIp(ip);
      //保存token
      sysTokenRepository.saveToken(tokenEntity);
    } else {
      tokenEntity.setToken(token);
      tokenEntity.setUpdateTime(DateUtils.asDate(now));
      tokenEntity.setExpireTime(DateUtils.asDate(expireTime));
      //更新token
      sysTokenRepository.updateToken(tokenEntity);
    }

    if (log.isDebugEnabled()) {
      log.debug("生成Token:{}", tokenEntity);
    }

    return tokenEntity;
  }

  @Override
  @Transactional(readOnly = true)
  public SysTokenEntity queryTokenByEmployeeNumber(final String employeeNumber) {
    //根据员工ID查询Token
    return firstNonNull(sysTokenRepository.queryTokenByEmployeeNumber(employeeNumber),
        nullSysTokenEntity());
  }

  @Override
  @Transactional(readOnly = true)
  public SysTokenEntity queryTokenByEmployeeNumberAndIp(final String employeeNumber, final String ip) {
    //根据员工ID查询Token
    return firstNonNull(sysTokenRepository.queryTokenByEmployeeNumberAndIp(employeeNumber, ip),
        nullSysTokenEntity());
  }

  @Override
  public Boolean updateExpireTime(final String employeeNumber, final String token) {
    //当前时间
    final LocalDateTime now = LocalDateTime.now();
    final LocalDateTime expireTime = now.plusSeconds(TOKEN_EXPIRE);
    return sysTokenRepository.updateExpireTime(employeeNumber, token, DateUtils.asDate(expireTime));
  }

  @Override
  public Integer kickOutOtherToken(final String employeeNumber, final String ip) {
    final LocalDateTime expireTime = LocalDateTime.now();
    return sysTokenRepository.kickOutOtherToken(employeeNumber, ip, DateUtils.asDate(expireTime));
  }

}
