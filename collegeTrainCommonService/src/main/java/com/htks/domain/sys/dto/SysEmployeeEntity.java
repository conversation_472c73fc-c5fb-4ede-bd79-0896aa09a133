package com.htks.domain.sys.dto;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.domain.AbstractUpdateTraceDomain;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 员工信息
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Getter
@Setter
public final class SysEmployeeEntity extends AbstractUpdateTraceDomain {

  private static final long serialVersionUID = 3989429736701073184L;

  //姓名
  private String employeeName;

  //工号
  private String employeeNumber;

  private String inPlantMail;

  //部门
  private String groupName;

  //部门ID
  private String groupId;

  //职务
  private String jobName;

  //职务ID
  private String jobId;

  //职位
  private String positionName;

  //职位ID
  private String positionId;

  //企业微信用户id
  private String wxworkUserid;

  //角色
  private String roles;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("id", getId()).
        add("name", getName()).
        add("employeeName", employeeName).
        add("employeeNumber", employeeNumber).
        add("groupName", groupName).
        add("groupId", groupId).
        add("jobName", jobName).
        add("jobId", jobId).
        add("positionName", positionName).
        add("positionId", positionId).
        add("roleList", roles).
        toString();
  }
}
