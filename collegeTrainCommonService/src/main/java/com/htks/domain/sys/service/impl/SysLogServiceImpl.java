package com.htks.domain.sys.service.impl;

import com.htks.domain.AbstractBaseServiceImpl;
import com.htks.domain.sys.dto.SysLogEntity;
import com.htks.domain.sys.dto.SysRoleEntity;
import com.htks.domain.sys.repository.hana.SysDefaultRepository;
import com.htks.domain.sys.repository.hana.SysLogRepository;
import com.htks.domain.sys.service.SysLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 系统日志Service层接口实现类
 *
 * <AUTHOR>
 * @date 2020/09/04
 */
@Slf4j
@Service("sysLogServiceImpl")
@Transactional(rollbackFor = Exception.class)
public class SysLogServiceImpl extends AbstractBaseServiceImpl implements SysLogService {

  private final SysLogRepository sysLogRepository;

  public SysLogServiceImpl(SysLogRepository sysLogRepository) {
    this.sysLogRepository = sysLogRepository;
  }

  @Override
  public void saveSysLog(SysLogEntity sysLog) {
    //保存系统日志
    sysLogRepository.saveSysLog(sysLog);
  }


}

