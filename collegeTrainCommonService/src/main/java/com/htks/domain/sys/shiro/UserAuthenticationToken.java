package com.htks.domain.sys.shiro;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.AuthenticationToken;

/**
 * 自定义Token
 *
 * <AUTHOR>
 * @date 2020/09/07
 */
@Slf4j
public final class UserAuthenticationToken implements AuthenticationToken {

  private static final long serialVersionUID = 992762125110186147L;

  /**
   * Token
   */
  private final String token;

  public UserAuthenticationToken(String token) {
    this.token = token;
  }

  @Override
  public Object getPrincipal() {
    if (log.isDebugEnabled()) {
      log.debug("UserAuthenticationToken.getPrincipal()-Token:{}", token);
    }
    return token;
  }

  @Override
  public Object getCredentials() {
    if (log.isDebugEnabled()) {
      log.debug("UserAuthenticationToken.getCredentials()-Token:{}", token);
    }
    return token;
  }
}
