package com.htks.domain.sys.dto;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.domain.AbstractUpdateTraceDomain;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色信息
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Getter
@Setter
public final class SysRoleEntity extends AbstractUpdateTraceDomain {

  private static final long serialVersionUID = -1675747470575413333L;

  //角色ID
  private String roleId;

  //角色名称
  private String roleName;

  //角色描述
  private String roleDescription;

  //资源(菜单)集合
  private List<SysResourceEntity> menuList;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("roleId", roleId).
        add("roleName", roleName).
        add("roleDescription", roleDescription).
        add("menuList", menuList).
        toString();
  }
}
