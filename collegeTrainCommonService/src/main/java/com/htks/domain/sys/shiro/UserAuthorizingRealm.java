package com.htks.domain.sys.shiro;

import com.auth0.jwt.interfaces.Claim;
import com.google.common.base.Strings;
import com.htks.common.utils.IPUtils;
import com.htks.common.utils.TokenGenerator;
import com.htks.domain.sys.dto.SysTokenEntity;
import com.htks.domain.sys.service.SysTokenService;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 自定义 Shiro认证实现
 *
 * <AUTHOR>
 * @date 2020/09/07
 */
@Slf4j
@Component("userAuthorizingRealm")
public final class UserAuthorizingRealm extends AuthorizingRealm {

  /**
   * 最大重试次数
   */
  private static final Integer MAX_RETRY_COUNT = 5;

  /**
   * 用于记录登录次数的key前缀
   */
  private static final String RETRY_CACHE = "passwordRetryCache";

  private final Cache<String, AtomicInteger> passwordRetryCache;

  private final SysTokenService sysTokenService;

  private final HttpServletRequest request;

  @Lazy
  @Autowired
  public UserAuthorizingRealm(@Qualifier("ehCacheManager") CacheManager cacheManager,
      SysTokenService sysTokenService, HttpServletRequest request) {
    this.sysTokenService = sysTokenService;
    this.passwordRetryCache = cacheManager.getCache(RETRY_CACHE);
    this.request = request;
  }

  /**
   * 重写supports方法，用于检测是否支持此Token
   */
  @Override
  public boolean supports(AuthenticationToken token) {
    return token instanceof UserAuthenticationToken;
  }

  /**
   * Shiro-用户授权(验证权限时调用)
   *
   * @param principalCollection principalCollection
   * @return AuthorizationInfo
   */
  @Override
  protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
    if (log.isDebugEnabled()) {
      log.debug("Shiro-用户授权:{}", principalCollection);
    }

    if (null == principalCollection) {
      throw new AuthorizationException("principalCollection is required.");
    }

    return new SimpleAuthorizationInfo();
  }

  /**
   * Shiro-用户认证(认证回调函数，验证登录时调用)
   *
   * @param authenticationToken authenticationToken
   * @return AuthenticationInfo
   */
  @Override
  protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) {
    if (log.isDebugEnabled()) {
      log.debug("Shiro-用户认证:{}", authenticationToken);
    }

    //获取Token
    final String accessToken = (String) authenticationToken.getPrincipal();

    // 获取重试次数
    AtomicInteger retryCount = passwordRetryCache.get(accessToken);
    if (null == retryCount) {
      retryCount = new AtomicInteger(0);
    }

    //重试次数自增1，如果重试过多，抛出异常
    if (retryCount.incrementAndGet() > MAX_RETRY_COUNT) {
      throw new ExcessiveAttemptsException("登录重试过多...");
    }

    //存入
    passwordRetryCache.put(accessToken, retryCount);
    if (log.isDebugEnabled()) {
      log.debug("用户:{}, 尝试登陆次数:{}...", accessToken, retryCount.get());
    }

    // 解密获得工号
    final String employeeNumber = TokenGenerator.getEmployeeNumber(accessToken);
    if (Strings.isNullOrEmpty(employeeNumber)) {
      throw new UnknownAccountException("无效的Token...");
    }

    //当前登录IP
    final String ip = IPUtils.getIpAddr(request);

    //查询用户Token信息
    final SysTokenEntity tokenEntity = sysTokenService.queryTokenByEmployeeNumberAndIp(employeeNumber, ip);
    //Token失效
    if (Objects.equals(tokenEntity, null) || Objects.equals(tokenEntity.getExpireTime(), null)
        || tokenEntity.getExpireTime().getTime() < System.currentTimeMillis()) {
      throw new IncorrectCredentialsException("Token已失效，请重新登录...");
    }

    /*//查询账户信息
    final SysAccountEntity sysAccountEntity = nullSysAccountEntity();

    //账户是否存在
    if (Boolean.TRUE.equals(sysAccountEntity.isNullObject())) {
      throw new UnknownAccountException("该账号不存在...");
    }

    //账号是否锁定
    if (Boolean.TRUE.equals(sysAccountEntity.getFreeze())) {
      throw new LockedAccountException("该账号已被锁定...");
    }*/

    //解码验证Token
    final Map<String, Claim> userData = TokenGenerator.verifyToken(accessToken, employeeNumber, employeeNumber);
    if (userData == null) {
      throw new IncorrectCredentialsException("账号密码不匹配...");
    }

    //验证成功清空重试次数
    passwordRetryCache.remove(accessToken);

    //return new SimpleAuthenticationInfo(sysAccountEntity, accessToken, getName());
    return new SimpleAuthenticationInfo(employeeNumber, accessToken, getName());
  }
}
