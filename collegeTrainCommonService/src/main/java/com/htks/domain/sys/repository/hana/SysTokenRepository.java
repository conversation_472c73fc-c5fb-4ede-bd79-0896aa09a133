package com.htks.domain.sys.repository.hana;


import com.htks.domain.sys.dto.SysTokenEntity;
import java.util.Date;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Token仓储接口
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Mapper
@Repository
public interface SysTokenRepository {

  /**
   * 根据工号查询Token
   *
   * @param employeeNumber 工号
   * @return Token
   */
  SysTokenEntity queryTokenByEmployeeNumber(@Param("employeeNumber") String employeeNumber);

  /**
   * 根据工号与登录IP查询Token
   *
   * @param employeeNumber 工号
   * @param ip             登录IP
   * @return Token
   */
  SysTokenEntity queryTokenByEmployeeNumberAndIp(@Param("employeeNumber") String employeeNumber, @Param("ip") String ip);

  /**
   * 保存Token信息
   *
   * @param token Token
   */
  void saveToken(SysTokenEntity token);

  /**
   * 更新Token信息
   *
   * @param token Token
   */
  void updateToken(SysTokenEntity token);

  /**
   * 更新过期时间
   *
   * @param employeeNumber 工号
   * @param token          Token
   * @param expireTime     过期时间
   * @return 是否成功
   */
  Boolean updateExpireTime(@Param("employeeNumber") String employeeNumber, @Param("token") String token,
      @Param("expireTime") Date expireTime);

  /**
   * 根据工号更新当前IP之外的其余Token的过期时间
   *
   * @param employeeNumber 工号
   * @param ip             当前IP
   * @param expireTime     过期时间
   * @return 踢掉的人数
   */
  Integer kickOutOtherToken(@Param("employeeNumber") String employeeNumber, @Param("ip") String ip, @Param("expireTime") Date expireTime);
}
