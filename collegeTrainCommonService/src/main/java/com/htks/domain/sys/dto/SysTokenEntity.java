package com.htks.domain.sys.dto;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.domain.AbstractUpdateTraceDomain;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;


/**
 * Token
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Getter
@Setter
public final class SysTokenEntity extends AbstractUpdateTraceDomain {

  private static final long serialVersionUID = 618243382852759963L;

  /**
   * 角色id
   */
  private String roleId;

  /**
   * 工号
   */
  @NotNull(message = "employeeNumber is required.")
  private String employeeNumber;

  /**
   * Token
   */
  @NotBlank(message = "token is required.")
  private String token;

  /**
   * 过期时间
   */
  private Date expireTime;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 登录IP
   */
  private String ip;

  public Date getExpireTime() {
    return obtainValidDate(expireTime);
  }

  public void setExpireTime(Date expireTime) {
    this.expireTime = obtainValidDate(expireTime);
  }

  @Override
  public Date getUpdatedTime() {
    return obtainValidDate(updateTime);
  }

  @Override
  public void setUpdatedTime(Date updateTime) {
    this.updateTime = obtainValidDate(updateTime);
  }

  @Override
  public String toString() {
    return toStringHelper(this).
        add("roleId", roleId).
        add("employeeNumber", employeeNumber).
        add("token", token).
        add("expireTime", expireTime).
        add("updateTime", updateTime).
        add("ip", ip).
        toString();
  }
}
