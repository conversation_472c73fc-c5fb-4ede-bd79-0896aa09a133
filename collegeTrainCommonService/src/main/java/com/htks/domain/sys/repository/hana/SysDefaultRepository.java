package com.htks.domain.sys.repository.hana;

import com.htks.domain.sys.dto.SysResourceEntity;
import com.htks.domain.sys.dto.SysRoleEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 员工仓储接口
 *
 * <AUTHOR>
 * @date 2022/06/21.
 */
@Mapper
@Repository
public interface SysDefaultRepository {

  /**
   * 查询角色信息
   *
   * @param employeeNumber 工号
   * @return 返回
   */
  SysRoleEntity getRoleByEmployee(@Param("employeeNumber") final String employeeNumber);

  /**
   * 查询角色信息 - 学员角色
   *
   * @param employeeNumber 工号
   * @return 返回
   */
  Integer getStudent(@Param("employeeNumber") final String employeeNumber);



}
