package com.htks.domain.sys;


import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.domain.AbstractBaseCondition;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 系统管理模块查询信息实体
 *
 * <AUTHOR>
 * @date 2020/09/08.
 */
@Getter
@Setter
public final class SysQueryCondition extends AbstractBaseCondition {

  //员工ID
  private String employeeId;

  //开始时间
  private String startDate;

  //结束时间
  private String endDate;

  //statusList
  private List<String> statusList;

  //companyList
  private List<String> companyList;

  //事业部
  private List<String> departmentList;

  //部门
  private List<String> groupList;

  //部门ID
  private String groupId;

  //stationList
  private List<String> stationList;

  //employeeNumberList
  private List<String> employeeNumberList;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("employeeId", employeeId).
        add("startDate", startDate).
        add("endDate", endDate).
        add("statusList", statusList).
        add("companyList", companyList).
        add("departmentList", departmentList).
        add("groupId", groupId).
        add("groupList", groupList).
        add("stationList", stationList).
        add("employeeNumberList", employeeNumberList).
        toString();
  }
}
