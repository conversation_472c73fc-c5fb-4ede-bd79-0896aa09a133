package com.htks.domain.sys.service;

import com.htks.domain.sys.dto.SysTokenEntity;

/**
 * Token Service层接口
 *
 * <AUTHOR>
 * @date 2020/09/04
 */
public interface SysTokenService {

  /**
   * 生成Token
   *
   * @param employeeNumber 工号
   * @param signature      签名
   * @param ip             登录IP
   * @return Token
   */
  SysTokenEntity createToken(final String employeeNumber, final String signature, final String ip);

  /**
   * 根据工号查询Token
   *
   * @param employeeNumber 工号
   * @return Token
   */
  SysTokenEntity queryTokenByEmployeeNumber(final String employeeNumber);

  /**
   * 根据工号与登录IP查询Token
   *
   * @param employeeNumber 工号
   * @param ip             登录IP
   * @return Token
   */
  SysTokenEntity queryTokenByEmployeeNumberAndIp(final String employeeNumber, final String ip);

  /**
   * 根据工号和token 更新过期时间
   *
   * @param employeeNumber 工号
   * @param token          Token
   * @return 是否成功
   */
  Boolean updateExpireTime(final String employeeNumber, final String token);

  /**
   * 根据工号踢掉当前IP之外的其他登录者
   *
   * @param employeeNumber 工号
   * @param ip             当前IP
   * @return 踢掉的人数
   */
  Integer kickOutOtherToken(final String employeeNumber, final String ip);

}
