package com.htks.domain.sys.service.impl;

import static com.google.common.base.MoreObjects.firstNonNull;
import static com.google.common.base.Strings.isNullOrEmpty;
import static com.google.common.collect.Lists.newArrayList;
import static com.htks.domain.sys.SysNullObjects.nullSysEmployeeEntity;
import static org.springframework.util.Assert.notNull;

import com.htks.domain.AbstractBaseServiceImpl;
import com.htks.domain.sys.SysQueryCondition;
import com.htks.domain.sys.dto.SysEmployeeEntity;
import com.htks.domain.sys.dto.SysResourceEntity;
import com.htks.domain.sys.dto.SysRoleEntity;
import com.htks.domain.sys.repository.hana.SysDefaultRepository;
import com.htks.domain.sys.repository.mysql.SysEmployeeRepository;
import com.htks.domain.sys.service.SysEmployeeService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 员工Service层接口实现类
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Slf4j
@Service("sysEmployeeServiceImpl")
@Transactional(rollbackFor = Exception.class)
public class SysEmployeeServiceImpl extends AbstractBaseServiceImpl implements SysEmployeeService {

  private final SysDefaultRepository sysDefaultRepository;

  private final SysEmployeeRepository sysEmployeeRepository;

  public SysEmployeeServiceImpl(SysDefaultRepository sysDefaultRepository, SysEmployeeRepository sysEmployeeRepository) {
    this.sysDefaultRepository = sysDefaultRepository;
    this.sysEmployeeRepository = sysEmployeeRepository;
  }

  @Override
  @Transactional(readOnly = true)
  public SysEmployeeEntity getEmployeeInfo(String employeeNumber) {
    notNull(employeeNumber, "The EmployeeNumber is required!");
    final SysEmployeeEntity employee = firstNonNull(sysEmployeeRepository.getEmployeeInfo(employeeNumber), nullSysEmployeeEntity());
    if (!isNullOrEmpty(employee.getEmployeeNumber())) {
      final SysRoleEntity roleResult = sysDefaultRepository.getRoleByEmployee(employee.getEmployeeNumber());
      if(!roleResult.isNullObject()){
        if(roleResult.getRoleId()!=null && roleResult.getRoleId().length()>0){
          employee.setRoles(roleResult.getRoleId());
        }
        else {
          employee.setRoles("");
        }
      }
    }
    return employee;
  }

  @Override
  @Transactional(readOnly = true)
  public List<SysEmployeeEntity> queryEmployeeByCondition(SysQueryCondition condition) {
    return sysEmployeeRepository.queryEmployeeByCondition(condition);
  }

  @Override
  @Transactional(readOnly = true)
  public SysRoleEntity getEmployeeRoleInfo(String employeeNumber) {
    return sysDefaultRepository.getRoleByEmployee(employeeNumber);
  }

    @Override
    public Integer getStudent(String employeeNumber) {
        return sysDefaultRepository.getStudent(employeeNumber);
    }
}
