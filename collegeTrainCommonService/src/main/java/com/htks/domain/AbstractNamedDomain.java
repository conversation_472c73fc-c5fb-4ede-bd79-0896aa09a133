package com.htks.domain;

import static com.google.common.base.MoreObjects.toStringHelper;
import static com.google.common.base.Strings.isNullOrEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 域Abstract类
 *
 * <AUTHOR>
 * @date 2010/09/09.
 */
@Getter
@Setter
public abstract class AbstractNamedDomain extends AbstractIdDomain {

  private static final long serialVersionUID = 299278004299391344L;
  @ApiModelProperty(hidden = true)
  private String name;

  public AbstractNamedDomain() {
    super();
    name = "";
  }

  @Override
  public Boolean isNullObject() {
    return this.getId() == null && isNullOrEmpty(getName());
  }

  @Override
  public String toString() {
    return toStringHelper(this)
        .add("id", getId())
        .add("name", name)
        .toString();
  }
}
