package com.htks.domain.ExamInformation.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
//学生成绩排名信息
@Getter
@Setter
public class ExamAllCollegeInformation {
    @ExcelProperty(value = "排名")
    @ApiModelProperty(value = "排名")
    private Integer ranking;

    @ExcelProperty(value = "厂别")
    @ApiModelProperty(value = "厂别")
    private String factoryType;

    @ExcelProperty(value = "批次")
    @ApiModelProperty(value = "批次")
    private String batch;

    @ExcelProperty(value = "班别")
    @ApiModelProperty(value = "班别")
    private String classNo;

    @ExcelProperty(value = "工号")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ExcelProperty(value = "姓名")
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ExcelProperty(value = "实习部门")
    @ApiModelProperty(value = "实习部门")
    private String department;

    @ExcelProperty(value = "区域")
    @ApiModelProperty(value = "区域")
    private String area;

    @ExcelProperty(value = "IQ测试")
    @ApiModelProperty(value = "IQ测试")
    private Double IQExam;
    @ExcelProperty(value = "新生见面会")
    @ApiModelProperty(value = "新生见面会")
    private Double meeting;
    @ExcelProperty(value = "HR公开课理论考试")
    @ApiModelProperty(value = "HR公开课理论考试")
    private Double HRExam;
    @ExcelProperty(value = "工艺公开课理论考试")
    @ApiModelProperty(value = "工艺公开课理论考试")
    private Double craftExam;
    @ExcelProperty(value = "BU公开课理论考试")
    @ApiModelProperty(value = "BU公开课理论考试")
    private Double BUExam;
    @ExcelProperty(value = "上岗证考核")
    @ApiModelProperty(value = "上岗证考核")
    private Double postExam;
    @ExcelProperty(value = "工程类实操考试")
    @ApiModelProperty(value = "工程类实操考试")
    private Double appointRecord;
    @ExcelProperty(value = "SOP制作")
    @ApiModelProperty(value = "SOP制作")
    private Double sopExam;
    @ExcelProperty(value = "异常答辩")
    @ApiModelProperty(value = "异常答辩")
    private Double exceptionRecord;
    @ExcelProperty(value = "工程结业理论考试")
    @ApiModelProperty(value = "工程结业理论考试")
    private Double projectExam;
    @ExcelProperty(value = "每周周报")
    @ApiModelProperty(value = "每周周报")
    private Double weeklyReport;
    @ExcelProperty(value = "培训组长主观评价")
    @ApiModelProperty(value = "培训组长主观评价")
    private Double subjectiveJudge;
    @ExcelProperty(value = "《为什么是华天》读后感")
    @ApiModelProperty(value = "《为什么是华天》读后感")
    private Double afterRead;

    @ExcelProperty(value = "总分")
    @ApiModelProperty(value = "总分")
    private Double score;
}
