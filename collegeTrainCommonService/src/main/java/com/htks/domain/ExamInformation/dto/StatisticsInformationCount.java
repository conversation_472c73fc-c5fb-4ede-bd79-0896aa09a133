package com.htks.domain.ExamInformation.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
//满意度统一
@Getter
@Setter
public class StatisticsInformationCount {
    @ExcelProperty(value = "批次")
    @ApiModelProperty(value = "批次")
    private String batch;
    @ExcelProperty(value = "实习部门")
    @ApiModelProperty(value = "实习部门")
    private String department;
    @ExcelProperty(value = "评价日期")
    @ApiModelProperty(value = "提交时间")
    private String createdTime;
    @ExcelProperty(value = "区域/制程")
    @ApiModelProperty(value = "区域")
    private String area;

    @ExcelProperty(value = "工号")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ExcelProperty(value = "姓名")
    @ApiModelProperty(value = "姓名")
    private String employeeName;




    @ExcelProperty(value = "授课时长")
    @ApiModelProperty(value = "理论培训-授课时长")
    private Integer theoryCourseDuration;

    @ExcelProperty(value = "授课专业性")
    @ApiModelProperty(value = "理论培训-授课专业性")
    private Integer theoryMajor;

    @ExcelProperty(value = "课程互动")
    @ApiModelProperty(value = "课程互动")
    private Integer theoryCourseInteraction;

    @ExcelProperty(value = "专业能力")
    @ApiModelProperty(value = "师傅评价-专业能力")
    private Integer masterMajor;

    @ExcelProperty(value = "课程&工作安排")
    @ApiModelProperty(value = "师傅评价-课程&工作安排")
    private Integer masterCourseArrange;

    @ExcelProperty(value = "实操教学")
    @ApiModelProperty(value = "师傅评价-实操教学")
    private Integer masterPracticalOperation;

    @ExcelProperty(value = "耐心负责")
    @ApiModelProperty(value = "师傅评价-耐心负责")
    private Integer masterPatience;

    @ExcelProperty(value = "每周答疑")
    @ApiModelProperty(value = "领导关心-每周答疑")
    private Integer leaderQuestion;


    @ExcelProperty(value = "座谈会")
    @ApiModelProperty(value = "领导关系-座谈会")
    private Integer leaderMeet;

    @ExcelProperty(value = "领导关怀")
    @ApiModelProperty(value = "领导关系-领导关怀")
    private Integer leaderCare;

    @ExcelProperty(value = "理论")
    @ApiModelProperty(value = "学习感受-理论")
    private Integer studyTheory;

    @ExcelProperty(value = "实操")
    @ApiModelProperty(value = "学习感受-实操")
    private Integer studyPracticalOperation;

    @ExcelProperty(value = "报告")
    @ApiModelProperty(value = "学习感受-报告")
    private Integer studyReport;


}
