package com.htks.domain.ExamInformation.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.sourceforge.pinyin4j.PinyinHelper;

//学生成绩信息
@Getter
@Setter
public class ExamInformation {

    @ExcelProperty(value = "厂别")
    @ApiModelProperty(value = "厂别")
    private String factoryType;

    @ExcelProperty(value = "批次")
    @ApiModelProperty(value = "批次")
    private String batch;


    @ExcelProperty(value = "班别")
    @ApiModelProperty(value = "班别")
    private String classNo;

    @ExcelProperty(value = "工号")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ExcelProperty(value = "姓名")
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ExcelProperty(value = "实习部门")
    @ApiModelProperty(value = "实习部门")
    private String department;

    @ExcelProperty(value = "区域")
    @ApiModelProperty(value = "区域")
    private String area;

    @ExcelProperty(value = "培训考试项目")
    @ApiModelProperty(value = "培训考试项目")
    private String examName;

    @ExcelProperty(value = "分制")
    @ApiModelProperty(value = "分制")
    private String theoryCourseInteraction;

    @ExcelProperty(value = "计算比重")
    @ApiModelProperty(value = "计算比重")
    private String masterMajor;

    @ExcelProperty(value = "评分")
    @ApiModelProperty(value = "评分")
    private Double masterCourseArrange;

    @ExcelProperty(value = "折算得分")
    @ApiModelProperty(value = "折算得分")
    private Double masterPracticalOperation;

    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注")
    private String masterPatience;

    @ApiModelProperty(hidden = true)
    private int assessedId;

    private String convertToPinyinInitials(String chinese) {
        StringBuilder pinyin = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
            if (pinyinArray != null) {
                pinyin.append(pinyinArray[0].charAt(0));
            } else {
                pinyin.append(c);
            }
        }
        return pinyin.toString().toUpperCase();
    }
}
