package com.htks.domain.ExamInformation.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
//满意度统一接口参数
@Getter
@Setter
public class StatisticsReasonBody {
    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "实习部门")
    private List<String> department;

    @ApiModelProperty(value = "区域")
    private List<String> area;

    @ApiModelProperty(value = "日期")
    private String createdTime;

    @ApiModelProperty(value = "日期")
    private String startTime;

    @ApiModelProperty(value = "日期")
    private String endTime;

}
