package com.htks.domain.ExamInformation.service.impl;

import com.htks.domain.ExamInformation.dto.ExamAllCollegeInformation;
import com.htks.domain.ExamInformation.dto.ExamInformation;
import com.htks.domain.ExamInformation.dto.ExamInformationReasonBody;
import com.htks.domain.ExamInformation.dto.StatisticsInformationCount;
import com.htks.domain.ExamInformation.repository.hana.SatisfactionCountRepository;
import com.htks.domain.ExamInformation.service.SatisfactionCountService;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.domain.common.dto.PostAddScore;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class SatisfactionCountServiceImpl implements SatisfactionCountService {
    @Autowired
    SatisfactionCountRepository satisfactionCountRepository;
    /*@Autowired
    SatisfactionCountService satisfactionCountService ;*/
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @Override
    public List<StatisticsInformationCount> getSatisfactionInformation(String startTime,String endTime,String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId) {

    if (chooseDepartmentName!=null&&chooseDepartmentName.size()>0){
        chooseDepartmentName=satisfactionCountRepository.getAreaId(chooseDepartmentName);
    }
        return satisfactionCountRepository.getSatisfactionInformation(startTime,endTime,batch,employeeName,employeeNo,chooseDepartmentName,practiceDepartmentAreaId);
    }

    @Override
    public List<Long> getAssessedId(String batch, String employeeName, String employeeNo, List<String> chooseDepartmentName, List<String> practiceDepartmentAreaId,List<String> factoryTypes,String classNo) {
        return satisfactionCountRepository.getAssessedId2(batch, employeeName, employeeNo, chooseDepartmentName, practiceDepartmentAreaId,factoryTypes,classNo);
    }


    @Override
    public List<ExamInformation> getHR(Long id) {
        return satisfactionCountRepository.getHR(id);
    }

    @Override
    public EmployeeEntity getWechatId(String employee) {
        return satisfactionCountRepository.getWechatId(employee);
    }

    public List<ExamInformation> getExamInformation(ExamInformationReasonBody reasonBody){
        DecimalFormat format = new DecimalFormat("0.00%");
        List<ExamInformation> informations=new ArrayList<>();
        List<String> list = new ArrayList<>();
        List<String>lista=new ArrayList<>();
        if (reasonBody.getDepartment()!=null) {
            for (int i = 0; i < reasonBody.getDepartment().size(); i++) {
                lista.add(uploadWorkLicense.getDepartmentName(reasonBody.getDepartment().get(i)));
            }

            List<String> factory = new ArrayList<>();
            if (reasonBody.getFactoryType() == null || reasonBody.getFactoryType().size()==0) {
                for (int i = 0; i < reasonBody.getDepartment().size(); i++) {
                    factory.add(uploadWorkLicense.getFactory(reasonBody.getDepartment().get(i)));
                }
                if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
                    reasonBody.setFactoryType(factory);
                } else if (factory.contains("华天昆山")) {
                    reasonBody.setFactoryType(factory);
                } else if (factory.contains("华天江苏")) {
                    reasonBody.setFactoryType(factory);
                }
            }
            reasonBody.setDepartment(lista);
        }

        try {
            if(reasonBody.getExam()==null||reasonBody.getExam().equals("")||reasonBody.getExam().size()==0) {
                list.add(0, "上岗证考核");
                list.add(1, "hr");
                list.add(2, "工程类实操考试");
                list.add(3, "异常答辩");
                list.add(4, "SOP制作");
                list.add(5, "新生见面会");
                list.add(6, "每周周报");
                list.add(7, "培训组长主观评价");
                list.add(8, "《为什么是华天》读后感");
                list.add(9, "工程结业理论考试");
            }else {
                 /*    if ("IQ测试HR公开理论课程考试工艺公开课理论考试BU公共理论考试工程结业理论考试".contains(list.get(i))) {
                    list.set(i,"hr");
                }*/
                for (int i = 0; i < reasonBody.getExam().size(); i++) {
                        if (reasonBody.getExam().get(i).contains("IQ测试")||reasonBody.getExam().get(i).contains("HR公开")||reasonBody.getExam().get(i).contains("工艺公开")||reasonBody.getExam().get(i).contains("BU公共")||reasonBody.getExam().get(i).contains("工程结业")){
                        list.add("hr");
                        break;
                    }
                }
                 list.addAll(reasonBody.getExam());
            }

            for (int i = 0; i < list.size(); i++) {
                switch (list.get(i)){
                    case "hr":
                        List <ExamInformation> hr= satisfactionCountRepository.getIqHrBu(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo());
                       if(hr.size()>0) {
                           for (ExamInformation examInformation:hr){
                               if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                   examInformation.setMasterCourseArrange(0.0);
                               }
                           }
                           Map<String, List<ExamInformation>> map = hr.stream().collect(Collectors.groupingBy(ExamInformation::getExamName));
                           for (int j = 0; j < hr.size(); j++) {
                               if (hr.get(j).getExamName().contains("工程结业理论考试")) {
//                                   hr.get(j).setMasterMajor("15%");
                                   hr.get(j).setMasterPracticalOperation(doubleTwo(hr.get(j).getMasterCourseArrange() * 0.1));
                               }else if(hr.get(j).getExamName().contains("工艺公开")){
                                   int assessedId = hr.get(j).getAssessedId();
                                   // 通过Id获取分数
//                                   String subjectScore = satisfactionCountRepository.getSubjectScore(assessedId);
                                   Double subjectScore = hr.get(j).getMasterCourseArrange();
                                   hr.get(j).setMasterCourseArrange(subjectScore);
                                   hr.get(j).setMasterPracticalOperation(doubleTwo(hr.get(j).getMasterCourseArrange() * 0.15));
                               }else if(hr.get(j).getExamName().contains("IQ")) {
                                   hr.get(j).setMasterMajor("4.00%");
                                   Double parse = new Double(format.parse(hr.get(j).getMasterMajor()).toString());
                                   hr.get(j).setMasterPracticalOperation(doubleTwo(hr.get(j).getMasterCourseArrange() * parse));
                               }else if(hr.get(j).getExamName().contains("英语")) {
                                   hr.get(j).setMasterMajor("0.00%");
                                   Double parse = new Double(format.parse(hr.get(j).getMasterMajor()).toString());
                                   hr.get(j).setMasterPracticalOperation(doubleTwo(hr.get(j).getMasterCourseArrange() * parse));
                               }else if(hr.get(j).getExamName().contains("HR公开课")){
                                   hr.get(j).setMasterMajor("3.00%");
                                   Double parse = new Double(format.parse(hr.get(j).getMasterMajor()).toString());
                                   hr.get(j).setMasterPracticalOperation(doubleTwo(hr.get(j).getMasterCourseArrange() * parse));
                               }else if(hr.get(j).getExamName().contains("BU公共课")){
                                   Double parse = new Double(format.parse(hr.get(j).getMasterMajor()).toString());
                                   hr.get(j).setMasterPracticalOperation(doubleTwo(hr.get(j).getMasterCourseArrange() * parse));
                               }
                           }
                           List<ExamInformation> informationAdd=new ArrayList<>();
                           if (reasonBody.getExam().size()==0){
                               informationAdd.addAll(hr);
                           }else {
                               for (int j = 0; j < hr.size(); j++) {
                                   for (int k = 0; k < reasonBody.getExam().size(); k++) {
                                       if (hr.get(j).getExamName().contains(reasonBody.getExam().get(k))) {
                                           informationAdd.add(hr.get(j));
                                       }
                                   }
                               }
                           }
                           informations.addAll(informationAdd);
                       }
                        break;
                    case "上岗证考核":
                        List<Long> Id=satisfactionCountRepository.getAssessedId2(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo());
                        for (int j = 0; j < Id.size(); j++) {
                            ExamInformation examInformation=new ExamInformation();
                            List <ExamInformation> list1ByPost =satisfactionCountRepository.getPost(Id.get(j));
                            List <ExamInformation> list1ByPostOther =satisfactionCountRepository.getPostOther(Id.get(j));

                            if(list1ByPost.size()==0){
                                continue;
                            }else {
                                for (ExamInformation examInformationPost:list1ByPost){
                                    if (examInformationPost.getMasterCourseArrange()==null||"".equals(examInformationPost.getMasterCourseArrange())){
                                        examInformationPost.setMasterCourseArrange(0.0);
                                    }
                                }
                                examInformation=list1ByPost.get(0);
                                if(list1ByPost.size()>=5) {
                                    examInformation.setMasterCourseArrange((double) (5 * 20) + (list1ByPostOther.size()+list1ByPost.size()-5));
                                    String employeeNo = satisfactionCountRepository.getEmployeeNoByAid(Id.get(j));
                                    PostAddScore postAddScore = satisfactionCountRepository.getAddScore(employeeNo);
                                    if (postAddScore != null){
                                        if (postAddScore.getType().equals("0")) {
                                            examInformation.setMasterCourseArrange(examInformation.getMasterCourseArrange() - postAddScore.getScore());
                                        } else {
                                            examInformation.setMasterCourseArrange(examInformation.getMasterCourseArrange() + postAddScore.getScore());
                                        }
                                }
                                }else {
                                    examInformation.setMasterCourseArrange((double) (list1ByPost.size() * 20) );
                                    String employeeNo = satisfactionCountRepository.getEmployeeNoByAid(Id.get(j));
                                    PostAddScore postAddScore = satisfactionCountRepository.getAddScore(employeeNo);
                                    if (postAddScore != null){
                                        if (postAddScore.getType().equals("0")) {
                                            examInformation.setMasterCourseArrange(examInformation.getMasterCourseArrange() - postAddScore.getScore());
                                        } else {
                                            examInformation.setMasterCourseArrange(examInformation.getMasterCourseArrange() + postAddScore.getScore());
                                        }
                                }
                                }
                            }
                            examInformation.setMasterPracticalOperation(doubleTwo(examInformation.getMasterCourseArrange()*0.1));
                            informations.add(examInformation);
                        }
                        break;
                    case "工程类实操考试":
                        List<Long> longs=satisfactionCountRepository.getAssessedId2(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo());

                        for (int j = 0; j < longs.size(); j++) {
                            ExamInformation examInformation = new ExamInformation();
                            List<ExamInformation> list1ByAppoint = satisfactionCountRepository.getAppointRecord(longs.get(j));
                            if (list1ByAppoint.size() == 0) {

                            } else {
                                if (list1ByAppoint.size() > 1) {
                                    for (ExamInformation examInformationAA:list1ByAppoint){
                                        if (examInformationAA.getMasterCourseArrange()==null||"".equals(examInformationAA.getMasterCourseArrange())){
                                            examInformationAA.setMasterCourseArrange(0.0);
                                        }
                                    }
                                    if (list1ByAppoint.get(1).getMasterCourseArrange() > 80) {
                                        examInformation = list1ByAppoint.get(1);
                                        double additionalPoints = satisfactionCountRepository.getAdditionalPoints(longs.get(j));
                                        examInformation.setMasterCourseArrange(doubleTwo(examInformation.getMasterCourseArrange()));
                                        Double parse = new Double(format.parse(examInformation.getMasterMajor()).toString());
                                        examInformation.setMasterPracticalOperation(doubleTwo(examInformation.getMasterCourseArrange() * parse* 0.8 + satisfactionCountRepository.getAdditionalPoints(longs.get(j))));
                                    } else {
                                        examInformation = list1ByAppoint.get(1);
                                        examInformation.setMasterCourseArrange(0.00);
                                    }
                                } else if (list1ByAppoint.size() ==1){
                                    examInformation = list1ByAppoint.get(0);
                                    Double parse = new Double(format.parse(examInformation.getMasterMajor()).toString());
if (examInformation.getMasterCourseArrange()!=null){
                                    examInformation.setMasterPracticalOperation(doubleTwo(examInformation.getMasterCourseArrange() * parse+ satisfactionCountRepository.getAdditionalPoints(longs.get(j))));
                                }
                                else {
    examInformation.setMasterPracticalOperation(0.0);
}}
                                examInformation.setMasterPatience(satisfactionCountRepository.getQuestion(longs.get(j)) + satisfactionCountRepository.getAnswer(longs.get(j)));

                                informations.add(examInformation);
                            }
                        }
                        break;
                    case "异常答辩":
                        List <ExamInformation> Exception=(satisfactionCountRepository.getExceptionRecord(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo()));
                        for (ExamInformation examInformation:Exception) {
                            if (examInformation.getMasterPracticalOperation() != null) {
                                DecimalFormat df = new DecimalFormat("0.00");
                                examInformation.setMasterPracticalOperation(Double.valueOf(df.format(examInformation.getMasterPracticalOperation())));
                            }
                        }
                        informations.addAll(Exception);
                        break;
                    case "SOP制作":
                        List <ExamInformation> sop=(satisfactionCountRepository.getSOP(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo()));
                      if(sop.size()>0) {
                          for (ExamInformation examInformation:sop){
                              if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                  examInformation.setMasterCourseArrange(0.0);
                              }
                          }
                          for (int j = 0; j < sop.size(); j++) {
                              Double parse = new Double(format.parse(sop.get(j).getMasterMajor()).toString());
                              sop.get(j).setMasterPracticalOperation(doubleTwo(sop.get(j).getMasterCourseArrange() * parse));
                          }
                          informations.addAll(sop);
                      }
                        break;
                    case "新生见面会":
                        List <ExamInformation> meeting=(satisfactionCountRepository.getMeeting(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo()));
                      if(meeting.size()>0) {
                          for (ExamInformation examInformation:meeting){
                              if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                  examInformation.setMasterCourseArrange(0.0);
                              }
                          }
                          for (int j = 0; j < meeting.size(); j++) {
                              Double parse = new Double(format.parse(meeting.get(j).getMasterMajor()).toString());
                              meeting.get(j).setMasterPracticalOperation(doubleTwo(meeting.get(j).getMasterCourseArrange() * parse));
                          }
                          informations.addAll(meeting);
                      }
                        break;
                    case "每周周报":
                        List<Long> weekId=satisfactionCountRepository.getAssessedId2(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo());
                        for (int z = 0; z < weekId.size(); z++) {
                            List<ExamInformation> week = (satisfactionCountRepository.getWeeklyReport(weekId.get(z)));
                            List<String> score=satisfactionCountRepository.getWeeklyScore(reasonBody.getEmployeeNo());
                            for (ExamInformation examInformation:week){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }
                            if (week.size() >0) {
                                ExamInformation examInformation = new ExamInformation();
                                examInformation = week.get(0);
                                double a = 0.0;
                                for (int j = 0; j < week.size(); j++) {
                                    a = a + week.get(j).getMasterCourseArrange();
                                }
                                double result = a / week.size(); // 执行计算
                                double roundedResult = Math.round(result * 10.0) / 10.0;
                                examInformation.setMasterCourseArrange(roundedResult);
                                 result = roundedResult*0.05;
                                roundedResult=  Math.round(result * 100.0) / 100.0;
                                examInformation.setMasterPracticalOperation(roundedResult);
                                informations.add(examInformation);
                            }
                        }
                        break;
                    case "培训组长主观评价":
                        List<ExamInformation> SubjectiveJudge=(satisfactionCountRepository.getSubjectiveJudge(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo()));
                        if(SubjectiveJudge.size()>0) {
                            for (ExamInformation examInformation:SubjectiveJudge){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }
                            for (int j = 0; j < SubjectiveJudge.size(); j++) {
                                Double parse = new Double(format.parse(SubjectiveJudge.get(j).getMasterMajor()).toString());
                                SubjectiveJudge.get(j).setMasterPracticalOperation(doubleTwo(SubjectiveJudge.get(j).getMasterCourseArrange() * parse));
                            }
                            informations.addAll(SubjectiveJudge);
                        }
                        break;
                    case "《为什么是华天》读后感":
                        List<ExamInformation> AfterRead=(satisfactionCountRepository.getAfterRead(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea(),reasonBody.getFactoryType(),reasonBody.getClassNo()));
                        if(AfterRead.size()>0) {
                            for (ExamInformation examInformation:AfterRead){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }
                            for (int j = 0; j < AfterRead.size(); j++) {
                                Double parse = new Double(format.parse(AfterRead.get(j).getMasterMajor()).toString());
                                AfterRead.get(j).setMasterPracticalOperation(doubleTwo(AfterRead.get(j).getMasterCourseArrange() * parse));
                            }
                            informations.addAll(AfterRead);
                        }
                       break;
                }
            }
            return  informations;
        }catch (Exception e){
            e.printStackTrace();
            return  informations;
        }
    }
    

    public List<ExamAllCollegeInformation> getExamInformationRanking(List<Long> id,List<String> exams) throws ParseException {

        List<ExamAllCollegeInformation> list = new ArrayList<>();
        if(exams.size()==0) {
            exams.add(0, "IQ测试");
            exams.add(1, "HR公开课理论考试");
            exams.add(2, "工艺公开课理论考试");
            exams.add(3, "BU公共课理论考试");
            exams.add(4, "上岗证考核");
            exams.add(5, "工程类实操考试");
            exams.add(6, "SOP制作");
            exams.add(7, "异常答辩");
            exams.add(8, "工程结业理论考试");
            exams.add(9, "每周周报");
            exams.add(10, "培训组长主观评价");
            exams.add(11, "《为什么是华天》读后感");
        }
        DecimalFormat format = new DecimalFormat("0.00%");
        for (int i = 0; i < id.size(); i++) {
            ExamAllCollegeInformation exam = satisfactionCountRepository.getExamAllCollegeInformation(id.get(i));
            exam.setIQExam(0.0);exam.setMeeting(0.0);exam.setHRExam(0.00);exam.setCraftExam(0.0);exam.setBUExam(0.00);exam.setPostExam(0.0);
                    exam.setAppointRecord(0.0);exam.setSopExam(0.0);exam.setExceptionRecord(0.0);exam.setProjectExam(0.0);exam.setWeeklyReport(0.0);
                    exam.setSubjectiveJudge(0.0);
                   exam.setAfterRead(0.0);
            exam.setIQExam(0.0); exam.setMeeting(0.0);
            for (int j = 0; j < exams.size(); j++) {
                switch (exams.get(j)) {
                    case "IQ测试":{
                    List<ExamInformation> IQ = satisfactionCountRepository.getIQRanking(id.get(i));
                    if (IQ.size() != 0) {
                        for (ExamInformation examInformation:IQ){
                            if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                examInformation.setMasterCourseArrange(0.0);
                            }
                        }

                        Double parse = new Double(format.parse(IQ.get(0).getMasterMajor()).toString());
                        double iq = doubleTwo(IQ.get(0).getMasterCourseArrange() * parse);
                        exam.setIQExam(iq);
                    }
                    break;
                    }
                    case "新生见面会": {
                        List<ExamInformation> Meeting = satisfactionCountRepository.getMeetingRanking(id.get(i));
                        if (Meeting.size() != 0) {
                            for (ExamInformation examInformation:Meeting){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }

                            Double parse = new Double(format.parse(Meeting.get(0).getMasterMajor()).toString());
                            double iq =doubleTwo( Meeting.get(0).getMasterCourseArrange() * parse);
                            exam.setMeeting(iq);
                        }
                        break;
                    }
                    case "HR公开课理论考试": {
                        List<ExamInformation> HR = satisfactionCountRepository.getHR(id.get(i));
                        if (HR.size() != 0) {
                            for (ExamInformation examInformation:HR){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.00);
                                }
                            }
                            Double parse = new Double(format.parse(HR.get(0).getMasterMajor()).toString());
                            NumberFormat nf=new  DecimalFormat( "0.00");
                            if (HR.size()==2) {
                                Double a=HR.get(0).getMasterCourseArrange() + HR.get(1).getMasterCourseArrange();
                                double b=a/2;
                                double c=b*parse;
                                double iq = doubleTwo(((HR.get(0).getMasterCourseArrange() + HR.get(1).getMasterCourseArrange()) / 2 * parse));
                                exam.setHRExam(iq);
                            }else {
                                double iq = doubleTwo(((HR.get(0).getMasterCourseArrange()/ 2  * parse)));
                                exam.setHRExam(iq);
                            }
                        }
                        break;
                    }
                    case "工艺公开课理论考试": {
                        List<ExamInformation> craft = satisfactionCountRepository.getCraft(id.get(i));
                        if (craft.size() != 0) {
                            NumberFormat nf=new  DecimalFormat( "0.0");
                            Double parse = new Double(format.parse(craft.get(0).getMasterMajor()).toString());
                           for (ExamInformation examInformation:craft){
                               if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                   examInformation.setMasterCourseArrange(0.0);
                               }
                           }
                            double iq =doubleTwo(craft.get(0).getMasterCourseArrange() * parse) ;
                            exam.setCraftExam(iq);
//                           if (craft.size()==1){
//                            double iq =doubleTwo(craft.get(0).getMasterCourseArrange() * parse );
//                            exam.setCraftExam(iq);}
//                           else {
//                               double iq = doubleTwo((craft.get(0).getMasterCourseArrange()/ 2  * parse));
//                               exam.setCraftExam(iq);
//                           }
                        }
                        break;
                    }
                    case "BU公共课理论考试": {
                        List<ExamInformation> BU = satisfactionCountRepository.getBU(id.get(i));
                        if (BU.size() == 2) {
                            for (ExamInformation examInformation:BU){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }
                            NumberFormat nf=new  DecimalFormat( "0.0");
                            Double parse = new Double(format.parse(BU.get(0).getMasterMajor()).toString());
                                double iq =doubleTwo( Double.parseDouble(nf.format((BU.get(0).getMasterCourseArrange() + BU.get(1).getMasterCourseArrange())  * parse/ 2)));
                                exam.setBUExam(iq);
                        }
                        if (BU.size() == 1) {
                            for (ExamInformation examInformation:BU){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }
                            NumberFormat nf=new  DecimalFormat( "0.0");
                            Double parse = new Double(format.parse(BU.get(0).getMasterMajor()).toString());
                            double iq =doubleTwo(((BU.get(0).getMasterCourseArrange()) / 2  * parse));
                            exam.setBUExam(iq);
                        }
                        break;
                    }
                    case "上岗证考核": {
                        List<ExamInformation> post = satisfactionCountRepository.getPost(id.get(i));
                        if (post.size() != 0) {
                            ExamInformation examInformationOther=new ExamInformation();
                            for (ExamInformation examInformation:post){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                                List <ExamInformation> list1ByPost =satisfactionCountRepository.getPost(id.get(i));
                                List <ExamInformation> list1ByPostOther =satisfactionCountRepository.getPostOther(id.get(i));
                                if(list1ByPost.size()>=5){
                                    examInformationOther.setMasterCourseArrange((double) (5 * 20)+(list1ByPostOther.size()+list1ByPost.size()-5));
                                    String employeeNo=satisfactionCountRepository.getEmployeeNoByAid(id.get(i));
                                    PostAddScore postAddScore=satisfactionCountRepository.getAddScore(employeeNo);
                                    if (postAddScore!=null) {
                                        if (postAddScore.getType().equals("0")) {
                                            examInformationOther.setMasterCourseArrange(examInformationOther.getMasterCourseArrange() - postAddScore.getScore());
                                        } else {
                                            examInformationOther.setMasterCourseArrange(examInformationOther.getMasterCourseArrange() + postAddScore.getScore());
                                        }
                                    }
                                }else {
                                    examInformationOther.setMasterCourseArrange((double) (list1ByPost.size() * 20));
                                    String employeeNo=satisfactionCountRepository.getEmployeeNoByAid(id.get(i));
                                    PostAddScore postAddScore=satisfactionCountRepository.getAddScore(employeeNo);
                                    if (postAddScore!=null) {
                                        if (postAddScore.getType().equals("0")) {
                                            examInformationOther.setMasterCourseArrange(examInformationOther.getMasterCourseArrange() - postAddScore.getScore());
                                        } else {
                                            examInformationOther.setMasterCourseArrange(examInformationOther.getMasterCourseArrange() + postAddScore.getScore());
                                        }
                                    }
                            }
                            }
                            Double parse = new Double(format.parse(post.get(0).getMasterMajor()).toString());
                            double iq = doubleTwo(examInformationOther.getMasterCourseArrange() * parse);
                            exam.setPostExam(iq);
                        }
                        break;
                    }
                    //
                    case "工程类实操考试": {
                        List<ExamInformation> appoint = satisfactionCountRepository.getAppointRecordRanking(id.get(i));
                        if (appoint.size() != 0) {
                            double iq = 0;
                            Double parse = new Double(format.parse(appoint.get(0).getMasterMajor()).toString());
                            for (ExamInformation examInformation:appoint){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }
                            if (appoint.size() > 1) {
                                if (appoint.get(1).getMasterCourseArrange() > 80) {
                                    double additionalPoints = satisfactionCountRepository.getAdditionalPoints(id.get(i));
                                    iq =doubleTwo ((appoint.get(1).getMasterCourseArrange() * 0.8) * parse+ additionalPoints);
                                } else {
                                    iq = 0.0;
                                }
                            } else if (appoint.get(0).getMasterCourseArrange() > 80) {
                                Double additionalPoints = satisfactionCountRepository.getAdditionalPoints(id.get(i));
                                if (additionalPoints==null){
                                    additionalPoints=0.0;
                                }
                                iq = doubleTwo((appoint.get(0).getMasterCourseArrange()) * parse+ additionalPoints);
                            } else {
                                iq = 0.0;
                            }
                            exam.setAppointRecord(iq);
                        }
                        break;
                    }
                    //
                    case "工程结业理论考试": {
                        List<ExamInformation> project = satisfactionCountRepository.getProject(id.get(i));
                        if (project.size() != 0) {
                            for (ExamInformation examInformation:project){
                                if (examInformation.getMasterCourseArrange()==null||"".equals(examInformation.getMasterCourseArrange())){
                                    examInformation.setMasterCourseArrange(0.0);
                                }
                            }
                            Double parse = new Double(format.parse(project.get(0).getMasterMajor()).toString());
                            double iq = doubleTwo(project.get(0).getMasterCourseArrange() * parse);
                            exam.setProjectExam(iq);
                        }
                        break;
                    }
//}
                    case "SOP制作": {
                        List<ExamInformation> sop = satisfactionCountRepository.getSOPRanking(id.get(i));
                        if (sop.size() != 0) {
                            double iq;
                            Double parse = new Double(format.parse(sop.get(0).getMasterMajor()).toString());
                            if(sop.get(0).getMasterCourseArrange()==null) {
                                iq = 0.0;
                            }
                            else {
                                    iq = doubleTwo(sop.get(0).getMasterCourseArrange() * parse);
                            }
                            exam.setSopExam(iq);
                        }
                        break;
                    }
                    case "异常答辩": {
                        List<ExamInformation> exception = satisfactionCountRepository.getExceptionRecordRanking(id.get(i));
                        if (exception.size() != 0) {
                            exam.setExceptionRecord(exception.get(0).getMasterCourseArrange());
                            if (exam.getExceptionRecord()==null){
                                exam.setExceptionRecord(0.0);
                            }
                        }
                        break;
                    }
//
                    case "每周周报": {
                        List<ExamInformation> week = satisfactionCountRepository.getWeeklyReportRanking(id.get(i));
                        if (week.size() != 0) {
                            double iq=0.0;
                                for (int z = 0; z < week.size(); z++) {
                                    if (week.get(z).getMasterCourseArrange()==null){
                                        week.get(z).setMasterCourseArrange(0.0);
                                    }
                                    iq =iq+week.get(z).getMasterCourseArrange();
                                }
                            Double parse = doubleTwo(new Double(format.parse(week.get(0).getMasterMajor()).toString()));
                            double result = iq/week.size()*parse; // 执行计算
                            double roundedResult = Math.round(result * 100.0) / 100.0;
                            exam.setWeeklyReport(doubleTwo(roundedResult));
                        }
                        break;
                    }
                    case "培训组长主观评价": {
                        List<ExamInformation> subject = satisfactionCountRepository.getSubjectiveJudgeRanking(id.get(i));
                        if (subject.size() != 0) {
                            Double parse = new Double(format.parse(subject.get(0).getMasterMajor()).toString());
                            double iq = doubleTwo(subject.get(0).getMasterCourseArrange() * parse);
                            exam.setSubjectiveJudge(iq);
                        }
                        break;
                    }
                    case "《为什么是华天》读后感": {
                        List<ExamInformation> afterRead = satisfactionCountRepository.getAfterReadRanking(id.get(i));
                        if (afterRead.size() != 0) {
                            double iq;
                            Double parse = new Double(format.parse(afterRead.get(0).getMasterMajor()).toString());
                            if(afterRead.get(0).getMasterCourseArrange()==null) {
                                iq = 0.0;
                            }
                            else {
                                iq = doubleTwo(afterRead.get(0).getMasterCourseArrange() * parse);
                            }
                            exam.setAfterRead(iq);
                        }
                        break;
                    }
                }
            }
            exam.setScore(exam.getIQExam()+exam.getMeeting()+exam.getHRExam()+exam.getCraftExam()+exam.getBUExam()+exam.getPostExam()
                    +exam.getAppointRecord()+exam.getSopExam()+exam.getExceptionRecord()+exam.getProjectExam()+exam.getWeeklyReport()+exam.getSubjectiveJudge()
                    +exam.getAfterRead());
            list.add(exam);
        }return list;

    }

    // 此方法未被调用
//    public List<ExamInformation> getExamInformation2(ExamInformationReasonBody reasonBody){
//        DecimalFormat format = new DecimalFormat("0.00%");
//        List<ExamInformation> informations=new ArrayList<>();
//        try {
//            List<String> list=reasonBody.getExam();
//            list.add(0,"上岗证考核");
//            list.add(0,"IQ测试HR公开理论课程考试工艺公开课理论考试BU公共理论考试");
//            for (int i = 0; i < list.size(); i++) {
//                if ("IQ测试HR公开理论课程考试工艺公开课理论考试BU公共理论考试".contains(list.get(i))) {
//                    list.set(i,"HR");
//                }
//                switch (list.get(i)){
//                    case "上岗证考核":
//                        List<Long> Id=satisfactionCountRepository.getAssessedId(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea());
//                        for (int j = 0; j < Id.size(); j++) {
//
//                            ExamInformation examInformation=new ExamInformation();
//                            List <ExamInformation> list1ByPost =satisfactionCountRepository.getPost(Id.get(j));
//                            List <ExamInformation> list1ByPostOther =satisfactionCountRepository.getPostOther(Id.get(j));
//
//                            if(list1ByPost.size()==0){
//
//                            }else {
//                                examInformation=list1ByPost.get(0);
//                                if(list1ByPost.size()>5){
//                                    examInformation.setMasterCourseArrange((double) (list1ByPost.size() * 20)+(list1ByPostOther.size()));
//                                }else {
//                                    examInformation.setMasterCourseArrange((double) (list1ByPost.size() * 20)+(list1ByPostOther.size()));
//                                }
//                                Double parse = new Double(format.parse(examInformation.getMasterMajor()).toString());
//                                examInformation.setMasterPracticalOperation(examInformation.getMasterCourseArrange()*parse);
//                                informations.add(examInformation);
//                            }
//
//                        }
//                        break;
//                    case "工程类实操考试":
//                        List<Long> longs=satisfactionCountRepository.getAssessedId(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea());
//
//                        for (int j = 0; j < longs.size(); j++) {
//                            ExamInformation examInformation=new ExamInformation();
//                            List <ExamInformation> list1ByAppoint =satisfactionCountRepository.getAppointRecord(longs.get(j));
//                            if(list1ByAppoint.size()==0) {
//
//                            }
//                            else{
//                            if(list1ByAppoint.size()>1){
//                                if (list1ByAppoint.get(1).getMasterCourseArrange()>80){
//                                    examInformation =list1ByAppoint.get(1);
//                                    double additionalPoints=satisfactionCountRepository.getAdditionalPoints(longs.get(j));
//                                    examInformation.setMasterCourseArrange(doubleTwo(examInformation.getMasterCourseArrange()*0.8+additionalPoints));
//                                }
//                                else {
//                                    examInformation =list1ByAppoint.get(1);
//                                    examInformation.setMasterCourseArrange(0.00);
//                                }
//                            }
//                            else {
//                                examInformation =list1ByAppoint.get(0);
//                            }
//                            examInformation.setMasterPatience(satisfactionCountRepository.getAnswer(longs.get(j))+satisfactionCountRepository.getQuestion(longs.get(j)));
//                            Double parse = new Double(format.parse(examInformation.getMasterMajor()).toString());
//                            examInformation.setMasterPracticalOperation(examInformation.getMasterCourseArrange()*parse);
//                            informations.add(examInformation);
//                        }
//                        }
//                        break;
//                    case "每周周报":
//                        List<Long> weekId=satisfactionCountRepository.getAssessedId(reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea());
//                        for (int z = 0; z < weekId.size(); z++) {
//                            List<ExamInformation> week=(satisfactionCountRepository.getWeeklyReport(weekId.get(z)));
//                            if(week.size()==0){
//
//                            }else {
//                                ExamInformation examInformation = new ExamInformation();
//                                examInformation = week.get(0);
//                                double a = 0.0;
//                                for (int j = 0; j < week.size(); j++) {
//                                    a = a + week.get(i).getMasterCourseArrange();
//                                }
//                                Double parse = new Double(format.parse(examInformation.getMasterMajor()).toString());
//                                examInformation.setMasterCourseArrange(doubleTwo(a / 10));
//                                examInformation.setMasterPracticalOperation(doubleTwo(examInformation.getMasterCourseArrange() * parse));
//                                informations.add(examInformation);
//                            }
//                        }
//                        break;
//                }
//
//            }
//            List<ExamInformation> hr= satisfactionCountRepository.getAllExamInformation(list,reasonBody.getBatch(),reasonBody.getEmployeeName(),reasonBody.getEmployeeNo(),reasonBody.getDepartment(), reasonBody.getArea());
//           if(hr.size()!=0){
//               for (int j = 0; j < hr.size(); j++) {
//                   Double parse = new Double(format.parse(hr.get(j).getMasterMajor()).toString());
//                   hr.get(j).setMasterPracticalOperation(doubleTwo(hr.get(j).getMasterCourseArrange()*parse));
//               }
//               informations.addAll(hr);
//           }
//            return  informations;
//        }catch (Exception e){
//            e.printStackTrace();
//            return  informations;
//        }
//    }
    private Double doubleTwo(Double d){
        BigDecimal bd = new BigDecimal(d);
        bd = bd.setScale(2, BigDecimal.ROUND_HALF_UP);

        double result = bd.doubleValue();
        return  result;
    }
}
