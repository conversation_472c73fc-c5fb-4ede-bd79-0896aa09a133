package com.htks.domain.ExamInformation.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
//排名以及成绩接口参数对象
@Getter
@Setter
public class ExamInformationReasonBody {
    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "厂别")
    private List<String> factoryType = new ArrayList<>();

    @ApiModelProperty(value = "班别")
    private String classNo;

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "实习部门")
    private List<String> department;

    @ApiModelProperty(value = "区域")
    private List<String> area;

    @ApiModelProperty(value = "考试项目")
    private List<String> exam;

}
