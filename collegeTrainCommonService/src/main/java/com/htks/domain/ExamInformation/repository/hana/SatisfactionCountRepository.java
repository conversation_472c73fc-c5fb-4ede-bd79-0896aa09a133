package com.htks.domain.ExamInformation.repository.hana;

import com.htks.domain.ExamInformation.dto.ExamAllCollegeInformation;
import com.htks.domain.ExamInformation.dto.ExamInformation;
import com.htks.domain.ExamInformation.dto.ExamInformationReasonBody;
import com.htks.domain.ExamInformation.dto.StatisticsInformationCount;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.domain.common.dto.PostAddScore;
import com.htks.domain.school.dto.CollegeTrainWeeklyReport;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SatisfactionCountRepository {
    List<StatisticsInformationCount> getSatisfactionInformation(String startTime,String endTime,String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId );
    List<String>  getAreaId(List<String>chooseDepartmentName);
    /*获取IQ，HR1,HR2,工艺1，工艺2，BU1,BU2,工程结业考试*/
    List<ExamInformation> getIqHrBu(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId,List<String> factoryTypes,String classNo );

    /*获取上岗证*/
    List<ExamInformation> getPost(Long id);

    List<ExamInformation> getPostOther(Long id);

    /*获取工程实操*/
    List<ExamInformation> getAppointRecord(Long id );

    /*异常答辩*/
    List<ExamInformation> getExceptionRecord(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId ,List<String> factoryTypes,String classNo);

    /*获取SOP*/
    List<ExamInformation> getSOP(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId ,List<String> factoryTypes,String classNo);

    /*获取'YYYY-MM-DD HH24:MI:SS'*/
    List<ExamInformation> getMeeting(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId,List<String> factoryTypes,String classNo );

    /*每周周报*/
    List<ExamInformation> getWeeklyReport(Long id);
    List<String> getWeeklyScore(String id);
    /*组长评价*/
    List<ExamInformation> getSubjectiveJudge(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId,List<String> factoryTypes,String classNo );

    /*什么是华天读后感*/
    List<ExamInformation> getAfterRead(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId,List<String> factoryTypes,String classNo );

    /*获取所有*/
    List<ExamInformation> getAllExamInformation(List<String>list,String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId );

    /*获取AssessedId*/
    List<Long> getAssessedId(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId );
    List<Long> getAssessedId2(String batch,String employeeName,String employeeNo,List<String>chooseDepartmentName,List<String>practiceDepartmentAreaId ,List<String> factoryTypes,String classNo);

    /*获取附加分*/
    Double getAdditionalPoints(Long id);

    /*获取工程类问题*/
    String getQuestion(Long id);

    /*获取工程类答案*/
    String getAnswer(Long id);


  /*  大学生培训考试统计报表*/

    List<ExamInformation> getIQRanking(Long id);

    List<ExamInformation> getMeetingRanking(Long id);

    List<ExamInformation> getCraft(Long id);

    List<ExamInformation> getHR(Long id);

    List<ExamInformation> getBU(Long id);

    List<ExamInformation> getPostRanking(Long id);

    List<ExamInformation> getProject(Long id);

    List<ExamInformation> getAppointRecordRanking(Long id);

    List<ExamInformation> getSOPRanking(Long id);

    List<ExamInformation> getExceptionRecordRanking(Long id);

    List<ExamInformation> getWeeklyReportRanking(Long id);

    List<ExamInformation> getSubjectiveJudgeRanking(Long id);

    List<ExamInformation> getAfterReadRanking(Long id);

    ExamAllCollegeInformation getExamAllCollegeInformation(Long id);
  /*  获取微信号*/
    EmployeeEntity getWechatId(String employee);
    //获取上岗证考试加分项
    PostAddScore getAddScore(String employeeNo);

    String getSubjectScore(int id);

    String getExamScore(int id);

    String getEmployeeNoByAid(long id);

}
