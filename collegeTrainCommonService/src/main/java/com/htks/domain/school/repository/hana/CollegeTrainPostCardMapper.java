package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.htks.domain.school.dto.CollegeTrainPostCard;
import com.htks.domain.school.request.CollegeTrainPostCardRequest;
import com.htks.domain.school.vo.CollegeTrainPostCardGroupVo;
import com.htks.domain.school.vo.CollegeTrainPostCardVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 大学生培训系统 上岗证 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainPostCardMapper extends BaseMapper<CollegeTrainPostCard> {

    @Select("<script>" +
            "SELECT ctpc.ASSESSED_ID ,count(ctpc.ASSESSED_ID)\n" +
            "FROM COLLEGE_TRAIN_POST_CARD ctpc \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.ID = ctpc.ASSESSED_ID\n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON ctpa.ID = cta.PRACTICE_DEPARTMENT_AREA_ID\n" +
            "WHERE cta.DELETED_FLAG = FALSE "+
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.factoryFlag !=null and request.factoryFlag !=''\">  " +
            "    and ctpa.FACTORY_FLAG = #{request.factoryFlag}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            " GROUP BY ctpc.ASSESSED_ID  "+
            "</script>")
    IPage<CollegeTrainPostCardVo> queryPaperList(@Param("page") Page<CollegeTrainPostCardVo> page, @Param("request") CollegeTrainPostCardRequest request);

    @Select("SELECT ASSESSED_ID ,POST_AREA  ,count(1) as count\n" +
            "FROM COLLEGE_TRAIN_POST_CARD ctpc \n" +
            "GROUP BY ASSESSED_ID ,POST_AREA ")
    List<CollegeTrainPostCardGroupVo> getByAssessedIdList(@Param("assessedIdList") List<Long> assessedIdList);
}
