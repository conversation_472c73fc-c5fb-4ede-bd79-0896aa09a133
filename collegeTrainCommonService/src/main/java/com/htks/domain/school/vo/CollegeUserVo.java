package com.htks.domain.school.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/9 10:56
 */
@Data
public class CollegeUserVo {

    private Long id;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("员工工号")
    private String employeeNo;

    @ApiModelProperty("是否启用")
    private String enabled;

    @ApiModelProperty("角色:管理员100,阅卷师101,培训组组长102")
    private String roleId;

    private String updatedUser;

    private Long znId;

    @ApiModelProperty("读后感截止上传时间")
    private String afterReadingUploadDeadTime;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("选岗部门名称")
    private String chooseDepartmentName;

    @ApiModelProperty("入职时间")
    private String entryDate;

    @ApiModelProperty("异常答辩截止时间")
    private String exceptionDefenseEndTime;

    @ApiModelProperty("异常答辩开始时间")
    private String exceptionDefenseStartTime;

    @ApiModelProperty("师傅姓名")
    private String masterName;

    @ApiModelProperty("师傅工号")
    private String masterNo;

    @ApiModelProperty("上岗证获取截止时间")
    private String postCertificateEndTime;

    @ApiModelProperty("上岗证获取开始时间")
    private String postCertificateStartTime;

    @ApiModelProperty("工程实操考试截止时间")
    private String practicalOperationEndTime;

    @ApiModelProperty("工程实操考试开始时间")
    private String practicalOperationStartTime;

    @ApiModelProperty("实习部门ID")
    private String practiceDepartmentAreaId;

//    @ApiModelProperty("SOP截止上传时间")
//    private String sopUploadDeadTime;
}
