package com.htks.domain.school.base;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/9 9:19
 */
@Getter
@Setter
@NoArgsConstructor
public class BaseEntity extends Convert {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(hidden = true)
    private Date updatedTime;

    @TableField("CREATED_USER")
    @ApiModelProperty(hidden = true)
    private String createdUser;

    /**
     * 删除标志 0 存在  1删除
     */
    //@TableLogic
    @TableField("DELETED_FLAG")
    private String deletedFlag;



}
