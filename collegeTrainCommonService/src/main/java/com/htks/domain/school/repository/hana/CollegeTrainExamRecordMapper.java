package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.excel.CollegeTrainExamRecordExportExcel;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 大学生 考试记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainExamRecordMapper extends BaseMapper<CollegeTrainExamRecord> {

    @Select("<script>" +
            "SELECT cter.Id, cter.TOTAL_SCORE, cter.EXAM_NUMBER ,cter.EXAM_NAME ,TO_NVARCHAR(cter.CREATED_TIME ,'YYYY-MM-DD HH24:MI:SS')as examStartTime," +
            "TO_NVARCHAR(cter.UPDATED_TIME,'YYYY-MM-DD HH24:MI:SS') as examEndTime ,cter.JUDGE_NO ,cter.JUDGE_NAME ," +
            "cter.EXAM_TYPE,cter.PAPER_NAME ,ctec.PAPER_TYPE ," +
            "cter.JUDGE_NUMBER ,cter.ALLOCATION_STATUS ,cta.BATCH ,cta.EMPLOYEE_NO ," +
            "cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName,ctpa.AREA ,cter.EVALUATE_REASON,ctss.SCORE as subjectiveScore FROM \n" +
            "COLLEGE_TRAIN_EXAM_RECORD cter \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cter .ASSESSED_ID  = cta.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_SUBJECTIVE_SCORE ctss ON ctss.EMPLOYEE_NUMBER  = cta.EMPLOYEE_NO \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID  = ctpa.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON cter.EXAM_CATEGORY_ID = ctec.ID "+
            "WHERE cter.DELETED_FLAG  = FALSE AND cta.DELETED_FLAG =FALSE and ctec.ID in ('4','5')" +
            "<if test=\"request.allocationStatus !=null and request.allocationStatus !=''\">  " +
            "    and cter.ALLOCATION_STATUS = #{request.allocationStatus}  " +
            "</if>  " +
            "<if test=\"request.examName !=null and request.examName !=''\">  " +
            "    and cter.EXAM_NAME = #{request.examName}  " +
            "</if>  " +
            "<if test=\"request.examNumber !=null and request.examNumber !=''\">  " +
            "    and cter.EXAM_NUMBER = #{request.examNumber}  " +
            "</if>  " +
            "<if test=\"request.judgeNo !=null and request.judgeNo !=''\">  " +
            "    and cter.JUDGE_NO = #{request.judgeNo}  " +
            "</if>  " +
            "<if test=\"request.judgeName !=null and request.judgeName !=''\">  " +
            "    and cter.JUDGE_NAME = #{request.judgeName}  " +
            "</if>  " +
            "<if test=\"request.judgeNumber !=null and request.judgeNumber !=''\">  " +
            "    and cter.JUDGE_NUMBER = #{request.judgeNumber}  " +
            "</if>  " +
            "<if test=\"request.examStartTime !=null and request.examStartTime !='' \">  " +
            "    and cter.EXAM_START_TIME <![CDATA[>= ]]> #{request.examStartTime}  " +
            "</if>  " +
            "<if test=\"request.examEndTime !=null and request.examEndTime !=''\">  " +
            "    and cter.EXAM_END_TIME <![CDATA[<= ]]> #{request.examEndTime}  " +
            "</if>  " +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "</script>")
    IPage<CollegeTrainExamRecordVo> queryPaperList(@Param("page") Page<CollegeTrainExamRecordVo> page, @Param("request") CollegeTrainExamRecordRequest request);

    @Select("<script>" +
            "SELECT \n" +
            "cter.Id,cter.EXAM_NUMBER ,cter.EXAM_NAME ,\n" +
            "TO_NVARCHAR(cter.CREATED_TIME ,'YYYY-MM-DD HH24:MI:SS')as examStartTime,TO_NVARCHAR(cter.UPDATED_TIME,'YYYY-MM-DD HH24:MI:SS') as examEndTime,\n" +
            "cter.JUDGE_NO ,cter.JUDGE_NAME ,\n" +
            "cter.JUDGE_NUMBER ,cter.ALLOCATION_STATUS,\n" +
            "cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,\n" +
            "ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ,ctpa.AREA ,\n" +
            "ctec.PAPER_TYPE   \n" +
            "FROM COLLEGE_TRAIN_EXAM_RECORD cter \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cter.ASSESSED_ID  = cta.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID  = ctpa.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON cter.EXAM_CATEGORY_ID = ctec.ID \n" +
            "WHERE cter.DELETED_FLAG  = FALSE AND cta.DELETED_FLAG =FALSE and ctec.ID in ('4','5')" +
            "<if test=\"request.allocationStatus !=null and request.allocationStatus !=''\">  " +
            "    and cter.ALLOCATION_STATUS = #{request.allocationStatus}  " +
            "</if>  " +
            "<if test=\"request.examName !=null and request.examName !=''\">  " +
            "    and cter.EXAM_NAME = #{request.examName}  " +
            "</if>  " +
            "<if test=\"request.examNumber !=null and request.examNumber !=''\">  " +
            "    and cter.EXAM_NUMBER = #{request.examNumber}  " +
            "</if>  " +
            "<if test=\"request.judgeNo !=null and request.judgeNo !=''\">  " +
            "    and cter.JUDGE_NO = #{request.judgeNo}  " +
            "</if>  " +
            "<if test=\"request.judgeName !=null and request.judgeName !=''\">  " +
            "    and cter.JUDGE_NAME = #{request.judgeName}  " +
            "</if>  " +
            "<if test=\"request.judgeNumber !=null and request.judgeNumber !=''\">  " +
            "    and cter.JUDGE_NUMBER = #{request.judgeNumber}  " +
            "</if>  " +
            "<if test=\"request.examStartTime !=null and request.examStartTime !='' \">  " +
            "    and cter.EXAM_START_TIME <![CDATA[>= ]]> #{request.examStartTime}  " +
            "</if>  " +
            "<if test=\"request.examEndTime !=null and request.examEndTime !=''\">  " +
            "    and cter.EXAM_END_TIME <![CDATA[<= ]]> #{request.examEndTime}  " +
            "</if>  " +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "</script>")
    List<CollegeTrainExamRecordExportExcel> getList(@Param("request") CollegeTrainExamRecordRequest request);

    @Select("<script>" +
            "SELECT cter.Id, cter.TOTAL_SCORE, cter.EXAM_NUMBER ,cter.EXAM_NAME ,TO_NVARCHAR(cter.CREATED_TIME ,'YYYY-MM-DD HH24:MI:SS')as examStartTime," +
            "TO_NVARCHAR(cter.UPDATED_TIME,'YYYY-MM-DD HH24:MI:SS') as examEndTime ,cter.JUDGE_NO ,cter.JUDGE_NAME ," +
            "cter.EXAM_TYPE,cter.PAPER_NAME ,ctec.PAPER_TYPE ," +
            "cter.JUDGE_NUMBER ,cter.ALLOCATION_STATUS ,cta.BATCH ,cta.EMPLOYEE_NO ," +
            "cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT  as practiceDepartmentName ,ctpa.AREA ,cter.EVALUATE_REASON FROM \n" +
            "COLLEGE_TRAIN_EXAM_RECORD cter \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cter.ASSESSED_ID  = cta.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID  = ctpa.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON cter.EXAM_CATEGORY_ID = ctec.ID "+
            "WHERE cter.DELETED_FLAG  = FALSE AND cta.DELETED_FLAG =FALSE " +
            "and cter.TOTAL_SCORE IS NULL  and ctec.ID in ('4','5')"+
            "<if test=\"request.allocationStatus !=null and request.allocationStatus !=''\">  " +
            "    and cter.ALLOCATION_STATUS = #{request.allocationStatus}  " +
            "</if>  " +
            "<if test=\"request.examName !=null and request.examName !=''\">  " +
            "    and cter.EXAM_NAME = #{request.examName}  " +
            "</if>  " +
            "<if test=\"request.examNumber !=null and request.examNumber !=''\">  " +
            "    and cter.EXAM_NUMBER = #{request.examNumber}  " +
            "</if>  " +
            "<if test=\"request.judgeNo !=null and request.judgeNo !=''\">  " +
            "    and cter.JUDGE_NO = #{request.judgeNo}  " +
            "</if>  " +
            "<if test=\"request.judgeNumber !=null and request.judgeNumber !=''\">  " +
            "    and cter.JUDGE_NUMBER = #{request.judgeNumber}  " +
            "</if>  " +
            "<if test=\"request.judgeName !=null and request.judgeName !=''\">  " +
            "    and cter.JUDGE_NAME = #{request.judgeName}  " +
            "</if>  " +
            "<if test=\"request.examStartTime !=null and request.examStartTime !='' \">  " +
            "    and cter.EXAM_START_TIME <![CDATA[>= ]]> #{request.examStartTime}  " +
            "</if>  " +
            "<if test=\"request.examEndTime !=null and request.examEndTime !=''\">  " +
            "    and cter.EXAM_END_TIME <![CDATA[<= ]]> #{request.examEndTime}  " +
            "</if>  " +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +

            "</script>")

    IPage<CollegeTrainExamRecordVo> getForApprovalPageList(@Param("page") Page<CollegeTrainExamRecordVo> page, @Param("request") CollegeTrainExamRecordRequest request);

    @Select("<script>" +
            "SELECT cter.Id, cter.TOTAL_SCORE, cter.EXAM_NUMBER ,cter.EXAM_NAME ,TO_NVARCHAR(cter.CREATED_TIME ,'YYYY-MM-DD HH24:MI:SS')as examStartTime," +
            "TO_NVARCHAR(cter.UPDATED_TIME,'YYYY-MM-DD HH24:MI:SS') as examEndTime,cter.JUDGE_NO ,cter.JUDGE_NAME ,cter.OBJECTIVE_SCORE ,cter.SUBJECTIVE_SCORE ," +
            "cter.EXAM_TYPE,cter.PAPER_NAME ,ctec.PAPER_TYPE ," +
            "cter.JUDGE_NUMBER ,cter.ALLOCATION_STATUS ,cta.BATCH ,cta.EMPLOYEE_NO ," +
            "cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ,ctpa.AREA ,cter.EVALUATE_REASON FROM \n" +
            "COLLEGE_TRAIN_EXAM_RECORD cter \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cter.ASSESSED_ID  = cta.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID  = ctpa.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON cter.EXAM_CATEGORY_ID = ctec.ID "+
            "WHERE cter.DELETED_FLAG  = FALSE AND cta.DELETED_FLAG =FALSE " +
            "and cter.TOTAL_SCORE IS NOT NULL and ctec.ID in ('4','5') "+
            "<if test=\"request.allocationStatus !=null and request.allocationStatus !=''\">  " +
            "    and cter.ALLOCATION_STATUS = #{request.allocationStatus}  " +
            "</if>  " +
            "<if test=\"request.examName !=null and request.examName !=''\">  " +
            "    and cter.EXAM_NAME = #{request.examName}  " +
            "</if>  " +
            "<if test=\"request.examNumber !=null and request.examNumber !=''\">  " +
            "    and cter.EXAM_NUMBER = #{request.examNumber}  " +
            "</if>  " +
            "<if test=\"request.judgeNo !=null and request.judgeNo !=''\">  " +
            "    and cter.JUDGE_NO = #{request.judgeNo}  " +
            "</if>  " +
            "<if test=\"request.judgeName !=null and request.judgeName !=''\">  " +
            "    and cter.JUDGE_NAME = #{request.judgeName}  " +
            "</if>  " +
            "<if test=\"request.judgeNumber !=null and request.judgeNumber !=''\">  " +
            "    and cter.JUDGE_NUMBER = #{request.judgeNumber}  " +
            "</if>  " +
            "<if test=\"request.examStartTime !=null and request.examStartTime !='' \">  " +
            "    and cter.EXAM_START_TIME <![CDATA[>= ]]> #{request.examStartTime}  " +
            "</if>  " +
            "<if test=\"request.examEndTime !=null and request.examEndTime !=''\">  " +
            "    and cter.EXAM_END_TIME <![CDATA[<= ]]> #{request.examEndTime}  " +
            "</if>  " +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "</script>")

    IPage<CollegeTrainExamRecordVo> getApprovedPageList(@Param("page") Page<CollegeTrainExamRecordVo> page, @Param("request") CollegeTrainExamRecordRequest request);

    @Select("<script>" +
            "SELECT cter.Id, cter.TOTAL_SCORE,  cter.OBJECTIVE_SCORE, cter.EXAM_NUMBER ,cter.EXAM_NAME ,TO_NVARCHAR(cter.CREATED_TIME ,'YYYY-MM-DD HH24:MI:SS')as examStartTime," +
            "TO_NVARCHAR(cter.UPDATED_TIME,'YYYY-MM-DD HH24:MI:SS') as examEndTime,cter.JUDGE_NO ,cter.JUDGE_NAME ," +
            "cter.EXAM_TYPE,cter.PAPER_NAME ,ctec.PAPER_TYPE ," +
            "cter.JUDGE_NUMBER ,cter.ALLOCATION_STATUS ,cta.BATCH ,cta.EMPLOYEE_NO ," +
            "cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ,ctpa.AREA,ctss.EXAM_CLASS as examClass, cter.SUBJECTIVE_SCORE  as subjectiveScore  FROM \n" +
            "COLLEGE_TRAIN_EXAM_RECORD cter \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cter.ASSESSED_ID  = cta.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_SUBJECTIVE_SCORE ctss ON ctss.EMPLOYEE_NUMBER  = cta.EMPLOYEE_NO AND ctss.EXAM_CLASS=cter.EXAM_NAME\n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID  = ctpa.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON cter.EXAM_CATEGORY_ID = ctec.ID "+
            "WHERE cter.DELETED_FLAG  = FALSE AND cta.DELETED_FLAG =FALSE  AND ctec.CATEGORY_ITEM in ('工艺公开课理论考试','工程结业理论考试') " +
            "<if test=\"request.allocationStatus !=null and request.allocationStatus !=''\">  " +
            "    and cter.ALLOCATION_STATUS = #{request.allocationStatus}  " +
            "</if>  " +
            "<if test=\"request.examName !=null and request.examName !=''\">  " +
            "    and cter.EXAM_NAME = #{request.examName}  " +
            "</if>  " +
            "<if test=\"request.examNumber !=null and request.examNumber !=''\">  " +
            "    and cter.EXAM_NUMBER = #{request.examNumber}  " +
            "</if>  " +
            "<if test=\"request.judgeNo !=null and request.judgeNo !=''\">  " +
            "    and cter.JUDGE_NO = #{request.judgeNo}  " +
            "</if>  " +
            "<if test=\"request.judgeName !=null and request.judgeName !=''\">  " +
            "    and cter.JUDGE_NAME = #{request.judgeName}  " +
            "</if>  " +
            "<if test=\"request.judgeNumber !=null and request.judgeNumber !=''\">  " +
            "    and cter.JUDGE_NUMBER = #{request.judgeNumber}  " +
            "</if>  " +
            "<if test=\"request.examStartTime !=null and request.examStartTime !='' \">  " +
            "    and cter.EXAM_START_TIME <![CDATA[>= ]]> #{request.examStartTime}  " +
            "</if>  " +
            "<if test=\"request.examEndTime !=null and request.examEndTime !=''\">  " +
            "    and cter.EXAM_END_TIME <![CDATA[<= ]]> #{request.examEndTime}  " +
            "</if>  " +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "<if test=\"request.examType.size > 0 \">  " +
            "    and ctec.CATEGORY_ITEM in " +
            "<foreach collection=\"request.examType\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.examType.size = 0 \">  " +
            "    and ctec.CATEGORY_ITEM in ('工艺公开课理论考试','工程结业理论考试')" +
            "</if>  " +
            "</script>")
    IPage<CollegeTrainExamRecordVo> getEngineeringPageList(@Param("page") Page<CollegeTrainExamRecordVo> page, @Param("request") CollegeTrainExamRecordRequest request);


    @Select("<script>" +
            "SELECT cter.Id, cter.TOTAL_SCORE, cter.SUBJECTIVE_SCORE, cter.OBJECTIVE_SCORE, cter.EXAM_NUMBER ,cter.EXAM_NAME ,TO_NVARCHAR(cter.CREATED_TIME ,'YYYY-MM-DD HH24:MI:SS')as examStartTime," +
            "TO_NVARCHAR(cter.UPDATED_TIME,'YYYY-MM-DD HH24:MI:SS') as examEndTime,cter.JUDGE_NO ,cter.JUDGE_NAME ," +
            "cter.EXAM_TYPE,cter.PAPER_NAME ,ctec.PAPER_TYPE ," +
            "cter.JUDGE_NUMBER ,cter.ALLOCATION_STATUS ,cta.BATCH ,cta.EMPLOYEE_NO ," +
            "cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ,ctpa.AREA,ctec.CATEGORY_ITEM as examClass FROM \n" +
            "COLLEGE_TRAIN_EXAM_RECORD cter \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cter.ASSESSED_ID  = cta.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID  = ctpa.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON cter.EXAM_CATEGORY_ID = ctec.ID "+
            "WHERE cter.DELETED_FLAG  = FALSE AND cta.DELETED_FLAG =FALSE  AND cter.EXAM_CATEGORY_ID='9' " +
            "    and ctec.CATEGORY_ITEM in ('工艺公开课理论考试','工程结业理论考试')" +
            "</script>")
    List<CollegeTrainExamRecordVo> queryEngineeringPageList();


    @Select("select distinct ${column} from COLLEGE_TRAIN_EXAM_RECORD where DELETED_FLAG  = FALSE")
    List<String> getSpecifiedColumn(String column);
}
