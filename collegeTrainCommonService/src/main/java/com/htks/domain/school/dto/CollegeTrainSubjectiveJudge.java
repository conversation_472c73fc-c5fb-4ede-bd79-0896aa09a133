package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 培训组长主观分评价
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_SUBJECTIVE_JUDGE")
@ApiModel(value = "CollegeTrainSubjectiveJudge对象", description = "大学生培训系统 培训组长主观分评价")
public class CollegeTrainSubjectiveJudge implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ASSESSED_ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @ApiModelProperty("得分")
    @TableField("SCORE")
    private Integer score;


}
