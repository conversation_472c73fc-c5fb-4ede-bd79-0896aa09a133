package com.htks.domain.school.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/16 9:43
 */
@Data
public class CollegeTrainWeeklyReportVo {

    private Long id;

    @ApiModelProperty("厂别")
    private String factoryType;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("班别")
    private String classNo;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("进入BU时间")
    private String buTime;

    @ApiModelProperty("实习周别")
    private String workTime;


    @ApiModelProperty("得分")
    private Integer score;

    @ApiModelProperty("入职年份")
    private String enterDate;

    @ApiModelProperty("周别")
    private Integer weekly;

    @ApiModelProperty("评价周期")
    private String appraise;

    @ApiModelProperty("阅卷师工号")
    private String judgeNo;

    @ApiModelProperty("阅卷师名称")
    private String judgeName;

}
