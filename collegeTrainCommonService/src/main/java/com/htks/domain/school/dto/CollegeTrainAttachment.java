package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 统一附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_ATTACHMENT")
@ApiModel(value = "CollegeTrainAttachment对象", description = "大学生培训系统 统一附件表")
public class CollegeTrainAttachment implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("额外描述")
    @TableField("ATTACHMENT_MEMO")
    private String attachmentMemo;

    @ApiModelProperty("附件原文件名")
    @TableField("ATTACHMENT_NAME")
    private String attachmentName;

    @ApiModelProperty("附件相对路径")
    @TableField("ATTACHMENT_PATH")
    private String attachmentPath;

    @TableField("ATTACHMENT_TYPE")
    private String attachmentType;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @ApiModelProperty("上岗证主键ID")
    @TableField("POST_CARD_ID")
    private Long postCardId;

    @ApiModelProperty("试题ID")
    @TableField("QUESTION_ID")
    private Long questionId;


}
