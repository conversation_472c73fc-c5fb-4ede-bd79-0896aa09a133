package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.htks.domain.school.dto.CollegeTrainExamRecordAnswer;
import com.htks.domain.school.vo.CollegeTrainExamRecordAnswerVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 岗位认证系统 考试记录答案表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainExamRecordAnswerMapper extends BaseMapper<CollegeTrainExamRecordAnswer> {

    @Select("<script>" +
            "SELECT " +
            "ctera.ID, ctec.SINGLE_OPTION_ANSWER ,ctec.MORE_OPTION_ANSWER ,ctec.JUDGE_ANSWER ,ctec.FILL_ANSWER ,ctec.SHORT_ANSWER , "+
            "ctq.QUESTION_TYPE ,ctq.QUESTION_CONTENT ,ctq.STANDARD_ANSWER ,ctera.ANSWER_TEXT ,ctera.SCORE ,ctera.NOTE,a.ATTACHMENT_PATH  " +
            "FROM COLLEGE_TRAIN_EXAM_RECORD_ANSWER ctera \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_RECORD cter ON ctera.EXAM_ID = cter.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_QUESTION ctq ON ctq.ID = ctera.QUESTION_ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_EXAM_CATEGORY ctec ON cter.EXAM_CATEGORY_ID = ctec.ID "+
            "LEFT JOIN  COLLEGE_TRAIN_ATTACHMENT a ON a.ID =ctera.ATTACHMENT_ID "+
            "WHERE cter.DELETED_FLAG = FALSE AND ctq.DELETED_FLAG = FALSE and cter.ID = #{examId}\n " +
            "<if test=\"questionType !=null and questionType !=''\">  " +
            "    and ctq.QUESTION_TYPE = #{questionType}  " +
            "</if>  " +
            "</script>")
    List<CollegeTrainExamRecordAnswerVo> getExamRecordAnswer(@Param("examId") Long examId,@Param("questionType") String questionType);
}
