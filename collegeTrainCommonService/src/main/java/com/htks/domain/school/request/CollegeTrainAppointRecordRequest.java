package com.htks.domain.school.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.common.convert.Search;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 大学生 预约记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainAppointRecordRequest extends Search {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("实习部门名称")
    private List<String> practiceDepartmentNameList;

    @ApiModelProperty("如果选了区域,直接给对应配置主键")
    private String practiceDepartmentAreaId;

    @ApiModelProperty("预约开始时间")
    private String appointStartTime;

    @ApiModelProperty("预约结束时间")
    private String appointEndTime;


    @ApiModelProperty("预约开始时间")
    private String appointStartDate;

    @ApiModelProperty("预约结束时间")
    private String appointEndDate;

    @ApiModelProperty("是否通过")
    private Boolean passed;

    @ApiModelProperty("考试状态 已考核：未考核")
    private String status;

    /**
     * 预约项目
     */
    @ApiModelProperty("评价类型 1：工程类 2：异常答辩")
    private Integer type;

    @ApiModelProperty("预约状态")
    private List<String> appointStatus = new ArrayList<>();


    private  String factoryType;

}
