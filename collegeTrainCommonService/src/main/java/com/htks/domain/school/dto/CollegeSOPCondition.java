package com.htks.domain.school.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CollegeSOPCondition {
    @ApiModelProperty("厂别")
    private List<String> factoryType = new ArrayList<>();
    @ApiModelProperty("批次")
    private String batch;
    @ApiModelProperty("学员工号")
    private String employeeNo;
    @ApiModelProperty("学员姓名")
    private String employeeName;
    @ApiModelProperty("实习部门")
    private List<String> department = new ArrayList<>();;
    @ApiModelProperty("区域/制程")
    private String area;
    @ApiModelProperty("SOP名称")
    private String sopName;
    @ApiModelProperty("第几页")
    private int pageSize;
    @ApiModelProperty("页大小")
    private int pageNumber;
    @ApiModelProperty(hidden = true)
    private int limit;
    @ApiModelProperty(hidden = true)
    private int offset;
}
