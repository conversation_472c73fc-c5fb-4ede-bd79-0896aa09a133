package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.htks.domain.school.dto.CollegeTrainDepartment;
import com.htks.domain.school.dto.CollegeTrainPostArea;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 部门区域对应表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08R
 */
@Mapper
public interface CollegeTrainPostAreaMapper extends BaseMapper<CollegeTrainPostArea> {

    List<String> getAllDepartment(String factoryFlag);

    String getFactoryFlag(String employeeNo);

    List<CollegeTrainDepartment>getCollegeTrainDepartment();
}
