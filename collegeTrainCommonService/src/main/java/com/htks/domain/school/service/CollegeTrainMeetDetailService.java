package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.domain.school.dto.CollegeTrainMeet;
import com.htks.domain.school.dto.CollegeTrainMeetDetail;

import java.util.List;

/**
 * <p>
 * 大学生培训系统 见面会详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainMeetDetailService extends IService<CollegeTrainMeetDetail> {

    void saveOrUpdateMeetDetail(List<CollegeTrainMeetDetail> collect, Long meetId, String type);
}
