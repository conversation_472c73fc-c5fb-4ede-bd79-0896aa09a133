package com.htks.domain.school.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeSubjectiveExcelDTO {

    /**
     * 序号
     */
    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号")
    private String number;
    /**
     * 学员工号
     */
    @ApiModelProperty("学员工号")
    @ExcelProperty(value = "学员工号")
    private String employeeNo;
    /**
     * 学员姓名
     */
    @ApiModelProperty("学员姓名")
    @ExcelProperty(value = "学员姓名")
    private String employeeName;
    /**
     * 考试科目
     */
    @ApiModelProperty("考试科目")
    @ExcelProperty(value = "考试科目")
    private String examClass;
    /**
     * 简答题得分
     */
    @ApiModelProperty("简答题得分")
    @ExcelProperty(value = "简答题得分")
    private String score;
}
