package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 员工表(每日同步)
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Getter
@Setter
@TableName("COMMON_EMPLOYEE")
@ApiModel(value = "CommonEmployee对象", description = "员工表(每日同步)")
public class CommonEmployee implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("班别")
    @TableField("AB")
    private String ab;

    @ApiModelProperty("年假(小时)")
    @TableField("ANNUAL_LEAVE")
    private String annualLeave;

    @ApiModelProperty("出生日期")
    @TableField("BIRTHDAY")
    private String birthday;

    @ApiModelProperty("事业部ID")
    @TableField("BUSINESS_UNIT_ID")
    private String businessUnitId;

    @ApiModelProperty("事业部名称")
    @TableField("BUSINESS_UNIT_NAME")
    private String businessUnitName;

    @ApiModelProperty("区组ID")
    @TableField("CLASS_BAN_ID")
    private String classBanId;

    @ApiModelProperty("区组名称")
    @TableField("CLASS_BAN_NAME")
    private String classBanName;

    @ApiModelProperty("学校")
    @TableField("COLLEGE_NAME")
    private String collegeName;

    @ApiModelProperty("成本中心")
    @TableField("COST_CENTER_CODE")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @TableField("COST_CENTER_NAME")
    private String costCenterName;

    @ApiModelProperty("部门ID")
    @TableField("DEPARTMENT_ID")
    private String departmentId;

    @ApiModelProperty("部门名称")
    @TableField("DEPARTMENT_NAME")
    private String departmentName;

    @ApiModelProperty("学历")
    @TableField("EDUCATION")
    private String education;

    @ApiModelProperty("邮箱")
    @TableField("EMAIL")
    private String email;

    @ApiModelProperty("员工类别")
    @TableField("EMPLOYEE_CATEGORY")
    private String employeeCategory;

    @ApiModelProperty("姓名")
    @TableField("EMPLOYEE_NAME")
    private String employeeName;

    @ApiModelProperty("工号")
    private String employeeNumber;

    @ApiModelProperty("员工状态(在职or离职or留职)")
    @TableField("EMPLOYEE_STATUS")
    private String employeeStatus;

    @ApiModelProperty("入职日期")
    @TableField("ENTRY_DATE")
    private String entryDate;

    @ApiModelProperty("一级主管姓名")
    @TableField("FIRST_SUPERVISOR_NAME")
    private String firstSupervisorName;

    @ApiModelProperty("一级主管工号")
    @TableField("FIRST_SUPERVISOR_NUMBER")
    private String firstSupervisorNumber;

    @ApiModelProperty("性别")
    @TableField("GENDER")
    private String gender;

    @ApiModelProperty("调休假(小时)")
    @TableField("HOLIDAY")
    private String holiday;

    @ApiModelProperty("HRMS登录密码")
    @TableField("HR_PASSWORD")
    private String hrPassword;

    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("身份证号码")
    @TableField("IDENTITY_NUMBER")
    private String identityNumber;

    @ApiModelProperty("介绍人")
    @TableField("INTRODUCER_NAME")
    private String introducerName;

    @ApiModelProperty("加入集团日期")
    @TableField("JOINING_GROUP_COMPANY_DATE")
    private String joiningGroupCompanyDate;

    @ApiModelProperty("离职日期")
    @TableField("LEAVE_JOB_DATE")
    private String leaveJobDate;

    @ApiModelProperty("离职类型")
    @TableField("LEAVE_JOB_TYPE")
    private String leaveJobType;

    @ApiModelProperty("专业")
    @TableField("MAJOR")
    private String major;

    @ApiModelProperty("是否免卡")
    @TableField("NOT_CLOCK")
    private String notClock;

    @ApiModelProperty("手机")
    @TableField("PHONE")
    private String phone;

    @ApiModelProperty("职等")
    @TableField("POSITION_GRADE")
    private String positionGrade;

    @ApiModelProperty("职务ID")
    @TableField("POSITION_ID")
    private String positionId;

    @ApiModelProperty("职务")
    @TableField("POSITION_NAME")
    private String positionName;

    @ApiModelProperty("职级")
    @TableField("POSITION_RANK")
    private String positionRank;

    @ApiModelProperty("试用期")
    @TableField("PROBATION_DATE")
    private String probationDate;

    @ApiModelProperty("劳务合同转正日期")
    @TableField("PROMOTION_DATE")
    private String promotionDate;

    @ApiModelProperty("特调假(小时)")
    @TableField("SPECIAL_HOLIDAY")
    private String specialHoliday;

    @ApiModelProperty("员工来源")
    @TableField("STAFF_SOURCE")
    private String staffSource;

    @ApiModelProperty("科ID")
    @TableField("STATION_ID")
    private String stationId;

    @ApiModelProperty("科名称")
    @TableField("STATION_NAME")
    private String stationName;

    @ApiModelProperty("同步数据源")
    @TableField("SYNC_SOURCE")
    private String syncSource;

    @ApiModelProperty("同步时间")
    @TableField("SYNC_TIME")
    private Date syncTime;

    @ApiModelProperty("微信ID")
    @TableField("WECHAR_ID")
    private String wecharId;

    @ApiModelProperty("职能ID")
    @TableField("ZN_ID")
    private String znId;

    @ApiModelProperty("职能名称")
    @TableField("ZN_NAME")
    private String znName;


}
