package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeSOPCondition;
import com.htks.domain.school.dto.CollegeTrainSopUpload;
import com.htks.domain.school.request.CollegeTrainSopUploadRequest;
import com.htks.domain.school.request.SopEvaluateRequest;
import com.htks.domain.school.vo.CollegeTrainSopUploadVo;
import com.htks.web.JsonPagedVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 大学生培训系统 SOP上传/读后感上传 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainSopUploadService extends IService<CollegeTrainSopUpload> {

    JsonPagedVO<CollegeTrainSopUploadVo> queryPaperList(CollegeTrainSopUploadRequest request);

    void downloadSopTemplate(HttpServletResponse response) throws IOException;

    void export(CollegeTrainSopUploadRequest request, HttpServletResponse response);

    ResultVO importTemplate(MultipartFile file) throws IOException;

    void assignExaminer(CollegeTrainSopUploadRequest request);

    void sopEvaluate(SopEvaluateRequest request);

    void downloadAfterReadingTemplate(HttpServletResponse response) throws IOException;

    ResultVO importAfterReadingTemplate(MultipartFile file,String employeeNo);

    ResultVO insertSopUpload(CollegeTrainSopUpload collegeTrainSopUpload);

    ResultVO getSpecifiedColumn(String column);

    ResultVO getSopGrader();

    JsonPagedVO queryEvaluateDetail(CollegeSOPCondition condition);

    void downloadEvaluateDetail(HttpServletResponse response) throws IOException;

    void downloadEvaluateDetailXlsx(HttpServletResponse response) throws IOException;

    void exportSopEvaluateDetail(HttpServletResponse response) throws IOException;

    String processExcelFile(MultipartFile file) throws IOException;

    List<String> querySopNames();
}
