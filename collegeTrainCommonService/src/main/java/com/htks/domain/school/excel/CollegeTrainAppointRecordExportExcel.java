package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 大学生 考试记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeTrainAppointRecordExportExcel {


    @ApiModelProperty("批次")
    @ExcelProperty(value = "批次",index = 0)
    private String batch;

    @ApiModelProperty("学员姓名")
    @ExcelProperty(value = "学员姓名",index = 1)
    private String employeeName;

    @ApiModelProperty("学员工号")
    @ExcelProperty(value = "学员工号",index = 2)
    private String employeeNo;

    @ApiModelProperty("实习部门")
    @ExcelProperty(value = "实习部门",index = 3)
    private String practiceDepartmentName;

    @ApiModelProperty("区域/制程")
    @ExcelProperty(value = "区域/制程",index = 4)
    private String area;

    @ApiModelProperty("预约项目")
    @ExcelProperty(value = "预约项目",index = 5)
    private String typeStr;

    @ApiModelProperty("预约开始时间")
    @ExcelProperty(value = "预约开始时间",index =6)
    private String appointStartTime;

    @ApiModelProperty("预约结束时间")
    @ExcelProperty(value = "预约结束时间",index = 7)
    private String appointEndTime;

    @ApiModelProperty("预约状态")
    @ExcelProperty(value = "预约状态",index = 8)
    private String appointStatus;

    @ApiModelProperty("考核状态")
    @ExcelProperty(value = "考核状态",index = 9)
    private String status;

    @ApiModelProperty("得分(分)")
    @ExcelProperty(value = "得分(分)",index = 10)
    private BigDecimal score;

    @ApiModelProperty("考试通过时间")
    @ExcelProperty(value = "考试通过时间",index = 11)
    private String passedDate;

    @ApiModelProperty("附加分(分)")
    @ExcelProperty(value = "附加分(分)",index = 12)
    private BigDecimal additionalPoints;

    @ApiModelProperty("是否通过")
    @ExcelProperty(value = "是否通过",index = 13)
    private String passedStr;

    @ApiModelProperty("是否通过")
    @ExcelIgnore
    private Boolean passed;

    @ApiModelProperty("评价类型 1：工程类实操 2：异常答辩")
    @ExcelIgnore
    private Integer type;
}
