package com.htks.domain.school.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.CheckNumberUtils;
import com.htks.common.utils.EasyExcelUtils;
import com.htks.common.utils.JudgeNumberUtils;
import com.htks.common.utils.PageUtils;
import com.htks.common.utils.SheetUtil;
import com.htks.domain.school.dto.*;
import com.htks.domain.school.enums.AllocationStatusEnum;
import com.htks.domain.school.enums.EmployeeStatusEnum;
import com.htks.domain.school.enums.EvaluateStatusEnum;
import com.htks.domain.school.excel.*;
import com.htks.domain.school.repository.hana.CollegeTrainSopUploadMapper;
import com.htks.domain.school.request.CollegeTrainSopUploadRequest;
import com.htks.domain.school.request.SopEvaluateRequest;
import com.htks.domain.school.service.*;
import com.htks.domain.school.vo.*;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.utils.HttpUtils;
import com.htks.web.JsonPagedVO;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import static com.htks.common.utils.EasyExcelUtils.exportExcel;

/**
 * <p>
 * 大学生培训系统 SOP上传/ 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainSopUploadServiceImpl extends ServiceImpl<CollegeTrainSopUploadMapper, CollegeTrainSopUpload> implements CollegeTrainSopUploadService {
    @Resource
    private CollegeTrainSopUploadMapper collegeTrainSopUploadMapper;
    @Resource
    private CommonEmployeeService commonEmployeeService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @Resource
    private CollegeTrainSopUploadDetailService collegeTrainSopUploadDetailService;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @Override
    public JsonPagedVO<CollegeTrainSopUploadVo> queryPaperList(CollegeTrainSopUploadRequest request) {
        if (request.getPracticeDepartmentNameList()!=null) {
            List<String> list = new ArrayList<>();
            List<String> listDepartment = new ArrayList<>();

            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                list.add(uploadWorkLicense.getFactory(request.getPracticeDepartmentNameList().get(i)));
                listDepartment.add(uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i)));
            }
            request.setFactoryType(list);
            request.setPracticeDepartmentNameList(listDepartment);
        }

        Page<CollegeTrainSopUploadVo> page = PageUtils.getPage(request);



        IPage<CollegeTrainSopUploadVo> collegeTrainSopUploadPage = this.baseMapper.queryPaperList(page,request);
        return JsonPagedVO.success(collegeTrainSopUploadPage.getRecords(),Integer.parseInt(collegeTrainSopUploadPage.getTotal()+""));
    }

    @Override
    public void downloadSopTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainSopUploadExcel> list = new ArrayList<>();

        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();

        SheetUtil sheetUtil = new SheetUtil(fieldValues);
        CustomSheetWriteHandler customSheetWriteHandler = new CustomSheetWriteHandler();
        String fileName = URLEncoder.encode("SOP批量分配阅卷师模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        EasyExcel.write(response.getOutputStream(), CollegeTrainSopUploadExcel.class)
                .registerWriteHandler(new CustomSheetWriteHandler())
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("分配阅卷师").registerWriteHandler(sheetUtil).doWrite(list);
    }

    @Override
    public void export(CollegeTrainSopUploadRequest request, HttpServletResponse response) {
        List<CollegeTrainSopUploadExportExcel> list = this.baseMapper.getList(request);
        EasyExcelUtils.exportExcel(response, list, "SOP信息", CollegeTrainSopUploadExportExcel.class);
    }

    @Override
    public ResultVO importTemplate(MultipartFile file) {
        List<CollegeTrainSopUploadExcel> collegeTrainSopUploadExcelList = null;
        try {
            collegeTrainSopUploadExcelList = EasyExcelUtils.readExcel(file, CollegeTrainSopUploadExcel.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }
        List<CollegeTrainSopUpload> list = new ArrayList<>();
        int a=-1;
        for  (int i = 0; i < collegeTrainSopUploadExcelList.size(); i++) {
            CollegeTrainSopUploadExcel collegeTrainEngineeringExamRecordExcel = collegeTrainSopUploadExcelList.get(i);

            if (collegeTrainEngineeringExamRecordExcel.getNumber()==null){

            }else {
                if (collegeTrainEngineeringExamRecordExcel.getNumber().contains("导入文档说明")) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a< collegeTrainSopUploadExcelList.size()) {
            collegeTrainSopUploadExcelList.subList(a, collegeTrainSopUploadExcelList.size()).clear();
        }

        if (ObjectUtil.isNotEmpty(collegeTrainSopUploadExcelList)) {
            for (int i = 0; i < collegeTrainSopUploadExcelList.size(); i++) {
                CollegeTrainSopUploadExcel collegeTrainSopUploadExcel = collegeTrainSopUploadExcelList.get(i);
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSopUploadExcel.getEmployeeNo(), "第" + (i + 2) + "行学员工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSopUploadExcel.getGraderNo(), "第" + (i + 2) + "行阅卷师工号不能为空!");
                //获取员工信息
                CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainSopUploadExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(employeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //获取阅卷师信息,必须是在职状态
                CommonEmployee judgeEmployeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainSopUploadExcel.getGraderNo(), EmployeeStatusEnum.ON_THE_JOB.getCode());
                if (ObjectUtil.isEmpty(judgeEmployeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行第3列数据不存在");
                }
                //获取大学生信息
                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(collegeTrainSopUploadExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(collegeTrainAssessed)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //根据员工id进行数据匹配

                if (uploadWorkLicense.getSopSum(collegeTrainAssessed.getId(),1)==0) {
                    return ResultVO.error("第" + (i + 2) + "行SOP数据不存在");
                }

                //校验是否被批阅
                if (uploadWorkLicense.getSopScore(collegeTrainAssessed.getId(),1).equals("已评价")){
                    return ResultVO.error("第" + (i + 2) + "行数据已被批阅，不可再次导入");
                }
                //封装参数进行更新
                CollegeTrainSopUpload newCollegeTrainSopUpload = new CollegeTrainSopUpload();
                newCollegeTrainSopUpload.setAssessedId(collegeTrainAssessed.getId());
                newCollegeTrainSopUpload.setGradingNumber(JudgeNumberUtils.getSopJudgeNumber());
                newCollegeTrainSopUpload.setGraderNo(collegeTrainSopUploadExcel.getGraderNo());
                newCollegeTrainSopUpload.setGraderName(judgeEmployeeInfo.getEmployeeName());

                list.add(newCollegeTrainSopUpload);
            }
        }
        Date dateToday = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmSS");
        String currentTime = sdf.format(dateToday);
        String number = "SOP" + currentTime;
        if(list.size()>0) {
            for (int i = 0; i < list.size(); i++) {
                uploadWorkLicense.updateSop(list.get(i).getGraderNo(), list.get(i).getGraderName(), list.get(i).getAssessedId(), number);
            }
            return ResultVO.success("成功导入" + list.size() + "数据");
        }else {
        return ResultVO.success("没有符合数据");
        }
    }

    @Override
    public void assignExaminer(CollegeTrainSopUploadRequest request) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(request);
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getId(),"数据id必传");
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getGraderNo(),"阅卷师工号必传");
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getGraderName(),"阅卷师名称必传");
        String judgeNumber = JudgeNumberUtils.getSopJudgeNumber();

        CollegeTrainSopUpload collegeTrainSopUpload = new CollegeTrainSopUpload();
        collegeTrainSopUpload.setId(request.getId());
        collegeTrainSopUpload.setGraderName(request.getGraderName());
        collegeTrainSopUpload.setGraderNo(request.getGraderNo());
        collegeTrainSopUpload.setGradingNumber(judgeNumber);

        updateById(collegeTrainSopUpload);
    }

    @Override
    public void sopEvaluate(SopEvaluateRequest request) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(request);
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getSopUploadId());
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getScore());
        //更新sop上传表总分和评价状态
        CollegeTrainSopUpload collegeTrainSopUpload = new CollegeTrainSopUpload();
        collegeTrainSopUpload.setId(request.getSopUploadId());
        collegeTrainSopUpload.setScore(request.getScore());
        collegeTrainSopUpload.setEvaluateReason(request.getEvaluateReason());
        collegeTrainSopUpload.setEvaluateStatus(EvaluateStatusEnum.EVALUATED.getCode());
        updateById(collegeTrainSopUpload);

        //插入sop评分详情(判空下,读后感没有详情)
        if (ObjectUtil.isNotEmpty(request.getSopUploadDetail())){
            request.getSopUploadDetail().setSopUploadId(request.getSopUploadId());
            collegeTrainSopUploadDetailService.save(request.getSopUploadDetail());
        }
    }

    @Override
    public void downloadAfterReadingTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainAfterReadingExcel> list = new ArrayList<>();

        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();

        SheetUtil sheetUtil = new SheetUtil(fieldValues);

        String fileName = URLEncoder.encode("读后感评价模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        CustomSheetWriteHandler customSheetWriteHandler = new CustomSheetWriteHandler();

        EasyExcel.write(response.getOutputStream(), CollegeTrainAfterReadingExcel.class)
                .registerWriteHandler(new  CustomSheetWriteHandler())
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("读后感评价").registerWriteHandler(sheetUtil).doWrite(list);
    }

    @Override
    public ResultVO importAfterReadingTemplate(MultipartFile file,String employeeNo) {
        List<CollegeTrainAfterReadingExcel> collegeTrainAfterReadingExcelList = null;
        try {
            collegeTrainAfterReadingExcelList = EasyExcelUtils.readExcel(file, CollegeTrainAfterReadingExcel.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }
        List<CollegeTrainSopUpload> list = new ArrayList<>();
        int a=-1;
        for  (int i = 0; i < collegeTrainAfterReadingExcelList.size(); i++) {
            CollegeTrainAfterReadingExcel collegeTrainEngineeringExamRecordExcel = collegeTrainAfterReadingExcelList.get(i);

            if (collegeTrainEngineeringExamRecordExcel.getNumber()==null){

            }else {
                if (collegeTrainEngineeringExamRecordExcel.getNumber().contains("导入文档说明")) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a< collegeTrainAfterReadingExcelList.size()) {
            collegeTrainAfterReadingExcelList.subList(a, collegeTrainAfterReadingExcelList.size()).clear();
        }
        if (ObjectUtil.isNotEmpty(collegeTrainAfterReadingExcelList)) {
            for (int i = 0; i < collegeTrainAfterReadingExcelList.size(); i++) {
                CollegeTrainAfterReadingExcel collegeTrainAfterReadingExcel = collegeTrainAfterReadingExcelList.get(i);
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAfterReadingExcel.getEmployeeNo(), "第" + (i + 2) + "行学员工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAfterReadingExcel.getScore(), "第" + (i + 2) + "行分数不能为空!");
                //获取员工信息
                if (!isNonNegativeInteger(collegeTrainAfterReadingExcel.getScore())){
                    return ResultVO.error("第" + (i + 2) + "行得分不是整数");
                }
                CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainAfterReadingExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(employeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //获取大学生信息
                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(collegeTrainAfterReadingExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(collegeTrainAssessed)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                int sum=Integer.parseInt(collegeTrainAfterReadingExcel.getScore());
                if (!CheckNumberUtils.isNumber(collegeTrainAfterReadingExcel.getScore()+"")){
                    return ResultVO.error("第" + (i + 2) + "行得分数据必须是小于等于3的整数");
                }if (sum>3){
                    return ResultVO.error("第" + (i + 2) + "行得分数据必须是小于等于3的整数");
                }
                /*//根据员工id进行数据匹配
                CollegeTrainSopUpload collegeTrainSopUpload = getByCondition(collegeTrainAssessed.getId());
                if (ObjectUtil.isEmpty(collegeTrainSopUpload)) {
                    return ResultVO.error("第" + (i + 2) + "行SOP数据不存在");
                }*/

                CommonEmployee employeeInfoTeacher = commonEmployeeService.getEmployeeInfo(employeeNo);
                //封装参数进行更新
                CollegeTrainSopUpload newCollegeTrainSopUpload = new CollegeTrainSopUpload();
                newCollegeTrainSopUpload.setAssessedId(collegeTrainAssessed.getId());
                newCollegeTrainSopUpload.setScore(Integer.parseInt(collegeTrainAfterReadingExcel.getScore()));
                newCollegeTrainSopUpload.setEvaluateStatus(EvaluateStatusEnum.EVALUATED.getCode());
                newCollegeTrainSopUpload.setGraderName(employeeInfoTeacher.getEmployeeName());
                newCollegeTrainSopUpload.setGraderNo(employeeInfoTeacher.getEmployeeNumber());
                list.add(newCollegeTrainSopUpload);
            }
        }
        for (int i = 0; i < list.size(); i++) {
            CollegeTrainSopUpload collegeTrainSopUpload=list.get(i);
            List<CollegeTrainSopUpload> trainSopUploads = new ArrayList<>();
            trainSopUploads.add(collegeTrainSopUpload);
            uploadWorkLicense.updateReadAfter(trainSopUploads);
        }
        return ResultVO.success("成功导入" + list.size() + "数据");
    }

    @Override
    public ResultVO insertSopUpload(CollegeTrainSopUpload collegeTrainSopUpload) {
        CollegeTrainSopUpload dbCollegeTrainSopUpload = getByCondition(collegeTrainSopUpload.getAssessedId());
        if (ObjectUtil.isEmpty(dbCollegeTrainSopUpload)){
            return ResultVO.error("在截止上传时间内仅支持一次上传");
        }
        save(collegeTrainSopUpload);
        return ResultVO.success();
    }

    @Override
    public ResultVO getSpecifiedColumn(String column) {
        List<String> list = this.baseMapper.getSpecifiedColumn(column);
        return ResultVO.success(list);
    }

    @Override
    public ResultVO getSopGrader() {
        List<SopGraderVo> list = this.baseMapper.getSopGrader();
        return ResultVO.success(list);
    }

    private CollegeTrainSopUpload getByCondition(Long assessedId) {
        LambdaQueryWrapper<CollegeTrainSopUpload> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(CollegeTrainSopUpload::getDeletedFlag,Boolean.FALSE)
                .eq(CollegeTrainSopUpload::getAssessedId,assessedId);
        return getOne(lambdaQuery);
    }
    public class CustomSheetWriteHandler implements SheetWriteHandler {
        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            Row row = sheet.getRow(20);  // 获取第十行
            if (row == null) {
                row = sheet.createRow(20);
            }
            Cell cell = row.createCell(0);  // 获取第一列
            cell.setCellValue("导入文档说明:\n" +
                    "(1)红色为必填项:\n" +
                    "(2)学员工号必须为人事系统中人员信息中已有的数据，如不是则提示导入失败\n" +
                    "(3)得分必须为整数");  // 填入值

            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                if (mergedRegion.getFirstRow() == 20 && mergedRegion.getFirstColumn() == 0 && mergedRegion.getLastColumn() == 6) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 合并第十行的第1到第5列
            sheet.addMergedRegion(new CellRangeAddress(
                    20, // 第十行
                    26, // 第十行
                    0, // 第一列
                    6  // 第五列
            ));
        }
    }
    public static boolean isNonNegativeInteger(String str) {
        // 使用正则表达式判断字符串是否为非负整数，可以包含0
        return str.matches("[0-9]+");
    }

    /**
     * SOP 评价明细管理查询
     * @param condition
     */
    public JsonPagedVO queryEvaluateDetail(CollegeSOPCondition condition){
        if (condition.getDepartment()!=null) {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < condition.getDepartment().size(); i++) {
                list.add(uploadWorkLicense.getDepartmentName(condition.getDepartment().get(i)));
            }

            List<String> factory = new ArrayList<>();
            if (condition.getFactoryType() == null || condition.getFactoryType().size()==0) {
                for (int i = 0; i < condition.getDepartment().size(); i++) {
                    factory.add(uploadWorkLicense.getFactory(condition.getDepartment().get(i)));
                }
                if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
                    condition.setFactoryType(factory);
                } else if (factory.contains("华天昆山")) {
                    condition.setFactoryType(factory);
                } else if (factory.contains("华天江苏")) {
                    condition.setFactoryType(factory);
                }
            }
            condition.setDepartment(list);
        }
        List<CollegeSopDetailVO> collegeSopDetailVOS = collegeTrainSopUploadMapper.querySopDetail(condition);
        for (CollegeSopDetailVO vo:collegeSopDetailVOS) {
            String qaScore = vo.getQaScore();
            String enScore = vo.getEnScore();
            Double qaDouble = new Double(qaScore);
            Double enDouble = new Double(enScore);
            Double result = (qaDouble+enDouble) / 2.00;
            DecimalFormat format = new DecimalFormat("0.00");
            String format1 = format.format(result);
            vo.setResultScore(format1);
        }
        return new JsonPagedVO<>(collegeSopDetailVOS, collegeTrainSopUploadMapper.querySopDetailCount(condition).size());
    }


    public void exportSopEvaluateDetail(HttpServletResponse response) throws IOException {
        CollegeSOPCondition condition = new CollegeSOPCondition();
        condition.setPageNumber(1);
        condition.setPageSize(1000000);
        JsonPagedVO queryEvaluateDetail = queryEvaluateDetail(condition);
        List<CollegeSopDetailVO> collegeSopDetailVOS = (List<CollegeSopDetailVO>)queryEvaluateDetail.getData();
        List<CollegeSopDetailExcel> excelList = new ArrayList<>();
        int index = 0;
        for (CollegeSopDetailVO vo:collegeSopDetailVOS) {
            Double a=Double.valueOf(vo.getEnScore())/2;
            Double b=Double.valueOf(vo.getQaScore())/2;
            vo.setResultScore(String.valueOf(a+b));
            CollegeSopDetailExcel excel = new CollegeSopDetailExcel();
            BeanUtils.copyProperties(vo,excel);
            excel.setId(++index);
            excelList.add(excel);
        }
        exportExcel(response, excelList, "SOP明细", CollegeSopDetailExcel.class);
    }


    /**
     * 解析上传的SOP评价excel
     * @param file
     * @return
     * @throws IOException
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public String processExcelFile(MultipartFile file) throws IOException {
//        String path =  ResourceUtils.getFile("classpath:mapper").getParent();
//        File tempFile = new File(path + File.pathSeparator + "temp" + File.pathSeparator + file.getName());
//        file.transferTo(tempFile);
//        FileInputStream fileInputStream = new FileInputStream(tempFile);

        InputStream inputStream = HttpUtils.postDecryptFile("http://***********:9999/oa.action.web/getOaData/decryptFile", file);
        if (inputStream == null ) {
            return "文件解析失败!";
        }

        List<CollegeSOPExcelDTO> collegeTrainAssessedExcelList = null;
        try {
            collegeTrainAssessedExcelList = EasyExcelUtils.readExcel3(inputStream, CollegeSOPExcelDTO.class);
        } catch (IOException e) {
            e.printStackTrace();
            return "文件解析错误";
        }
        for(CollegeSOPExcelDTO dto : collegeTrainAssessedExcelList){
            String employeeNo = dto.getNumber();
            String sopName = dto.getSopName();
            String qaScore = dto.getQaScore();
            String eqScore = dto.getEgScore();
            if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(employeeNo) || StringUtils.isEmpty(sopName) || StringUtils.isEmpty(qaScore) || StringUtils.isEmpty(eqScore)){
                return "红色字体为必填字段";
            }
            if(collegeTrainSopUploadMapper.queryStudent(employeeNo) < 1){
                return "工号："+ employeeNo +"未找到相应人员";
            }
        }

            for (CollegeSOPExcelDTO excel:collegeTrainAssessedExcelList) {
                collegeTrainSopUploadMapper.addSopScoreOne(excel);
            }
//            collegeTrainSopUploadMapper.addSopScore(sopExcelList);
            return "文件上传成功";

    }



    public void downloadEvaluateDetail(HttpServletResponse response) throws IOException {

        String fileName = "SOP评价明细模板.xls";
//        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 在Excel工作簿中建一工作表，其名为缺省值

        HSSFSheet sheet = workbook.createSheet();
        sheet.setColumnWidth(0, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(1, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(2, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(3, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(4, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));

        HSSFCellStyle cellStyle2 = workbook.createCellStyle();

        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        HSSFFont font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle2.setFont(font);

        HSSFRow row = sheet.createRow((short)0);
        row.setHeight((short) 800);
        //在索引0的位置创建单元格（左上端）
        HSSFCell cell = row.createCell((short)0);
        // 创建样式 表头样式
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        cellStyle.setWrapText(true);
        HSSFDataFormat format = workbook.createDataFormat();
        cellStyle .setDataFormat(format.getFormat("@"));
        sheet.setDefaultColumnStyle(0, cellStyle );
        sheet.setDefaultColumnStyle(1, cellStyle );
        // 在单元格中输入一些内容
        cell.setCellValue("导入说明：\r\n" +
                "           1.\t红色字体为必填字段；\r\n" +
                "           2.\t学员工号必须为系统中已有在职数据，否则导入失败；");
        cell.setCellStyle(cellStyle);

        row = sheet.createRow(1);
        cell = row.createCell(0);
        cell.setCellValue("序号");
        cell.setCellStyle(cellStyle);
        cell =  row.createCell(1);
        cell.setCellValue("学员工号");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(2);
        cell.setCellValue("SOP名称");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(3);
        cell.setCellValue("质量评委评分");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(4);
        cell.setCellValue("工程评委评分");
        cell.setCellStyle(cellStyle2);

        workbook.write(response.getOutputStream());
        response.flushBuffer();
    }


    public void downloadEvaluateDetailXlsx(HttpServletResponse response) throws IOException {

        String fileName = "SOP评价明细模板.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

        // 创建新的Excel 工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 在Excel工作簿中建一工作表，其名为缺省值

        XSSFSheet sheet = workbook.createSheet();
        sheet.setColumnWidth(0, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(1, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(2, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(3, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(4, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
        XSSFCellStyle cellStyle2 = workbook.createCellStyle();
//        HSSFCellStyle cellStyle2 = workbook.createCellStyle();

        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        XSSFFont font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle2.setFont(font);

        XSSFRow row = sheet.createRow((short)0);
        row.setHeight((short) 800);
        //在索引0的位置创建单元格（左上端）
        XSSFCell cell = row.createCell((short)0);
        // 创建样式 表头样式
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        cellStyle.setWrapText(true);
        XSSFDataFormat format = workbook.createDataFormat();
        cellStyle .setDataFormat(format.getFormat("@"));
        sheet.setDefaultColumnStyle(0, cellStyle );
        sheet.setDefaultColumnStyle(1, cellStyle );
        // 在单元格中输入一些内容
        cell.setCellValue("导入说明：\r\n" +
                "           1.\t红色字体为必填字段；\r\n" +
                "           2.\t学员工号必须为系统中已有在职数据，否则导入失败；");
        cell.setCellStyle(cellStyle);

        row = sheet.createRow(1);
        cell = row.createCell(0);
        cell.setCellValue("序号");
        cell.setCellStyle(cellStyle);
        cell =  row.createCell(1);
        cell.setCellValue("学员工号");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(2);
        cell.setCellValue("SOP名称");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(3);
        cell.setCellValue("质量评委评分");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(4);
        cell.setCellValue("工程评委评分");
        cell.setCellStyle(cellStyle2);

        workbook.write(response.getOutputStream());
        response.flushBuffer();
    }

    @Override
    public List<String> querySopNames(){
        return collegeTrainSopUploadMapper.querySopNames();
    }

}
