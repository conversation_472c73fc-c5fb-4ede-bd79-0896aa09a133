package com.htks.domain.school.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.htks.common.convert.Search;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/16 9:43
 */
@Data
public class CollegeTrainWeeklyReportRequest extends Search {

    private Long id;

    @ApiModelProperty("厂别")
    private String factoryType;

    @ApiModelProperty("班别")
    private String classType;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private List<String> practiceDepartmentNameList;

    @ApiModelProperty("如果选了区域,直接给对应配置主键")
    private String practiceDepartmentAreaId;

    @ApiModelProperty("得分")
    private Integer score;

    @ApiModelProperty("入职年份起始")
    private String enterDateStart;

    @ApiModelProperty("入职年份终止")
    private String enterDateEnd;

    @ApiModelProperty("周别")
    private Integer[] weekly;

    @ApiModelProperty("阅卷师工号")
    private String judgeNo;

    @ApiModelProperty("阅卷师名称")
    private String judgeName;


}
