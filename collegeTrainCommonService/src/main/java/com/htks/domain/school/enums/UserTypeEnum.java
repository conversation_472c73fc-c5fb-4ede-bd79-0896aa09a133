package com.htks.domain.school.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户类型枚举
 *
 * <AUTHOR>
 * @date 2023/05/09
 */
@Getter
@RequiredArgsConstructor
public enum UserTypeEnum {
    STUDENT(1, "学员登录"),
    GRADER(2, "阅卷师登录"),
    TRAINING_TEAM_LEADER(3, "培训组长登录"),
    ADMINISTRATOR(4, "管理员登录");


    private final Integer code;
    private final String info;

    public static UserTypeEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (UserTypeEnum e : UserTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }

}
