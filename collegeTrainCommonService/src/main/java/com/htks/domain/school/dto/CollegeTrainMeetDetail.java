package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 见面会详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_MEET_DETAIL")
@KeySequence(value = "COLLEGE_TRAIN_MEET_DETAIL_SEQ",dbType = DbType.SAP_HANA)
@ApiModel(value = "CollegeTrainMeetDetail对象", description = "大学生培训系统 见面会详情")
public class CollegeTrainMeetDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("COLLEGE_TRAIN_MEET.ID")
    @TableField("MEET_ID")
    private Long meetId;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @ApiModelProperty("评委姓名")
    @TableField("JUDGE_NAME")
    private String judgeName;

    @ApiModelProperty("评委工号")
    @TableField("JUDGE_NO")
    private String judgeNo;

    @ApiModelProperty("得分")
    @TableField("SCORE")
    private BigDecimal score;

    @TableField("DELETED_FLAG")
    private String deletedFlag;


}
