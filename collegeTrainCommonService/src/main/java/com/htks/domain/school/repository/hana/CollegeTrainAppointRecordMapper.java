package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.htks.domain.school.dto.CollegeTrainAppointRecord;
import com.htks.domain.school.request.CollegeTrainAppointRecordRequest;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.web.JsonPagedVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 大学生培训系统 预约记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainAppointRecordMapper extends BaseMapper<CollegeTrainAppointRecord> {

    @Select("<script>" +
            "SELECT ctar.ID, " +
            "ctar.TYPE ,ctar.STATUS ,ctar.SCORE ,ctar.PASSED_DATE ,ctar.PASSED ,(ctar.APPOINT_START_DATE ||' '||ctar.APPOINT_START_TIME) AS appointStartTime,(CTAR .APPOINT_END_DATE ||' '||ctar.APPOINT_END_TIME) AS appointEndTime,\n" +
            "cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,\n" +
            "ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ,ctpa.AREA ,ctar" +
            ".ADDITIONAL_POINTS,APPOINT_START_DATE,APPOINT_END_DATE,ctar.APPOINT_STATUS\n"+
            " FROM COLLEGE_TRAIN_APPOINT_RECORD ctar \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.ID = CTAR .ASSESSED_ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID = ctpa.ID \n" +
            "WHERE CTAR .DELETED_FLAG = FALSE AND cta.DELETED_FLAG = FALSE \n" +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.factoryType !=null and request.factoryType !=''\">  " +
            "    and cta.FACTORY_FLAG = #{request.factoryType}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.employeeName like CONCAT(CONCAT('%',#{request.employeeName}), '%')" +
            "</if>  " +
            "<if test=\"request.appointStatus.size > 0\">  " +
            "    and ctar.APPOINT_STATUS in " +
            "<foreach collection=\"request.appointStatus\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.ID = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "<if test=\"request.appointStartDate !=null and request.appointStartDate !='' \">  " +
            "and (ctar.APPOINT_START_DATE  <![CDATA[>= ]]> #{request.appointStartDate} OR (APPOINT_START_DATE <![CDATA[= ]]> #{request.appointStartDate} AND APPOINT_START_TIME <![CDATA[>= ]]> #{request.appointStartTime}))" +
            "</if>  " +
            "<if test=\"request.appointEndTime !=null and request.appointEndDate !=''\">  " +
            "AND (ctar.APPOINT_END_DATE <![CDATA[< ]]> #{request.appointEndDate} OR (ctar.APPOINT_END_DATE <![CDATA[= ]]> #{request.appointEndDate} AND ctar.APPOINT_END_TIME <![CDATA[<= ]]> #{request.appointEndTime}))" +
            "</if>  " +
            "<if test=\"request.status !=null and request.status !=''\">  " +
            "    and ctar.STATUS = #{request.status}  " +
            "</if>  " +
            "<if test=\"request.passed !=null \">  " +
            "    and ctar.PASSED = #{request.passed}  " +
            "</if>  " +
            "<if test=\"request.type !=null \">  " +
            "    and ctar.TYPE = #{request.type}  " +
            "</if>  " +
            "</script>")
    IPage<CollegeTrainAppointRecordVo> queryPaperList(@Param("page") Page<CollegeTrainAppointRecordVo> page, @Param("request") CollegeTrainAppointRecordRequest request);

    @Select("<script>" +
            "SELECT " +
            "ctar.TYPE ,ctar.STATUS ,ctar.SCORE ,ctar.PASSED_DATE ,ctar.PASSED ,ctar.APPOINT_START_TIME,ctar.APPOINT_END_TIME ,\n" +
            "cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,\n" +
            "ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ,ctpa.AREA,ctar.APPOINT_START_DATE,ctar.APPOINT_END_DATE ,ctar.ADDITIONAL_POINTS,ctar.APPOINT_STATUS as appointStatus\n"+
            " FROM COLLEGE_TRAIN_APPOINT_RECORD ctar \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.ID = CTAR .ASSESSED_ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID = ctpa.ID \n" +
            "WHERE CTAR .DELETED_FLAG = FALSE AND cta.DELETED_FLAG = FALSE \n" +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.factoryType !=null and request.factoryType !=''\">  " +
            "    and cta.FACTORY_FLAG = #{request.factoryType}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.ID = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "<if test=\"request.appointStartTime !=null and request.appointStartTime !='' \">  " +
            "    and ctar.APPOINT_START_TIME <![CDATA[>= ]]> #{request.appointStartTime}  " +
            "</if>  " +
            "<if test=\"request.appointEndTime !=null and request.appointEndTime !=''\">  " +
            "    and ctar.APPOINT_END_TIME <![CDATA[<= ]]> #{request.appointEndTime}  " +
            "</if>  " +
            "<if test=\"request.appointStartDate !=null and request.appointStartDate !='' \">  " +
            "    and ctar.APPOINT_START_DATE <![CDATA[>= ]]> #{request.appointStartDate}  " +
            "</if>  " +
            "<if test=\"request.appointEndTime !=null and request.appointEndDate !=''\">  " +
            "    and ctar.APPOINT_END_DATE <![CDATA[<= ]]> #{request.appointEndDate}  " +
            "</if>  " +
            "<if test=\"request.status !=null and request.status !=''\">  " +
            "    and ctar.STATUS = #{request.status}  " +
            "</if>  " +
            "<if test=\"request.passed !=null \">  " +
            "    and ctar.PASSED = #{request.passed}  " +
            "</if>  " +
            "<if test=\"request.type !=null \">  " +
            "    and ctar.TYPE = #{request.type}  " +
            "</if>  " +
            "<if test=\"request.appointStatus.size > 0\">  " +
            "    and ctar.APPOINT_STATUS in " +
            "<foreach collection=\"request.appointStatus\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "</script>")
    List<CollegeTrainAppointRecordVo> getList(@Param("request") CollegeTrainAppointRecordRequest request);


    @Update("update COLLEGE_TRAIN_APPOINT_RECORD set APPOINT_STATUS = '2' where ID = #{id}")
    int updateAppointRecode(@Param("id") Long id);

    @Select("select STATUS from COLLEGE_TRAIN_APPOINT_RECORD  where ID = #{id}")
    String queryAppointRecode(@Param("id") Long id);
}
