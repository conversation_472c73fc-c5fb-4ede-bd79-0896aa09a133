package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.htks.domain.school.dto.CollegeTrainSubjectiveJudge;
import com.htks.domain.school.request.CollegeTrainSubjectiveJudgeRequest;
import com.htks.domain.school.vo.CollegeTrainSubjectiveJudgeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 大学生培训系统 培训组长主观分评价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainSubjectiveJudgeMapper extends BaseMapper<CollegeTrainSubjectiveJudge> {

    @Select("<script>" +
            "SELECT ctsj.ID, cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,cta.MASTER_NO ,cta.MASTER_NAME ,ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName,ctpa.AREA ,ctsj.SCORE \n" +
            "FROM COLLEGE_TRAIN_SUBJECTIVE_JUDGE ctsj\n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta  ON ctsj.ASSESSED_ID = cta.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON ctpa.ID =cta.PRACTICE_DEPARTMENT_AREA_ID \n" +
            "WHERE cta.DELETED_FLAG =FALSE " +
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.masterName !=null and request.masterName !=''\">  " +
            "    and cta.MASTER_NAME = #{request.masterName}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "</script>")
    IPage<CollegeTrainSubjectiveJudgeVo> queryPaperList(@Param("page") Page<CollegeTrainSubjectiveJudgeVo> page, @Param("request") CollegeTrainSubjectiveJudgeRequest request);
}
