package com.htks.domain.school.request;

import com.htks.common.convert.Search;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/10 9:53
 */
@Data
public class CollegeTrainEnabledRequest {

    @ApiModelProperty("学员工号")
    @NotBlank(message = "学员工号不能为空")
    private String employeeNo;

    @ApiModelProperty("状态：开启、关闭")
    @NotBlank(message = "状态不能为空")
    private String enabled;
}
