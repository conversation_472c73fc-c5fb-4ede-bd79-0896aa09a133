package com.htks.domain.school.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SubjectiveExcel {

    @ExcelProperty("序号")
    private String index;

    @ExcelProperty("考试编号")
    private String examNumber;

    @ExcelProperty("考试名称")
    private String examName;

    @ExcelProperty("实际考试时间")
    private String examDatetime;

    @ExcelProperty("考试科目")
    private String examClass;


    @ExcelProperty("学员姓名")
    private String employeeName;

    @ExcelProperty("学员工号")
    private String employeeNo;

    @ExcelProperty("实习部门")
    private String practiceDepartmentName;

    @ExcelProperty("区域/制程")
    private String area;

    @ExcelProperty("简答题得分")
    private String subjectiveScore;

    @ExcelProperty("总分")
    private String totalScore;
}
