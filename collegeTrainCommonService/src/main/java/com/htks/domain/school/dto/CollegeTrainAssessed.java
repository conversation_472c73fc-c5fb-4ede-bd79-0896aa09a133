package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@TableName("COLLEGE_TRAIN_ASSESSED")
@ApiModel(value = "CollegeTrainAssessed对象", description = "大学生培训系统 大学生信息配置表")
@KeySequence(value = "COLLEGE_TRAIN_ASSESSED_SEQ",dbType = DbType.SAP_HANA)
@EqualsAndHashCode(callSuper = false)
public class CollegeTrainAssessed extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("读后感截止上传时间")
    @TableField("AFTER_READING_UPLOAD_DEAD_TIME")
    private String afterReadingUploadDeadTime;

    @ApiModelProperty("批次")
    @TableField("BATCH")
    private String batch;

    @ApiModelProperty("选岗部门名称")
    @TableField("CHOOSE_DEPARTMENT_NAME")
    private String chooseDepartmentName;

    @ApiModelProperty("选岗部门ID")
    @TableField("CHOOSE_DEPARTMENT_ID")
    private String chooseDepartmentId;

    @ApiModelProperty("实习部门ID")
    @TableField("DEPARTMENT_ID")
    private String departmentId;

    @ApiModelProperty("学员姓名")
    @TableField("EMPLOYEE_NAME")
    private String employeeName;

    @ApiModelProperty("学员工号")
    @TableField("EMPLOYEE_NO")
    private String employeeNo;

    @ApiModelProperty("入职时间")
    @TableField("ENTRY_DATE")
    private String entryDate;

    @ApiModelProperty("异常答辩截止时间")
    @TableField("EXCEPTION_DEFENSE_END_TIME")
    private String exceptionDefenseEndTime;

    @ApiModelProperty("异常答辩开始时间")
    @TableField("EXCEPTION_DEFENSE_START_TIME")
    private String exceptionDefenseStartTime;

    @ApiModelProperty("师傅姓名")
    @TableField("MASTER_NAME")
    private String masterName;

    @ApiModelProperty("师傅工号")
    @TableField("MASTER_NO")
    private String masterNo;

    @ApiModelProperty("上岗证获取截止时间")
    @TableField("POST_CERTIFICATE_END_TIME")
    private String postCertificateEndTime;

    @ApiModelProperty("上岗证获取开始时间")
    @TableField("POST_CERTIFICATE_START_TIME")
    private String postCertificateStartTime;

    @ApiModelProperty("工程实操考试截止时间")
    @TableField("PRACTICAL_OPERATION_END_TIME")
    private String practicalOperationEndTime;

    @ApiModelProperty("工程实操考试开始时间")
    @TableField("PRACTICAL_OPERATION_START_TIME")
    private String practicalOperationStartTime;

    @ApiModelProperty("实习部门ID")
    @TableField("PRACTICE_DEPARTMENT_AREA_ID")
    private String practiceDepartmentAreaId;

    @ApiModelProperty("SOP截止上传时间")
    @TableField("SOP_UPLOAD_DEAD_TIME")
    private String sopUploadDeadTime;

    @ApiModelProperty("厂别：华天昆山、华天江苏")
    @TableField("FACTORY_FLAG")
    private String factoryFlag;

    @ApiModelProperty("班别")
    @TableField("CLASS_NO")
    private String classNo;

    @ApiModelProperty("进入BU时间")
    @TableField("JOIN_BU_TIME")
    private String joinBuTime;

    @ApiModelProperty("状态：开启、关闭")
    @TableField("ENABLED")
    private String enabled;


}
