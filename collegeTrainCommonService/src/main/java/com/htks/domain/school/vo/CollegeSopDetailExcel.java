package com.htks.domain.school.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CollegeSopDetailExcel {

    @ExcelProperty("序号")
    private int id;

    @ExcelProperty("厂别")
    private String companyName;

    @ExcelProperty("批次")
    private String batch;

    @ExcelProperty("学员姓名")
    private String employeeName;

    @ExcelProperty("学员工号")
    private String employeeNo;

    @ExcelProperty("实习部门")
    private String practiceDepartment;

    @ExcelProperty("区域/制程")
    private String area;

    @ExcelProperty("SOP名称")
    private String sopName;

    @ExcelProperty("质量评委评分")
    private String qaScore;

    @ExcelProperty("工程评委评分")
    private String enScore;

    @ExcelProperty("得分")
    private String resultScore;

    public CollegeSopDetailExcel(int id, String companyName, String batch) {
        this.id = id;
        this.companyName = companyName;
        this.batch = batch;
    }
}
