package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 岗位认证系统 考试记录答案表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_EXAM_RECORD_ANSWER")
@ApiModel(value = "CollegeTrainExamRecordAnswer对象", description = "岗位认证系统 考试记录答案表")
public class CollegeTrainExamRecordAnswer implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("答案")
    @TableField("ANSWER_TEXT")
    private String answerText;

    @ApiModelProperty("附件ID")
    @TableField("ATTACHMENT_ID")
    private Long attachmentId;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @ApiModelProperty("考试id")
    @TableField("EXAM_ID")
    private Long examId;

    @ApiModelProperty("问题id")
    @TableField("QUESTION_ID")
    private Long questionId;

    @ApiModelProperty("得分")
    @TableField("SCORE")
    private BigDecimal score;
    @ApiModelProperty("说明")
    @TableField("NOTE")
    private String note;

}
