package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 上岗证
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_POST_CARD")
@ApiModel(value = "CollegeTrainPostCard对象", description = "大学生培训系统 上岗证")
public class CollegeTrainPostCard implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty("上岗证对应的 区域/制程")
    @TableField("POST_AREA")
    private String postArea;

    @ApiModelProperty("上岗证名称及编号")
    @TableField("POST_NAME")
    private String postName;

    @TableField("UPDATED_TIME")
    private Date updatedTime;

    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty("上岗证上传时间")
    @TableField("UPLOAD_TIME")
    private Date uploadTime;


}
