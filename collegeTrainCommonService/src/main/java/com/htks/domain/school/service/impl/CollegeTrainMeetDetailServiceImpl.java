package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.domain.school.dto.CollegeTrainMeet;
import com.htks.domain.school.dto.CollegeTrainMeetDetail;
import com.htks.domain.school.repository.hana.CollegeTrainMeetDetailMapper;
import com.htks.domain.school.repository.hana.CollegeTrainMeetMapper;
import com.htks.domain.school.service.CollegeTrainMeetDetailService;
import com.htks.domain.school.service.CollegeTrainMeetService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 大学生培训系统 见面会详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CollegeTrainMeetDetailServiceImpl extends ServiceImpl<CollegeTrainMeetDetailMapper, CollegeTrainMeetDetail> implements CollegeTrainMeetDetailService {

    @Override
    public void saveOrUpdateMeetDetail(List<CollegeTrainMeetDetail> collect, Long meetId, String type) {
        if ("update".equals(type)) {
            LambdaUpdateWrapper<CollegeTrainMeetDetail> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(CollegeTrainMeetDetail::getMeetId, meetId)
                    .set(CollegeTrainMeetDetail::getDeletedFlag, Boolean.TRUE);

            update(new CollegeTrainMeetDetail(), updateWrapper);
        }
        saveBatch(collect);
    }
}
