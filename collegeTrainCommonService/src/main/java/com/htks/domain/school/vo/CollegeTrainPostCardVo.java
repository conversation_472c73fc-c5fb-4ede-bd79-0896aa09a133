package com.htks.domain.school.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.domain.school.dto.CollegeTrainPostArea;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 上岗证
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainPostCardVo {

    private Long id;

    private Long assessedId;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("上岗证获取截止时间")
    private String postCertificateEndTime;

    @ApiModelProperty("上岗证获取开始时间")
    private String postCertificateStartTime;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("当部门当区数量")
    private Integer areaCount;

    @ApiModelProperty("总数量")
    private Integer allCount;

    @ApiModelProperty("得分")
    private Integer score;

}
