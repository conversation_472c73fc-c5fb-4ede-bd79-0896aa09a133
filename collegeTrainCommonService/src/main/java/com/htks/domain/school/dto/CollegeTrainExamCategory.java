package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 大学生 考试配置项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_EXAM_CATEGORY")
@ApiModel(value = "CollegeTrainExamCategory对象", description = "大学生 考试配置项目")
public class CollegeTrainExamCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("培训考试项目")
    @TableField("CATEGORY")
    private String category;

    @ApiModelProperty("培训考试项目名称")
    @TableField("CATEGORY_ITEM")
    private String categoryItem;

    @ApiModelProperty("试卷类型")
    @TableField("PAPER_TYPE")
    private String paperType;

    @ApiModelProperty("考试时长")
    @TableField("TEST_TIME")
    private Integer testTime;

    @ApiModelProperty("题库名称")
    @TableField("TITLE")
    private String title;

    @TableField("SINGLE_OPTION_ANSWER")
    @ApiModelProperty("单选题满分")
    private BigDecimal singleOptionAnswer;

    @TableField("MORE_OPTION_ANSWER")
    @ApiModelProperty("多选题满分")
    private BigDecimal moreOptionAnswer;

    @TableField("JUDGE_ANSWER")
    @ApiModelProperty("判断题满分")
    private BigDecimal judgeAnswer;

    @TableField("FILL_ANSWER")
    @ApiModelProperty("填空题满分")
    private BigDecimal fillAnswer;

    @TableField("SHORT_ANSWER")
    @ApiModelProperty("解答题满分")
    private BigDecimal shortAnswer;

    @TableField("SINGLE_OPTION_NUMBER")
    @ApiModelProperty("单选题题数")
    private Integer singleOptionNumber;

    @TableField("MORE_OPTION_NUMBER")
    @ApiModelProperty("多选题题数")
    private Integer moreOptionNumber;

    @TableField("JUDGE_NUMBER")
    @ApiModelProperty("判断题题数")
    private Integer judgeNumber;

    @TableField("FILL_NUMBER")
    @ApiModelProperty("填空题题数")
    private Integer fillNumber;

    @TableField("SHORT_NUMBER")
    @ApiModelProperty("简答题题数")
    private Integer shortNumber;


}
