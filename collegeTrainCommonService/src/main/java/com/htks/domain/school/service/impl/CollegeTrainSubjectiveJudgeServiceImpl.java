package com.htks.domain.school.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.EasyExcelUtils;
import com.htks.common.utils.JudgeNumberUtils;
import com.htks.common.utils.PageUtils;
import com.htks.common.utils.SheetUtil;
import com.htks.domain.school.dto.*;
import com.htks.domain.school.enums.EmployeeStatusEnum;
import com.htks.domain.school.excel.CollegeTrainAfterReadingExcel;
import com.htks.domain.school.excel.CollegeTrainExamRecordExcel;
import com.htks.domain.school.excel.CollegeTrainSubjectiveJudgeExcel;
import com.htks.domain.school.repository.hana.CollegeTrainSubjectiveJudgeMapper;
import com.htks.domain.school.request.CollegeTrainSubjectiveJudgeRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainSubjectiveJudgeService;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.domain.school.vo.CollegeTrainSubjectiveJudgeVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 * 大学生培训系统 培训组长主观分评价 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainSubjectiveJudgeServiceImpl extends ServiceImpl<CollegeTrainSubjectiveJudgeMapper, CollegeTrainSubjectiveJudge> implements CollegeTrainSubjectiveJudgeService {
    @Resource
    private CommonEmployeeService commonEmployeeService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @Override
    public JsonPagedVO getPageList(CollegeTrainSubjectiveJudgeRequest request) {
        if (request.getPracticeDepartmentNameList()!=null) {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                list.add(uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i)));
            }
            List<String> factory = new ArrayList<>();
            request.setPracticeDepartmentNameList(list);
        }

        Page<CollegeTrainSubjectiveJudgeVo> page = PageUtils.getPage(request);

        IPage<CollegeTrainSubjectiveJudgeVo> collegeTrainSubjectiveJudgePage = this.baseMapper.queryPaperList(page,request);
        return JsonPagedVO.success(collegeTrainSubjectiveJudgePage.getRecords(),Integer.parseInt(collegeTrainSubjectiveJudgePage.getTotal()+""));
    }

    @Override
    public void downTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainSubjectiveJudgeExcel> list = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setNo(String.valueOf(i+1));
        }
        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();

        SheetUtil sheetUtil = new SheetUtil(fieldValues);
        CustomSheetWriteHandler customSheetWriteHandler = new CustomSheetWriteHandler();

        String fileName = URLEncoder.encode("培训组长主观分评价模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        EasyExcel.write(response.getOutputStream(), CollegeTrainSubjectiveJudgeExcel.class)
                .registerWriteHandler(new CustomSheetWriteHandler())
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("培训组长主观分评价模").registerWriteHandler(sheetUtil).doWrite(list);
    }

    @Override
    public void startEvaluation(CollegeTrainSubjectiveJudgeRequest request) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(request);
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getId());
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getScore());

        CollegeTrainSubjectiveJudge collegeTrainSubjectiveJudge = new CollegeTrainSubjectiveJudge();
        collegeTrainSubjectiveJudge.setId(request.getId());
        collegeTrainSubjectiveJudge.setScore(request.getScore());
        updateById(collegeTrainSubjectiveJudge);
    }

    @Override
    public ResultVO importTemplate(MultipartFile file) {
        List<CollegeTrainSubjectiveJudgeExcel> collegeTrainSubjectiveJudgeExcelList = null;
        try {
            collegeTrainSubjectiveJudgeExcelList = EasyExcelUtils.readExcel(file, CollegeTrainSubjectiveJudgeExcel.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }
        List<CollegeTrainSubjectiveJudge> list = new ArrayList<>();
        List<CollegeTrainSubjectiveJudgeExcel> remove = new ArrayList<>();

        int a=-1;
        for  (int i = 0; i < collegeTrainSubjectiveJudgeExcelList.size(); i++) {
            CollegeTrainSubjectiveJudgeExcel collegeTrainEngineeringExamRecordExcel = collegeTrainSubjectiveJudgeExcelList.get(i);

            if (collegeTrainEngineeringExamRecordExcel.getEmployeeNo()==null&&collegeTrainEngineeringExamRecordExcel.getScore()==null){
                remove.add(collegeTrainEngineeringExamRecordExcel);
            }
            if (collegeTrainEngineeringExamRecordExcel.getNo()==null){}

            else {
                if (collegeTrainEngineeringExamRecordExcel.getNo().contains("导入文档说明")) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a< collegeTrainSubjectiveJudgeExcelList.size()) {
            collegeTrainSubjectiveJudgeExcelList.subList(a, collegeTrainSubjectiveJudgeExcelList.size()).clear();
        }
        collegeTrainSubjectiveJudgeExcelList.removeAll(remove);
        if (ObjectUtil.isNotEmpty(collegeTrainSubjectiveJudgeExcelList)) {
            for (int i = 0; i < collegeTrainSubjectiveJudgeExcelList.size(); i++) {
                CollegeTrainSubjectiveJudgeExcel collegeTrainSubjectiveJudgeExcel = collegeTrainSubjectiveJudgeExcelList.get(i);
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSubjectiveJudgeExcel.getEmployeeNo(), "第" + (i + 2) + "行员工编号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSubjectiveJudgeExcel.getScore(), "第" + (i + 2) + "行得分不能为空!");
                //获取员工信息
                if (!isPositiveInteger(collegeTrainSubjectiveJudgeExcel.getScore())){
                    return ResultVO.error("第" + (i + 2) + "行得分不是整数");
                }
                CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainSubjectiveJudgeExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(employeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行，未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //获取大学生信息
                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(collegeTrainSubjectiveJudgeExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(collegeTrainAssessed)) {
                    return ResultVO.error("第" + (i + 2) + "行数据未找到相应人员");
                }
                CollegeTrainSubjectiveJudge collegeTrainSubjectiveJudge = new CollegeTrainSubjectiveJudge();
                //根据员工id进行数据匹配
                CollegeTrainSubjectiveJudge dbCollegeTrainSubjectiveJudge = getByCondition(collegeTrainAssessed.getId());
                if (ObjectUtil.isNotEmpty(dbCollegeTrainSubjectiveJudge)) {
                    collegeTrainSubjectiveJudge.setId(dbCollegeTrainSubjectiveJudge.getId());
                }
                //封装参数进行更新
                collegeTrainSubjectiveJudge.setScore(Integer.parseInt(collegeTrainSubjectiveJudgeExcel.getScore()));
                collegeTrainSubjectiveJudge.setAssessedId(collegeTrainAssessed.getId());

                list.add(collegeTrainSubjectiveJudge);
            }
        }

        List<CollegeTrainSubjectiveJudge> listInsert = new ArrayList<>();
        List<CollegeTrainSubjectiveJudge> listUpdate = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            int sum= uploadWorkLicense.getSubjectiveSum(list.get(i).getAssessedId());
            if(sum==0){
                listInsert.add(list.get(i));
            }else {
                listUpdate.add(list.get(i));
            }
        }
        if(listInsert.size()>0){
            for (CollegeTrainSubjectiveJudge collegeTrainSubjectiveJudge:listInsert) {
                uploadWorkLicense.insertSUBJECTIVE(collegeTrainSubjectiveJudge);
            }
        }
        if(listUpdate.size()>0){
            for(CollegeTrainSubjectiveJudge collegeTrainSubjectiveJudge:listUpdate){
                List<CollegeTrainSubjectiveJudge> listUpdateTwo = new ArrayList<>();
                listUpdateTwo.add(collegeTrainSubjectiveJudge);
                uploadWorkLicense.updateSubjective(listUpdateTwo);
            }
        }

        return ResultVO.success("成功导入" + list.size() + "数据");
    }

    private CollegeTrainSubjectiveJudge getByCondition(Long assessedId) {
        LambdaQueryWrapper<CollegeTrainSubjectiveJudge> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(CollegeTrainSubjectiveJudge::getAssessedId,assessedId);
        return getOne(lambdaQuery);
    }

    public class CustomSheetWriteHandler implements SheetWriteHandler {
        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            Row row = sheet.getRow(20);  // 获取第十行
            if (row == null) {
                row = sheet.createRow(20);
            }
            Cell cell = row.createCell(0);  // 获取第一列
            cell.setCellValue(
                    "导入文档说明:\n" +
                            "(1)红色为必填项:\n" +
                            "(2)学员工号和阅卷师工号必须为人事系统中人员信息中已有的数据，如不是则提示导入失败;\n" +
                            "(3)得分必须为整数。");
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                if (mergedRegion.getFirstRow() == 20 && mergedRegion.getFirstColumn() == 0 && mergedRegion.getLastColumn() == 6) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 合并第十行的第1到第5列
            sheet.addMergedRegion(new CellRangeAddress(
                    20, // 第十行
                    26, // 第十行
                    0, // 第一列
                    6  // 第五列
            ));
        }
    }
    public static boolean isPositiveInteger(String str) {
        // 使用正则表达式判断字符串是否只包含数字，并且第一个字符不是0
        return str.matches("[0-9]\\d*");
    }
}
