package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.repository.hana.CommonEmployeeMapper;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.sys.dto.SysEmployeeEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 员工表(每日同步) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Service
public class CommonEmployeeServiceImpl extends ServiceImpl<CommonEmployeeMapper, CommonEmployee> implements CommonEmployeeService {

    @Override
    public CommonEmployee getEmployeeInfo(String employeeNumber) {
        LambdaQueryWrapper<CommonEmployee> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(CommonEmployee::getEmployeeNumber,employeeNumber);
        return getOne(lambdaQuery);
    }

    @Override
    public List<CommonEmployee> getEmployeeListByDepartmentName(String departmentName) {
        LambdaQueryWrapper<CommonEmployee> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(CommonEmployee::getDepartmentName,departmentName)
                .eq(CommonEmployee::getEmployeeStatus,"在职");
        return list(lambdaQuery);
    }

    @Override
    public CommonEmployee getEmployeeInfo(String employeeNumber, String employeeStatus) {
        LambdaQueryWrapper<CommonEmployee> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(CommonEmployee::getEmployeeNumber,employeeNumber)
                .eq(CommonEmployee::getEmployeeStatus,employeeStatus);
        return getOne(lambdaQuery);
    }
}
