package com.htks.domain.school.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.exception.CustomerException;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.CheckNumberUtils;
import com.htks.common.utils.EasyExcelUtils;
import com.htks.common.utils.JudgeNumberUtils;
import com.htks.common.utils.PageUtils;
import com.htks.common.utils.SheetUtil;
import com.htks.domain.school.dto.*;
import com.htks.domain.school.enums.AllocationStatusEnum;
import com.htks.domain.school.enums.EmployeeStatusEnum;
import com.htks.domain.school.excel.*;
import com.htks.domain.school.repository.hana.CollegeTrainExamRecordMapper;
import com.htks.domain.school.repository.hana.CollegeTrainSopUploadMapper;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.request.StartReviewingRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainExamRecordAnswerService;
import com.htks.domain.school.service.CollegeTrainExamRecordService;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.school.vo.CollegeSopDetailExcel;
import com.htks.domain.school.vo.CollegeSopDetailVO;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.domain.school.vo.SubjectiveExcel;
import com.htks.domain.student.repository.hana.ExamRepository;
import com.htks.domain.student.repository.hana.ReservationAppoint;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.utils.HttpUtils;
import com.htks.web.JsonPagedVO;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import static com.htks.common.utils.EasyExcelUtils.exportExcel;

/**
 * <p>
 * 大学生 考试记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainExamRecordServiceImpl extends ServiceImpl<CollegeTrainExamRecordMapper, CollegeTrainExamRecord> implements CollegeTrainExamRecordService {
    @Resource
    private CommonEmployeeService commonEmployeeService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @Resource
    private CollegeTrainExamRecordAnswerService collegeTrainExamRecordAnswerService;
    @Resource
    private ReservationAppoint removeById;
    @Resource
    private CollegeTrainSopUploadMapper collegeTrainSopUploadMapper;
    @Resource
    private UploadWorkLicense uploadWorkLicense;
    @Resource
    private ExamRepository examRepository;
    @Override
    public JsonPagedVO<List<CollegeTrainExamRecordVo>> queryPaperList(CollegeTrainExamRecordRequest request) {
        Page<CollegeTrainExamRecordVo> page = PageUtils.getPage(request);

        IPage<CollegeTrainExamRecordVo> collegeTrainExamRecordPage = this.baseMapper.queryPaperList(page,request);

        List<CollegeTrainExamRecordVo> records = collegeTrainExamRecordPage.getRecords();

        for (CollegeTrainExamRecordVo record : records) {
            if (record.getTotalScore() == null) {
                record.setStatus("未批阅");
            }else {
                record.setStatus("已批阅");
            }
        }

// 修改完数据后，更新回分页对象
        collegeTrainExamRecordPage.setRecords(records);
        return JsonPagedVO.success(collegeTrainExamRecordPage.getRecords(),Integer.parseInt(collegeTrainExamRecordPage.getTotal()+""));
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainExamRecordExcel> list = new ArrayList<>();

        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();

        SheetUtil sheetUtil = new SheetUtil(fieldValues);

        CustomSheetWriteHandlerTwo customSheetWriteHandler = new CustomSheetWriteHandlerTwo();
        String fileName = URLEncoder.encode("批量分配阅卷师模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        EasyExcel.write(response.getOutputStream(), CollegeTrainExamRecordExcel.class)
                .registerWriteHandler(new CustomSheetWriteHandlerTwo())
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("分配阅卷师").registerWriteHandler(sheetUtil).doWrite(list);
    }

    @Override
    public void export(CollegeTrainExamRecordRequest request,HttpServletResponse response) {
        List<CollegeTrainExamRecordExportExcel> list = this.baseMapper.getList(request);
        for (int i = 0; i <list.size() ; i++) {
            list.get(i).setNumber(i+1);
        }
        list.stream().forEach(o -> {
            o.setExamTime(o.getExamStartTime() + "~" + o.getExamEndTime());
        });
        EasyExcelUtils.exportExcel(response, list, "考试信息", CollegeTrainExamRecordExportExcel.class);
    }

    @Override
    public void assignExaminer(CollegeTrainExamRecordRequest request) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(request);
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getId(),"数据id必传");
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getJudgeNo(),"阅卷师工号必传");
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getExamName(),"考试名称必传");
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getJudgeName(),"阅卷师名称必传");
        String judgeNumber = JudgeNumberUtils.getJudgeNumber(request.getExamName());

        CollegeTrainExamRecord dbCollegeTrainExamRecord = getById(request.getId());
        if (ObjectUtil.isNotEmpty(dbCollegeTrainExamRecord.getTotalScore())){
            throw new CustomerException("该试卷已经被批阅,无法重新分配阅卷师", ResultCode.FAILED.getCode());
        }

        CollegeTrainExamRecord collegeTrainExamRecord = new CollegeTrainExamRecord();
        collegeTrainExamRecord.setId(request.getId());
        collegeTrainExamRecord.setJudgeName(request.getJudgeName());
        collegeTrainExamRecord.setJudgeNo(request.getJudgeNo());
        collegeTrainExamRecord.setJudgeNumber(judgeNumber);
        collegeTrainExamRecord.setAllocationStatus(AllocationStatusEnum.ALLOCATED.getCode());

        updateById(collegeTrainExamRecord);
    }

    @Override
    public ResultVO importTemplate(MultipartFile file) {
        List<CollegeTrainExamRecordExcelTwo> collegeTrainExamRecordExcelList = null;
        try {
            collegeTrainExamRecordExcelList = EasyExcelUtils.readExcel(file, CollegeTrainExamRecordExcelTwo.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }
        List<CollegeTrainExamRecord> list = new ArrayList<>();
        int a=-1;
        for  (int i = 0; i < collegeTrainExamRecordExcelList.size(); i++) {
            CollegeTrainExamRecordExcelTwo collegeTrainEngineeringExamRecordExcel = collegeTrainExamRecordExcelList.get(i);

            if (collegeTrainEngineeringExamRecordExcel.getNumber()==null){

            }else {
                if (collegeTrainEngineeringExamRecordExcel.getNumber().contains("导入文档说明")) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a< collegeTrainExamRecordExcelList.size()) {
            collegeTrainExamRecordExcelList.subList(a, collegeTrainExamRecordExcelList.size()).clear();
        }
        if (ObjectUtil.isNotEmpty(collegeTrainExamRecordExcelList)) {
            for (int i = 0; i < collegeTrainExamRecordExcelList.size(); i++) {
                CollegeTrainExamRecordExcelTwo collegeTrainExamRecordExcel = collegeTrainExamRecordExcelList.get(i);
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainExamRecordExcel.getExamNumber(), "第" + (i + 2) + "行考试编号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainExamRecordExcel.getExamName(), "第" + (i + 2) + "行考试名称不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainExamRecordExcel.getEmployeeNo(), "第" + (i + 2) + "行学员工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainExamRecordExcel.getJudgeNo(), "第" + (i + 2) + "行阅卷师工号不能为空!");
                //获取员工信息
                CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainExamRecordExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(employeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //获取阅卷师信息,必须是在职状态
                CommonEmployee judgeEmployeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainExamRecordExcel.getJudgeNo(), EmployeeStatusEnum.ON_THE_JOB.getCode());
                if (ObjectUtil.isEmpty(judgeEmployeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行第6列数据不存在");
                }
                //获取大学生信息
                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(collegeTrainExamRecordExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(collegeTrainAssessed)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //根据考试编号、考试名称和学员工号共同进行数据匹配
                CollegeTrainExamRecord collegeTrainExamRecord = getByConditionTwo(collegeTrainExamRecordExcel,collegeTrainAssessed.getId(),null);
                if (ObjectUtil.isEmpty(collegeTrainExamRecord)) {
                    return ResultVO.error("第" + (i + 2) + "行考试数据不存在");
                }
                //校验是否被批阅
                if (ObjectUtil.isNotEmpty(collegeTrainExamRecord.getTotalScore())){
                    return ResultVO.error("第" + (i + 2) + "行数据已被批阅，不可再次导入");
                }
                //封装参数进行更新
                CollegeTrainExamRecord newCollegeTrainExamRecord = new CollegeTrainExamRecord();
                newCollegeTrainExamRecord.setId(collegeTrainExamRecord.getId());
                newCollegeTrainExamRecord.setJudgeNumber(JudgeNumberUtils.getJudgeNumber(collegeTrainExamRecordExcel.getExamName()));
                newCollegeTrainExamRecord.setJudgeNo(collegeTrainExamRecordExcel.getJudgeNo());
                newCollegeTrainExamRecord.setJudgeName(judgeEmployeeInfo.getEmployeeName());
                newCollegeTrainExamRecord.setAllocationStatus("已分配");
                list.add(newCollegeTrainExamRecord);
            }
        }
        updateBatchById(list);
        return ResultVO.success("成功导入" + list.size() + "数据");
    }

    @Override
    public JsonPagedVO<CollegeTrainExamRecordVo> getForApprovalPageList(CollegeTrainExamRecordRequest request) {
        Page<CollegeTrainExamRecordVo> page = PageUtils.getPage(request);

        IPage<CollegeTrainExamRecordVo> collegeTrainExamRecordPage = this.baseMapper.getForApprovalPageList(page,request);
        return JsonPagedVO.success(collegeTrainExamRecordPage.getRecords(),Integer.parseInt(collegeTrainExamRecordPage.getTotal()+""));

    }

    @Override
    public ResultVO startReviewing(StartReviewingRequest request) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(request);
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getExamId());
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getSubjectiveScore());
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getExamRecordAnswerList());

        CollegeTrainExamRecord collegeTrainExamRecord = getById(request.getExamId());
        if (ObjectUtil.isEmpty(collegeTrainExamRecord)) {
            return ResultVO.error("获取不到试卷信息");
        }
        //更新试卷的简答题得分/总得分/评分理由
        collegeTrainExamRecord.setSubjectiveScore(request.getSubjectiveScore());
        collegeTrainExamRecord.setEvaluateReason(request.getEvaluateReason());

        String objectiveScore = collegeTrainExamRecord.getObjectiveScore();
        Integer totalScore = Integer.parseInt(request.getSubjectiveScore().toString()) + (ObjectUtil.isEmpty(objectiveScore) ? 0 : Integer.parseInt(objectiveScore));
        collegeTrainExamRecord.setTotalScore(totalScore.toString());
        updateById(collegeTrainExamRecord);

        collegeTrainExamRecordAnswerService.updateExamRecordAnswer(request.getExamRecordAnswerList());
        return ResultVO.success();

    }

    @Override
    public JsonPagedVO<CollegeTrainExamRecordVo> getApprovedPageList(CollegeTrainExamRecordRequest request) {
        Page<CollegeTrainExamRecordVo> page = PageUtils.getPage(request);

        IPage<CollegeTrainExamRecordVo> collegeTrainExamRecordPage = this.baseMapper.getApprovedPageList(page,request);
        return JsonPagedVO.success(collegeTrainExamRecordPage.getRecords(),Integer.parseInt(collegeTrainExamRecordPage.getTotal()+""));

    }

    @Override
    public JsonPagedVO<List<CollegeTrainExamRecordVo>> getEngineeringPageList(CollegeTrainExamRecordRequest request) {
        Page<CollegeTrainExamRecordVo> page = PageUtils.getPage(request);

        if (!StringUtils.isEmpty(request.getExamStartTime())){
            request.setExamStartTime(request.getExamStartTime()+" 00:00:00");
            request.setExamEndTime(request.getExamEndTime()+" 23:59:59");
        }
        IPage<CollegeTrainExamRecordVo> collegeTrainExamRecordPage = this.baseMapper.getEngineeringPageList(page,request);
        List<CollegeTrainExamRecordVo>list=collegeTrainExamRecordPage.getRecords();
        for (CollegeTrainExamRecordVo recordVo:list) {
            if (recordVo.getSubjectiveScore()==null){
                recordVo.setSubjectiveScore("0");
            }
            if (recordVo.getObjectiveScore()==null){
                recordVo.setObjectiveScore("0");
            }
            if (recordVo.getExamName().contains("工程结业理论考试")){
                recordVo.setExamClass("工程结业理论考试");
            }else {
                recordVo.setExamClass("工艺公开课理论考试");
            }

            double v = new Double(recordVo.getSubjectiveScore()) + new Double(recordVo.getObjectiveScore());
            recordVo.setTotalScore(v+"");
        }
        return JsonPagedVO.success(collegeTrainExamRecordPage.getRecords(),Integer.parseInt(collegeTrainExamRecordPage.getTotal()+""));

    }

    @Override
    public void downloadEngineeringTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainEngineeringExamRecordExcel> list = new ArrayList<>();

        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();

        SheetUtil sheetUtil = new SheetUtil(fieldValues);
        CustomSheetWriteHandler customSheetWriteHandler = new CustomSheetWriteHandler();

        String fileName = URLEncoder.encode("工程结业理论考试模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        EasyExcel.write(response.getOutputStream(), CollegeTrainEngineeringExamRecordExcel.class)
                .registerWriteHandler(new CustomSheetWriteHandler())
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("工程结业理论考试").registerWriteHandler(sheetUtil).doWrite(list);
    }

    @Override
    public ResultVO importEngineeringTemplate(MultipartFile file) {
        List<CollegeTrainEngineeringExamRecordExcel> collegeTrainEngineeringExamRecordExcels = null;
        try {
            collegeTrainEngineeringExamRecordExcels = EasyExcelUtils.readExcel(file, CollegeTrainEngineeringExamRecordExcel.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }
        List<CollegeTrainExamRecord> list = new ArrayList<>();
        int a=-1;
        for  (int i = 0; i < collegeTrainEngineeringExamRecordExcels.size(); i++) {
            CollegeTrainEngineeringExamRecordExcel collegeTrainEngineeringExamRecordExcel = collegeTrainEngineeringExamRecordExcels.get(i);
            if (collegeTrainEngineeringExamRecordExcel.getNumber()==null){

            }else {
                if (collegeTrainEngineeringExamRecordExcel.getNumber().contains("导入文档说明")) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a< collegeTrainEngineeringExamRecordExcels.size()) {
            collegeTrainEngineeringExamRecordExcels.subList(a, collegeTrainEngineeringExamRecordExcels.size()).clear();
        }
        if (ObjectUtil.isNotEmpty(collegeTrainEngineeringExamRecordExcels)) {
            for (int i = 0; i < collegeTrainEngineeringExamRecordExcels.size(); i++) {
                CollegeTrainEngineeringExamRecordExcel collegeTrainEngineeringExamRecordExcel = collegeTrainEngineeringExamRecordExcels.get(i);
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainEngineeringExamRecordExcel.getEmployeeNo(), "第" + (i + 2) + "行学员工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainEngineeringExamRecordExcel.getSubjectiveScore(), "第" + (i + 2) + "行简答题得分不能为空!");
                //获取大学生信息
                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(collegeTrainEngineeringExamRecordExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(collegeTrainAssessed)) {
                    return ResultVO.error("第" + (i + 2) + "行数据未找到相应人员");
                }
                //根据考试名称和学员工号共同进行数据匹配
                CollegeTrainExamRecord collegeTrainExamRecord = getByCondition(new CollegeTrainExamRecordExcel(),collegeTrainAssessed.getId(),"工程结业理论");
                if (ObjectUtil.isEmpty(collegeTrainExamRecord)) {
                    return ResultVO.error("第" + (i + 2) + "行考试数据不存在");
                }
           /*     //校验是否被批阅
                if (ObjectUtil.isNotEmpty(collegeTrainExamRecord.getSubjectiveScore())){
                    return ResultVO.error("第" + (i + 2) + "行数据已被批阅，不可再次导入");
                }*/
                if (!CheckNumberUtils.isNumber(collegeTrainEngineeringExamRecordExcel.getSubjectiveScore())){
                    return ResultVO.error("第" + (i + 2) + "行得分数据不是整数");
                }
                //封装参数进行更新
                CollegeTrainExamRecord newCollegeTrainExamRecord = new CollegeTrainExamRecord();
                newCollegeTrainExamRecord.setId(collegeTrainExamRecord.getId());
                newCollegeTrainExamRecord.setSubjectiveScore(new BigDecimal(collegeTrainEngineeringExamRecordExcel.getSubjectiveScore()));
                newCollegeTrainExamRecord.setTotalScore(String.valueOf(Integer.valueOf(collegeTrainExamRecord.getObjectiveScore())+Integer.valueOf(collegeTrainEngineeringExamRecordExcel.getSubjectiveScore())));
                list.add(newCollegeTrainExamRecord);
            }
        }
        updateBatchById(list);
        return ResultVO.success("成功导入" + list.size() + "数据");
    }

    @Override
    public ResultVO getSpecifiedColumn(String column) {
        List<String> list = this.baseMapper.getSpecifiedColumn(column);
        return ResultVO.success(list);
    }

    @Override
    public long getCountByAssessedId(String collegeTrainAssessedId) {
        LambdaQueryWrapper<CollegeTrainExamRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(CollegeTrainExamRecord::getAssessedId,collegeTrainAssessedId)
                .eq(CollegeTrainExamRecord::getDeletedFlag,Boolean.FALSE);
        return count(lambdaQuery);
    }

    @Override
    public ResultVO delete(String collegeTrainAssessedId) {
        long count = getCountByAssessedId(collegeTrainAssessedId);
        int a =uploadWorkLicense.getSumOne(collegeTrainAssessedId);
        int b =uploadWorkLicense.getSumTwo(collegeTrainAssessedId);
        int c =uploadWorkLicense.getSumThree(collegeTrainAssessedId);
        int d =uploadWorkLicense.getSumFour(collegeTrainAssessedId);
        if ((a+b+c+d)>0||count > 0){
            return ResultVO.error("该学员已有大学生培训相关数据上传,不可删除");

        }
        collegeTrainAssessedService.removeById(collegeTrainAssessedId);
        return ResultVO.success();
    }

    @Override
    public ResultVO getEmployeeInfo(String employeeNo) {
        removeById.getEmployeeInfo(employeeNo);
        return ResultVO.success(removeById.getEmployeeInfo(employeeNo));
    }

    @Override
    public void downloadSubjectiveExcel(HttpServletResponse response) throws IOException {

        String fileName = "主观题评分模板.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));


        // 创建新的Excel 工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 在Excel工作簿中建一工作表，其名为缺省值

        XSSFSheet sheet = workbook.createSheet();
        sheet.setColumnWidth(0, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(1, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(2, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(3, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.setColumnWidth(4, 5000); //第一个参数代表列id(从0开始),第2个参数代表宽度值
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));

        XSSFCellStyle cellStyle2 = workbook.createCellStyle();

        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        XSSFFont font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle2.setFont(font);

        XSSFRow row = sheet.createRow((short)0);
        row.setHeight((short) 1400);
        //在索引0的位置创建单元格（左上端）
        XSSFCell cell = row.createCell((short)0);
        // 创建样式 表头样式
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        cellStyle.setWrapText(true);
        XSSFDataFormat format = workbook.createDataFormat();
        cellStyle .setDataFormat(format.getFormat("@"));
        sheet.setDefaultColumnStyle(0, cellStyle );
        sheet.setDefaultColumnStyle(1, cellStyle );
        // 在单元格中输入一些内容
        cell.setCellValue("导入模板文字说明：\r\n" +
                "           1.\t红色字体为必填字段；\r\n" +
                "           2.\t学员工号必须为人事系统中人员信息中已有的在职数据，如不是则提示导入失败；\n" +
                "           3.\t考试科目：必须为工艺公开课理论考试或工程结业理论考试，否则导入失败；\n" +
                "           4.\t简答题得分必须为整数；");
        cell.setCellStyle(cellStyle);
        XSSFCellStyle cellStyle3 = workbook.createCellStyle();
        cellStyle3.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        cellStyle3.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        cellStyle3.setWrapText(true);
        row = sheet.createRow(1);
        cell = row.createCell(0);
        cell.setCellValue("序号");
        cell.setCellStyle(cellStyle3);
        cell =  row.createCell(1);
        cell.setCellValue("学员工号");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(2);
        cell.setCellValue("学员姓名");
        cell.setCellStyle(cellStyle3);
        cell =  row.createCell(3);
        cell.setCellValue("考试科目");
        cell.setCellStyle(cellStyle2);
        cell =  row.createCell(4);
        cell.setCellValue("简答题得分");
        cell.setCellStyle(cellStyle2);

        workbook.write(response.getOutputStream());
        response.flushBuffer();
    }

    @Override
    public String processExcelFile(MultipartFile file) throws IOException {
        InputStream inputStream = HttpUtils.postDecryptFile("http://***********:9999/oa.action.web/getOaData/decryptFile", file);
        if (inputStream == null ) {
            return "文件解析失败!";
        }

        List<CollegeSubjectiveExcelDTO> collegeTrainAssessedExcelList = null;
        try {
            collegeTrainAssessedExcelList = EasyExcelUtils.readExcel3(inputStream, CollegeSubjectiveExcelDTO.class);
        } catch (IOException e) {
            e.printStackTrace();
            return "文件解析错误";
        }

        for(CollegeSubjectiveExcelDTO dto : collegeTrainAssessedExcelList){
            String employeeNo = dto.getEmployeeNo();
            String employeeName = dto.getEmployeeName();
            String number = dto.getNumber();
            String examClass = dto.getExamClass();
            if(StringUtils.isEmpty(employeeNo) || StringUtils.isEmpty(examClass)){
                return "红色字体为必填字段";
            }
            if(collegeTrainSopUploadMapper.queryStudent(employeeNo) < 1){
                return "导入文件工号数据与系统数据不匹配，请检查后再次导入";
            }if (dto.getScore()==null){
                return "导入文件分数不能为空";
            }
            if (!CheckNumberUtils.isNumber(dto.getScore())){
                return "导入文件分数不能为小数";
            }
            if(!"工艺公开课理论考试".equals(examClass) && !"工程结业理论考试".equals(examClass)){
                return "导入文件课程名数据与系统数据不匹配，请检查后再次导入";
            }
        }

        for(CollegeSubjectiveExcelDTO dto: collegeTrainAssessedExcelList){
            String id;
            if (dto.getExamClass().contains("工程结业")){
                id="9";
            }else {
                id="4";
            }
            String score1=examRepository.getScore(dto.getEmployeeNo(),id);
            Double score2=Double.valueOf(score1)+Double.valueOf(dto.getScore());
            examRepository.updateScore(dto.getScore(),String.valueOf(score2),dto.getEmployeeNo(),id);
            if(collegeTrainSopUploadMapper.queryCount(dto) > 0){

                collegeTrainSopUploadMapper.updateSubjectiveScore(dto);
            }else {
                collegeTrainSopUploadMapper.addSubjectiveScore(dto);
            }
        }
        return "文件上传成功";
    }

    @Override
    public void exportSubjectiveExcel(HttpServletResponse response) {
        CollegeTrainExamRecordRequest request = new CollegeTrainExamRecordRequest();
        request.setPageNum(1L);
        request.setPageSize(1000000L);
        Page<CollegeTrainExamRecordVo> page = PageUtils.getPage(request);

        JsonPagedVO<List<CollegeTrainExamRecordVo>> collegeTrainExamRecordPage = getEngineeringPageList (request);
        List<CollegeTrainExamRecordVo> collegeTrainExamRecordVos = collegeTrainExamRecordPage.getData();
        List<SubjectiveExcel> excelList = new ArrayList<>();
        int index = 1;
        for (CollegeTrainExamRecordVo vo:collegeTrainExamRecordVos) {
            SubjectiveExcel excel = new SubjectiveExcel();
            BeanUtils.copyProperties(vo,excel);
            excel.setIndex((index++) + "");
            excel.setExamDatetime(vo.getExamStartTime() + " ~ " + vo.getExamEndTime());
            excelList.add(excel);
        }
//        collegeTrainExamRecordVos.add(new CollegeTrainExamRecordVo());
        exportExcel(response, excelList, "主观评分管理", SubjectiveExcel.class);
    }



    private CollegeTrainExamRecord getByCondition(CollegeTrainExamRecordExcel collegeTrainExamRecordExcel,Long assessedId,String examName) {
        LambdaQueryWrapper<CollegeTrainExamRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(ObjectUtil.isNotEmpty(collegeTrainExamRecordExcel.getExamNumber()),CollegeTrainExamRecord::getExamNumber,collegeTrainExamRecordExcel.getExamNumber())
                .eq(CollegeTrainExamRecord::getDeletedFlag,Boolean.FALSE)
                .eq(ObjectUtil.isNotEmpty(collegeTrainExamRecordExcel.getExamName()),CollegeTrainExamRecord::getExamName,collegeTrainExamRecordExcel.getExamName())
                .like(ObjectUtil.isNotEmpty(examName),CollegeTrainExamRecord::getExamName,examName)
                .eq(ObjectUtil.isNotEmpty(assessedId),CollegeTrainExamRecord::getAssessedId,assessedId);
        return getOne(lambdaQuery);
    }

    private CollegeTrainExamRecord getByConditionTwo(CollegeTrainExamRecordExcelTwo collegeTrainExamRecordExcel,Long assessedId,String examName) {
        LambdaQueryWrapper<CollegeTrainExamRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(ObjectUtil.isNotEmpty(collegeTrainExamRecordExcel.getExamNumber()),CollegeTrainExamRecord::getExamNumber,collegeTrainExamRecordExcel.getExamNumber())
                .eq(CollegeTrainExamRecord::getDeletedFlag,Boolean.FALSE)
                .eq(ObjectUtil.isNotEmpty(collegeTrainExamRecordExcel.getExamName()),CollegeTrainExamRecord::getExamName,collegeTrainExamRecordExcel.getExamName())
                .like(ObjectUtil.isNotEmpty(examName),CollegeTrainExamRecord::getExamName,examName)
                .eq(ObjectUtil.isNotEmpty(assessedId),CollegeTrainExamRecord::getAssessedId,assessedId);
        return getOne(lambdaQuery);
    }
    public class CustomSheetWriteHandler implements SheetWriteHandler {
        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            Row row = sheet.getRow(20);  // 获取第十行
            if (row == null) {
                row = sheet.createRow(20);
            }
            Cell cell = row.createCell(0);  // 获取第一列
            cell.setCellValue(
                    "导入文档说明:\n" +
                    "(1)红色为必填项:\n" +
                    "(2)工号和师傅工号必须为人事系统中人员信息中已有的数据，如不是则提示导入失败;\n" +
                    "(3)简答题得分必须为整数。");  // 填入值
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                if (mergedRegion.getFirstRow() == 20 && mergedRegion.getFirstColumn() == 0 && mergedRegion.getLastColumn() == 6) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 合并第十行的第1到第5列
            sheet.addMergedRegion(new CellRangeAddress(
                    20, // 第十行
                    26, // 第十行
                    0, // 第一列
                    6  // 第五列
            ));
        }
    }

    public class CustomSheetWriteHandlerTwo implements SheetWriteHandler {
        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            Row row = sheet.getRow(20);  // 获取第十行
            if (row == null) {
                row = sheet.createRow(20);
            }
            Cell cell = row.createCell(0);  // 获取第一列
            cell.setCellValue(
                    "导入文档说明:\n" +
                    "(1)红色为必填项:\n" +
                    "(2)学员工号和阅卷师工号必须为人事系统中人员信息中已有的在职的数据，如不是则提示导入失败;\n");  // 填入值
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                if (mergedRegion.getFirstRow() == 20 && mergedRegion.getFirstColumn() == 0 && mergedRegion.getLastColumn() == 6) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 合并第十行的第1到第5列
            sheet.addMergedRegion(new CellRangeAddress(
                    20, // 第十行
                    26, // 第十行
                    0, // 第一列
                    6  // 第五列
            ));
        }
    }
}
