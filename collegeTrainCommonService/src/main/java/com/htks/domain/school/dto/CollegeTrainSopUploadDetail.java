package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 SOP评价得分明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_SOP_UPLOAD_DETAIL")
@ApiModel(value = "CollegeTrainSopUploadDetail对象", description = "大学生培训系统 SOP评价得分明细")
@KeySequence(value = "COLLEGE_TRAIN_SOP_UPLOAD_DETAIL_SEQ",dbType = DbType.SAP_HANA)
public class CollegeTrainSopUploadDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("内容完整")
    @TableField("CONTENT_COMPLETE")
    private Integer contentComplete;

    @ApiModelProperty("内容阐述详细")
    @TableField("CONTENT_DETAILED")
    private Integer contentDetailed;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty("图文并茂易于理解")
    @TableField("EASY_UNDERSTAND")
    private String easyUnderstand;

    @ApiModelProperty("编写思路逻辑得分")
    @TableField("LOGIC")
    private Integer logic;

    @ApiModelProperty("sop格式得分")
    @TableField("SOP_FORMAT")
    private Integer sopFormat;

    @ApiModelProperty("COLLEGE_TRAIN_SOP_UPLOAD.ID")
    @TableField("SOP_UPLOAD_ID")
    private Long sopUploadId;

    @TableField("UPDATED_TIME")
    private Date updatedTime;

    @TableField("UPDATED_USER")
    private String updatedUser;


}
