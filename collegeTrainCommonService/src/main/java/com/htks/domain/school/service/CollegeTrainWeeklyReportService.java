package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainWeeklyReport;
import com.htks.domain.school.request.CollegeTrainSubjectiveJudgeRequest;
import com.htks.domain.school.request.CollegeTrainWeeklyReportRequest;
import com.htks.web.JsonPagedVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 大学生培训系统 周报 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainWeeklyReportService extends IService<CollegeTrainWeeklyReport> {

    void downTemplate(HttpServletResponse response) throws IOException;

    void downloadTemplate(HttpServletResponse response) throws IOException;
    ResultVO importTemplate(MultipartFile file);

    JsonPagedVO getPageList(CollegeTrainWeeklyReportRequest request);
}
