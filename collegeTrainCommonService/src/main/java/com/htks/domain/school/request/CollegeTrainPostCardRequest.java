package com.htks.domain.school.request;

import com.htks.common.convert.Search;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/12 13:41
 */
@Data
public class CollegeTrainPostCardRequest extends Search {

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;
    private String factoryFlag;
    @ApiModelProperty("实习部门名称")
    private List<String> practiceDepartmentNameList;

    @ApiModelProperty("如果选了区域,直接给对应配置主键")
    private String practiceDepartmentAreaId;
}
