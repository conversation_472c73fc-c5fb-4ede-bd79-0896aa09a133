package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 大学生 考试记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeTrainEngineeringExamRecordExcel extends Convert {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "序号",index = 0)
    private String number;

    @ApiModelProperty("学员工号")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "学员工号",index = 1)
    private String employeeNo;

    @HeadFontStyle(color = 0)
    @ApiModelProperty("学员姓名")
    @ExcelProperty(value = "学员姓名",index = 2)
    private String employeeName;

    @ApiModelProperty("简答题得分")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "简答题得分",index = 3)
    private String subjectiveScore;

}
