package com.htks.domain.school.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.common.convert.Search;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * sop
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainSopUploadRequest extends Search {

    private Long id;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private List<String> practiceDepartmentNameList;

    @ApiModelProperty("如果选了区域,直接给对应配置主键")
    private String practiceDepartmentAreaId;

    @ApiModelProperty("评价状态 已评价：未评价")
    private String evaluateStatus;

    @ApiModelProperty("SOP名称")
    private String sopName;

    @ApiModelProperty("1:sop上传 2：读后感上传")
    private Integer type;

    @ApiModelProperty("阅卷师姓名")
    private String graderName;

    @ApiModelProperty("阅卷师工号")
    private String graderNo;

    @ApiModelProperty("批阅编号")
    private String gradingNumber;
    @ApiModelProperty("厂别")
    private List<String> factoryType;
}
