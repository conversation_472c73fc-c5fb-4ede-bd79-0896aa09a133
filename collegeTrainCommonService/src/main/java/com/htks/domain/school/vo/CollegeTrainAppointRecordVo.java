package com.htks.domain.school.vo;

import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 大学生 预约记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainAppointRecordVo extends Convert {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("预约开始时间")
    private String appointStartDate;
    @ApiModelProperty("预约开始时间")
    private String appointEndDate;
    @ApiModelProperty("预约开始时间")
    private String appointStartTime;

    @ApiModelProperty("预约结束时间")
    private String appointEndTime;

    @ApiModelProperty("是否通过")
    private Boolean passed;

    @ApiModelProperty("得分(分)")
    private BigDecimal score;

    @ApiModelProperty("考试通过时间")
    private String passedDate;

    @ApiModelProperty("附加分(分)")
    private BigDecimal additionalPoints;

    @ApiModelProperty("考试状态 已考核：未考核")
    private String status;

    @ApiModelProperty("评价类型 1：工程类实操 2：异常答辩")
    private Integer type;

    @ApiModelProperty("预约状态 1：已预约 2：退回")
    private String appointStatus;



}
