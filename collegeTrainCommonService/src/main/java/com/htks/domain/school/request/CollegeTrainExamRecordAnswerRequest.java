package com.htks.domain.school.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 岗位认证系统 考试记录答案表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainExamRecordAnswerRequest extends Convert {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("COLLEGE_TRAIN_EXAM_RECORD_ANSWER.ID")
    private Long id;

    @ApiModelProperty("得分")
    private BigDecimal score;

    @ApiModelProperty("说明")
    private String note;
}
