package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.domain.school.dto.CollegeTrainUser;

import java.util.List;

/**
 * <p>
 * 岗位认证系统 管理员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainUserService extends IService<CollegeTrainUser> {

    /**
     * 检查存在
     *
     * @param loginName 登录名
     * @return boolean
     */
    boolean checkExist(String loginName);

    /**
     * 通过员工工号获取信息
     *
     * @param loginName 登录名
     * @return {@link CollegeTrainUser}
     */
    CollegeTrainUser getByEmployeeNo(String loginName);

    /**
     * 得到指定角色的用户信息
     *
     * @return {@link List}<{@link CollegeTrainUser}>
     */
    List<CollegeTrainUser> getAllJudge(String userRoleId);

    String handleFile(String filePath);
}
