package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.request.CollegeTrainEnabledRequest;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.request.StartReviewingRequest;
import com.htks.domain.school.vo.CollegeTrainAssessedVo;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.web.JsonPagedVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 大学生 考试记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainExamRecordService extends IService<CollegeTrainExamRecord> {

    JsonPagedVO<List<CollegeTrainExamRecordVo>> queryPaperList(CollegeTrainExamRecordRequest request);

    void downloadTemplate(HttpServletResponse response) throws IOException;

    void export(CollegeTrainExamRecordRequest request,HttpServletResponse response);

    void assignExaminer(CollegeTrainExamRecordRequest request);

    ResultVO importTemplate(MultipartFile file) throws IOException;

    JsonPagedVO<CollegeTrainExamRecordVo> getForApprovalPageList(CollegeTrainExamRecordRequest request);

    ResultVO startReviewing(StartReviewingRequest request);

    JsonPagedVO<CollegeTrainExamRecordVo> getApprovedPageList(CollegeTrainExamRecordRequest request);

    JsonPagedVO<List<CollegeTrainExamRecordVo>> getEngineeringPageList(CollegeTrainExamRecordRequest request);

    void downloadEngineeringTemplate(HttpServletResponse response) throws IOException;

    ResultVO importEngineeringTemplate(MultipartFile file) throws IOException;

    ResultVO getSpecifiedColumn(String column);

    long getCountByAssessedId(String collegeTrainAssessedId);

    ResultVO delete(String collegeTrainAssessedId);

    ResultVO getEmployeeInfo(String employeeNo);

    void downloadSubjectiveExcel(HttpServletResponse response) throws IOException;

    void exportSubjectiveExcel(HttpServletResponse response) throws IOException;

    String processExcelFile(MultipartFile file) throws IOException;



}
