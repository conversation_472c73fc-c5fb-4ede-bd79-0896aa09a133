package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.sys.dto.SysEmployeeEntity;

import java.util.List;

/**
 * <p>
 * 员工表(每日同步) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface CommonEmployeeService extends IService<CommonEmployee> {

    CommonEmployee getEmployeeInfo(String employeeNumber);

    List<CommonEmployee> getEmployeeListByDepartmentName(String departmentName);

    CommonEmployee getEmployeeInfo(String employeeNumber, String employeeStatus);
}
