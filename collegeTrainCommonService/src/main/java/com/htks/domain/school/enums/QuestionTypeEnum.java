package com.htks.domain.school.enums;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 试题类型枚举
 *
 * <AUTHOR>
 * @date 2023/05/09
 */
@Getter
@RequiredArgsConstructor
public enum QuestionTypeEnum {
    SINGLE_OPTION("1", "单选题"),
    MORE_OPTION("2", "多选题"),
    JUDGE("3", "判断题"),
    FILL("4", "填空题"),
    SHORT("5", "简答题");


    private final String code;
    private final String info;

    public static QuestionTypeEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (QuestionTypeEnum e : QuestionTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }

}
