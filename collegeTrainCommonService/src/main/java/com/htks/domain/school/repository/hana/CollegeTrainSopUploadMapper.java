package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.htks.domain.school.dto.CollegeSOPCondition;
import com.htks.domain.school.dto.CollegeSOPExcelDTO;
import com.htks.domain.school.dto.CollegeSubjectiveExcelDTO;
import com.htks.domain.school.dto.CollegeTrainSopUpload;
import com.htks.domain.school.excel.CollegeTrainSopUploadExportExcel;
import com.htks.domain.school.request.CollegeTrainSopUploadRequest;
import com.htks.domain.school.vo.CollegeSopDetailVO;
import com.htks.domain.school.vo.CollegeTrainSopUploadVo;
import com.htks.domain.school.vo.SopGraderVo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * 大学生培训系统 SOP上传/读后感上传 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainSopUploadMapper extends BaseMapper<CollegeTrainSopUpload> {

    @Select("<script>" +
            "SELECT ctsu.ID, cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ,ctpa.AREA ,\n" +
            "ctsu.SOP_NAME ,ctsu.EVALUATE_REASON, a.ATTACHMENT_PATH ,ctsu.EVALUATE_STATUS ,ctsu.SCORE ,ctsu.GRADER_NO ,ctsu.GRADER_NAME ,ctsu.GRADING_NUMBER,a.ATTACHMENT_NAME as fileName \n" +
            "FROM COLLEGE_TRAIN_SOP_UPLOAD ctsu \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.ID = CTSU .ASSESSED_ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID = ctpa.ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_ATTACHMENT a ON ctsu.ATTACHMENT_ID = a.ID \n" +
            "WHERE ctsu.DELETED_FLAG =FALSE AND cta.DELETED_FLAG =FALSE " +
            "and ctsu.Type = #{request.type}"+
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "<if test=\"request.sopName !=null and request.sopName !=''\">  " +
            "    and ctsu.SOP_NAME = #{request.sopName}  " +
            "</if>  " +
            "<if test=\"request.evaluateStatus !=null and request.evaluateStatus !=''\">  " +
            "    and ctsu.EVALUATE_STATUS = #{request.evaluateStatus}  " +
            "</if>  " +
            "<if test=\"request.gradingNumber !=null and request.gradingNumber !=''\">  " +
            "    and ctsu.GRADING_NUMBER = #{request.gradingNumber}  " +
            "</if>  " +
            "<if test=\"request.factoryType !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and cta.FACTORY_FLAG in    " +
            "<foreach collection=\"request.factoryType\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "</script>")
    IPage<CollegeTrainSopUploadVo> queryPaperList(@Param("page") Page<CollegeTrainSopUploadVo> page, @Param("request") CollegeTrainSopUploadRequest request);

    @Select("<script>" +
            "SELECT cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT ,ctpa.AREA ,\n" +
            "ctsu.SOP_NAME ,ctsu.ATTACHMENT_ID ,ctsu.EVALUATE_STATUS ,ctsu.SCORE ,ctsu.GRADER_NO ,ctsu.GRADER_NAME ,ctsu.GRADING_NUMBER \n" +
            "FROM COLLEGE_TRAIN_SOP_UPLOAD ctsu \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.ID = CTSU .ASSESSED_ID \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID = ctpa.ID \n" +
            "WHERE ctsu.DELETED_FLAG =FALSE AND cta.DELETED_FLAG =FALSE " +
            "and ctsu.Type = #{request.type}"+
            "</script>")
    List<CollegeTrainSopUploadExportExcel> getList(@Param("request") CollegeTrainSopUploadRequest request);

    @Select("select distinct ${column} from COLLEGE_TRAIN_SOP_UPLOAD where DELETED_FLAG  = FALSE")
    List<String> getSpecifiedColumn(@Param("column") String column);

    @Select("SELECT DISTINCT ce.EMPLOYEE_NAME ,ce.EMPLOYEE_NUMBER FROM COLLEGE_TRAIN_POST_AREA ctpa \n" +
            "LEFT JOIN COMMON_EMPLOYEE ce ON ctpa.QUALITY_LEADER = ce.EMPLOYEE_NUMBER ")
    List<SopGraderVo> getSopGrader();

    @Insert({"<script> insert into COLLEGE_TRAIN_SCORE(EMPLOYEE_NUMBER,SOP_NAME,QA_SCORE,EN_SCORE) values " +
            "<foreach collection='list' item='collegeSOPExcelDTO' separator=',' >" +
            "     (#{collegeSOPExcelDTO.number},#{collegeSOPExcelDTO.sopName},#{collegeSOPExcelDTO.qaScore},#{collegeSOPExcelDTO.egScore})" +
            "</foreach> </script>"})
    int addSopScore(List<CollegeSOPExcelDTO> list);

    @Insert({"insert into COLLEGE_TRAIN_SCORE(EMPLOYEE_NUMBER,SOP_NAME,QA_SCORE,EN_SCORE) values " +
            " (#{number},#{sopName},#{qaScore},#{egScore})"})
    int addSopScoreOne(CollegeSOPExcelDTO sopExcelDTO);

    @Select("select distinct(SOP_NAME) from COLLEGE_TRAIN_SCORE ")
    List<String> querySopNames();

    @Insert({"insert into COLLEGE_TRAIN_SUBJECTIVE_SCORE (EMPLOYEE_NUMBER,EMPLOYEE_NAME,EXAM_CLASS,SCORE) values " +
            " (#{employeeNo},#{employeeName},#{examClass},#{score})"})
    int addSubjectiveScore(CollegeSubjectiveExcelDTO excelDTO);

    @Update({"update COLLEGE_TRAIN_SUBJECTIVE_SCORE set EMPLOYEE_NAME = #{employeeName},SCORE = #{score} " +
            " where EMPLOYEE_NUMBER = #{employeeNo} and EXAM_CLASS = #{examClass}"})
    int updateSubjectiveScore(CollegeSubjectiveExcelDTO excelDTO);

    @Select({"select count(1) from COLLEGE_TRAIN_SUBJECTIVE_SCORE where EMPLOYEE_NUMBER = #{employeeNo} and EXAM_CLASS = #{examClass}"})
    int queryCount(CollegeSubjectiveExcelDTO excelDTO);


    @Select("<script>SELECT a.FACTORY_FLAG as companyName,a.BATCH ,a.EMPLOYEE_NAME ,a.EMPLOYEE_NO ,area.PRACTICE_DEPARTMENT,area.AREA,s.SOP_NAME ,s.QA_SCORE ,s.EN_SCORE " +
            "FROM COMMON_EMPLOYEE ce " +
            "JOIN COLLEGE_TRAIN_SCORE s ON s.EMPLOYEE_NUMBER = ce.EMPLOYEE_NUMBER " +
            "JOIN COLLEGE_TRAIN_ASSESSED a ON a.EMPLOYEE_NO = s.EMPLOYEE_NUMBER " +
            "JOIN COLLEGE_TRAIN_POST_AREA area ON area.ID = a.PRACTICE_DEPARTMENT_AREA_ID " +
            "<where>" +
            "<if test=\"factoryType != null and factoryType.size > 0 \" > and a.FACTORY_FLAG in " +
            "<foreach collection='factoryType' item='factory' separator=',' open='(' close=')'>" +
            "     (#{factory})" +
            "</foreach> "+
            "</if>"+
            "<if test=\"factoryType.size = 0 \" > and ce.COMPANY_NAME in ('华天江苏','华天昆山')" +
            "</if>"+
            "<if test=\"batch != null and batch != \'\'\" > and a.BATCH like CONCAT(CONCAT('%',#{batch}),'%') </if>"+
            "<if test=\"employeeNo != null and employeeNo != \'\'\" > and ce.EMPLOYEE_NUMBER = #{employeeNo}</if>"+
            "<if test=\"employeeName != null and employeeName != \'\'\" > and a.EMPLOYEE_NAME = #{employeeName}</if>"+
            "<if test=\"department != null and department.size > 0 \" > and area.PRACTICE_DEPARTMENT in " +
            "<foreach collection='department' item='department' separator=',' open='(' close=')'>" +
            "     (#{department})" +
            "</foreach> "+
            "</if>"+
            "<if test=\"area != null and area != \'\'\" > and area.ID = #{area}</if>"+
            "<if test=\"sopName != null and sopName != \'\'\" > and s.SOP_NAME like CONCAT(CONCAT('%',#{sopName}),'%')</if>"+
            "</where>" +
            "<if test=\"limit != 0 and offset != 0\"> limit #{limit} offset #{offset} </if>"+
            " </script>")
    List<CollegeSopDetailVO> querySopDetail(CollegeSOPCondition condition);

    @Select("<script>SELECT ce.COMPANY_NAME,a.BATCH " +
            "FROM COMMON_EMPLOYEE ce " +
            "JOIN COLLEGE_TRAIN_SCORE s ON s.EMPLOYEE_NUMBER = ce.EMPLOYEE_NUMBER " +
            "JOIN COLLEGE_TRAIN_ASSESSED a ON a.EMPLOYEE_NO = s.EMPLOYEE_NUMBER " +
            "JOIN COLLEGE_TRAIN_POST_AREA area ON area.ID = a.PRACTICE_DEPARTMENT_AREA_ID " +
            "<where>" +
            "<if test=\"factoryType.size > 0 \" > and  a.FACTORY_FLAG in " +
            "<foreach collection='factoryType' item='factory' separator=',' open='('  close=')' > " +
            "     (#{factory})" +
            "</foreach> "+
            "</if>"+
            "<if test=\"factoryType.size = 0 \" > and a.FACTORY_FLAG  in ('华天江苏','华天昆山')" +
            "</if>"+
            "<if test=\"batch != null and batch != \'\'\" > and a.BATCH like CONCAT(CONCAT('%',#{batch}),'%')</if>"+
            "<if test=\"employeeNo != null and employeeNo != \'\'\" > and ce.EMPLOYEE_NUMBER = #{employeeNo}</if>"+
            "<if test=\"employeeName != null and employeeName != \'\'\" > and a.EMPLOYEE_NAME = #{employeeName}</if>"+
            "<if test=\"department != null and department.size > 0 \" > and area.PRACTICE_DEPARTMENT in " +
            "<foreach collection='department' item='department' separator=',' open='(' close=')'>" +
            "     (#{department})" +
            "</foreach> "+
            "</if>"+
            "<if test=\"area != null and area != \'\'\" > and area.AREA = #{area}</if>"+
            "<if test=\"sopName != null and sopName != \'\'\" > and s.SOP_NAME like CONCAT(CONCAT('%',#{sopName}),'%')</if>"+
            "</where>" +
            " </script>")
    List<CollegeSopDetailVO> querySopDetailCount(CollegeSOPCondition condition);

    @Select("select count(1) from COLLEGE_TRAIN_ASSESSED where EMPLOYEE_NO = #{employeeNo}")
    int queryStudent(@Param("employeeNo") String employeeNo);
}
