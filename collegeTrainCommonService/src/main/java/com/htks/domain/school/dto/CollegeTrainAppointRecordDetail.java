package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 评价记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_APPOINT_RECORD_DETAIL")
@ApiModel(value = "CollegeTrainAppointRecordDetail对象", description = "大学生培训系统 评价记录")
public class CollegeTrainAppointRecordDetail  implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("作答记录")
    @TableField("ANSWER")
    private String answer;

    @ApiModelProperty("预约记录表COLLEGE_TRAIN_APPOINT_RECORD.ID")
    @TableField("APPOINT_RECORD_ID")
    private Long appointRecordId;

    @ApiModelProperty("评价人姓名")
    @TableField("EVALUATOR_NAME")
    private String evaluatorName;

    @ApiModelProperty("评价人工号")
    @TableField("EVALUATOR_NO")
    private String evaluatorNo;

    @ApiModelProperty("工程理论问题")
    @TableField("QUESTION")
    private String question;

    @ApiModelProperty("得分")
    @TableField("SCORE")
    private BigDecimal score;

    @ApiModelProperty("评价类型 1：工程类 2：异常答辩")
    @TableField("TYPE")
    private Integer type;

    @ApiModelProperty("评价人类型 1：工程类 2：异常答辩")
    @TableField("EVALUATOR_TYPE")
    private Integer evaluatorType;
}
