package com.htks.domain.school.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 分配状态枚举
 *
 * <AUTHOR>
 * @date 2023/05/09
 */
@Getter
@RequiredArgsConstructor
public enum AllocationStatusEnum {
    UNALLOCATED("未分配", "未分配"),
    ALLOCATED("已分配", "已分配");


    private final String code;
    private final String info;

    public static AllocationStatusEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (AllocationStatusEnum e : AllocationStatusEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }

}
