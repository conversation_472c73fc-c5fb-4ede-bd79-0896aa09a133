package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainMeet;
import com.htks.domain.school.request.CollegeTrainMeetRequest;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.domain.school.vo.CollegeTrainMeetVo;
import com.htks.web.JsonPagedVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * <p>
 * 大学生培训系统 见面会 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainMeetService extends IService<CollegeTrainMeet> {

    void downTemplate(HttpServletResponse response) throws IOException;

    ResultVO importTemplate(MultipartFile file);

    ResultVO getMeetDetail(Long meetId);

    JsonPagedVO<CollegeTrainMeetVo> queryPaperList(CollegeTrainMeetRequest request);
}
