package com.htks.domain.school.request;

import com.htks.common.convert.Convert;
import com.htks.domain.school.dto.CollegeTrainSopUploadDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/12 10:14
 */
@Data
public class SopEvaluateRequest extends Convert {

    @ApiModelProperty("sop评分主键")
    private Long sopUploadId;

    @ApiModelProperty("得分")
    private Integer score;

    @ApiModelProperty("评分理由")
    private String evaluateReason;

    @ApiModelProperty("sop评分详情")
    private CollegeTrainSopUploadDetail sopUploadDetail;

}
