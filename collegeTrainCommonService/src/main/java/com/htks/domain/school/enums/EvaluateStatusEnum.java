package com.htks.domain.school.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 评价状态枚举
 *
 * <AUTHOR>
 * @date 2023/05/09
 */
@Getter
@RequiredArgsConstructor
public enum EvaluateStatusEnum {
    NO_EVALUATED("未评价", "未评价"),
    EVALUATED("已评价", "已评价");


    private final String code;
    private final String info;

    public static EvaluateStatusEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (EvaluateStatusEnum e : EvaluateStatusEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }

}
