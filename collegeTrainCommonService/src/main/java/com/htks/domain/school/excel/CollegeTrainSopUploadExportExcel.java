package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 17:02
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeTrainSopUploadExportExcel {

    @ApiModelProperty("批次")
    @ExcelProperty(value = "批次",index = 0)
    private String batch;

    @ApiModelProperty("学员工号")
    @ExcelProperty(value = "学员工号",index = 1)
    private String employeeNo;

    @ApiModelProperty("学员姓名")
    @ExcelProperty(value = "学员姓名",index = 2)
    private String employeeName;

    @ApiModelProperty("实习部门")
    @ExcelProperty(value = "实习部门",index = 3)
    private String practiceDepartmentName;

    @ApiModelProperty("区域/制程")
    @ExcelProperty(value = "区域/制程",index = 4)
    private String area;

    @ApiModelProperty("SOP名称")
    @ExcelProperty(value = "SOP名称",index = 5)
    private String sopName;

    @ApiModelProperty("评价状态 已评价：未评价")
    @ExcelProperty(value = "评价状态",index = 6)
    private String evaluateStatus;

    @ApiModelProperty("得分")
    @ExcelProperty(value = "评价状态",index = 7)
    private Integer score;

    @ApiModelProperty("阅卷师")
    @ExcelProperty(value = "评价状态",index = 8)
    private String graderName;

    @ApiModelProperty("批阅编号")
    @ExcelProperty(value = "评价状态",index = 9)
    private String gradingNumber;

}
