package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 周报
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_WEEKLY_REPORT")
@ApiModel(value = "CollegeTrainWeeklyReport对象", description = "大学生培训系统 周报")
public class CollegeTrainWeeklyReport extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ASSESSED_ID")
    @TableField("ASSESSED_ID")
    private Long ID;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ASSESSED_ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @ApiModelProperty("评价周期结束日期")
    @TableField("EVALUATE_WEEKLY_END")
    private String evaluateWeeklyEnd;

    @ApiModelProperty("评价周期开始日期")
    @TableField("EVALUATE_WEEKLY_START")
    private String evaluateWeeklyStart;

    @ApiModelProperty("阅卷师姓名")
    @TableField("JUDGE_NAME")
    private String judgeName;

    @ApiModelProperty("阅卷师工号")
    @TableField("JUDGE_NO")
    private String judgeNo;

    @ApiModelProperty("得分")
    @TableField("SCORE")
    private String score;

    @ApiModelProperty("周别")
    @TableField("WEEKLY")
    private String weekly;


}
