package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/9 11:09
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
@Getter
@Setter
public class CollegeTrainAssessedExcel extends Convert {
    @ApiModelProperty("序号")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "序号",index = 0)
    private String number;

    @ApiModelProperty("厂别")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "厂别",index = 1)
    private String factoryFlag;

    @ApiModelProperty("批次")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "批次",index = 2)
    private String batch;

    @ApiModelProperty("班别")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "班别",index = 3)
    private String classNo;

    @ExcelProperty(value = "工号",index = 4)
    @HeadFontStyle(color = 10)
    private String employeeNo;

    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "姓名",index = 5)
    private String employeeName;


    @ExcelProperty(value = "选岗部门",index = 6)
    @HeadFontStyle(color = 0)
    private String chooseDepartmentName;

    @ExcelProperty(value = "实习部门",index = 7)
    @HeadFontStyle(color = 0)
    private String practiceDepartment;

    @ExcelProperty(value = "区域/制程",index = 8)
    @HeadFontStyle(color = 0)
    private String area;

    @ExcelProperty(value = "师傅工号",index = 9)
    @HeadFontStyle(color = 0)
    private String masterNo;

    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "师傅姓名",index = 10)
    private String masterName;

    @ExcelProperty(value = "进入BU时间",index = 11)
    @HeadFontStyle(color = 0)
    private String joinBuTime;

    @ExcelProperty(value = "上岗证获取开始时间",index = 12)
    @HeadFontStyle(color = 10)
    private String postCertificateStartTime;

    @ExcelProperty(value = "上岗证获取截止时间",index = 13)
    @HeadFontStyle(color = 10)
    private String postCertificateEndTime;

    @ExcelProperty(value = "工程实操考试开始时间",index = 14)
    @HeadFontStyle(color = 10)
    private String practicalOperationStartTime;

    @ExcelProperty(value = "工程实操考试截止时间",index = 15)
    @HeadFontStyle(color = 10)
    private String practicalOperationEndTime;

    @ExcelProperty(value = "异常报告答辩开始时间",index = 16)
    @HeadFontStyle(color = 10)
    private String exceptionDefenseStartTime;

    @ExcelProperty(value = "异常报告答辩截止时间",index = 17)
    @HeadFontStyle(color = 10)
    private String exceptionDefenseEndTime;

//    @ExcelProperty(value = "SOP截止上传时间",index = 15)
//    @HeadFontStyle(color = 10)
//    private String sopUploadDeadTime;

    @ExcelProperty(value = "读后感截止上传时间",index = 18)
    @HeadFontStyle(color = 10)
    private String afterReadingUploadDeadTime;

    @ExcelProperty(value = "状态",index = 19)
    @HeadFontStyle(color = 0)
    private String enabled;



}
