package com.htks.domain.school.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/16 9:43
 */
@Data
public class CollegeTrainSubjectiveJudgeVo {

    private Long id;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("师傅姓名")
    private String masterName;

    @ApiModelProperty("师傅工号")
    private String masterNo;

    @ApiModelProperty("得分")
    private Integer score;
}
