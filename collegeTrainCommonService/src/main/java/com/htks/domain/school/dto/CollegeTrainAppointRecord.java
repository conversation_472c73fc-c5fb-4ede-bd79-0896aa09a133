package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 预约记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_APPOINT_RECORD")
@ApiModel(value = "CollegeTrainAppointRecord对象", description = "大学生培训系统 预约记录")
public class CollegeTrainAppointRecord extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("附加分")
    @TableField("ADDITIONAL_POINTS")
    private BigDecimal additionalPoints;

    @ApiModelProperty("预约结束日期")
    @TableField("APPOINT_END_DATE")
    private String appointEndDate;

    @ApiModelProperty("预约结束时间")
    @TableField("APPOINT_END_TIME")
    private String appointEndTime;

    @ApiModelProperty("预约开始日期")
    @TableField("APPOINT_START_DATE")
    private String appointStartDate;

    @ApiModelProperty("预约开始时间")
    @TableField("APPOINT_START_TIME")
    private String appointStartTime;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @ApiModelProperty("是否通过")
    @TableField("PASSED")
    private Boolean passed;

    @ApiModelProperty("考试通过时间")
    @TableField("PASSED_DATE")
    private String passedDate;

    @ApiModelProperty("得分")
    @TableField("SCORE")
    private BigDecimal score;

    @ApiModelProperty("考试状态 已考核：未考核")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty("评价类型 1：工程类 2：异常答辩")
    @TableField("TYPE")
    private Integer type;



}
