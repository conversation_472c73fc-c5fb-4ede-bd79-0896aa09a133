package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.domain.school.dto.CollegeTrainDepartment;
import com.htks.domain.school.dto.CollegeTrainPostArea;
import com.htks.domain.school.repository.hana.CollegeTrainPostAreaMapper;
import com.htks.domain.school.service.CollegeTrainPostAreaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * <p>
 * 部门区域对应表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CollegeTrainPostAreaServiceImpl extends ServiceImpl<CollegeTrainPostAreaMapper, CollegeTrainPostArea> implements CollegeTrainPostAreaService {

    @Resource
    private CollegeTrainPostAreaMapper collegeTrainPostAreaMapper;

    @Override
    public List<String> getAllDepartment(String factoryFlag) {
        return collegeTrainPostAreaMapper.getAllDepartment(factoryFlag);
    }

    @Override
    public List<CollegeTrainDepartment> getCollegeTrainDepartment() {
        return collegeTrainPostAreaMapper.getCollegeTrainDepartment();
    }

    @Override
    public List<CollegeTrainPostArea> getAreaByDepartment(String department,String employeeNo) {
        notNull(department, "The department is required!");
        LambdaQueryWrapper<CollegeTrainPostArea> queryWrapper = Wrappers.lambdaQuery();
        String factoryFlag = collegeTrainPostAreaMapper.getFactoryFlag(employeeNo);
        queryWrapper.eq(CollegeTrainPostArea::getPracticeDepartment,department)
        .eq(CollegeTrainPostArea::getFactoryFlag,factoryFlag);
        return list(queryWrapper);
    }

    @Override
    public CollegeTrainPostArea getByDepartmentAndArea(String practiceDepartment, String area,String factoryFlag) {
        LambdaQueryWrapper<CollegeTrainPostArea> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(CollegeTrainPostArea::getPracticeDepartment,practiceDepartment)
                .eq(CollegeTrainPostArea::getArea,area)
                .eq(CollegeTrainPostArea::getFactoryFlag,factoryFlag);

        return getOne(queryWrapper);
    }

    @Override
    public List<CollegeTrainPostArea> getByDepartmentList(List<String> practiceDepartmentNameList) {
        LambdaQueryWrapper<CollegeTrainPostArea> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollegeTrainPostArea::getPracticeDepartment,practiceDepartmentNameList);
        return list(queryWrapper);
    }

    @Override
    public List<CollegeTrainPostArea> getByDepartmentListNew(List<String> practiceDepartmentNameList, List<String> factoryFlagList) {
        LambdaQueryWrapper<CollegeTrainPostArea> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollegeTrainPostArea::getPracticeDepartment,practiceDepartmentNameList)
        .in(CollegeTrainPostArea::getFactoryFlag,factoryFlagList);
        return list(queryWrapper);
    }

    @Override
    public List<CollegeTrainPostArea> getByAreaList(List<String> areaList) {
        LambdaQueryWrapper<CollegeTrainPostArea> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollegeTrainPostArea::getArea,areaList);
        return list(queryWrapper);
    }

    @Override
    public List<CollegeTrainPostArea> getByDepartmentAndAreaList(List<String> practiceDepartmentNameList, List<String> areaList) {
        LambdaQueryWrapper<CollegeTrainPostArea> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .in(CollegeTrainPostArea::getArea,areaList)
                .or()
                .in(CollegeTrainPostArea::getPracticeDepartment,practiceDepartmentNameList);
        return list(queryWrapper);
    }
}
