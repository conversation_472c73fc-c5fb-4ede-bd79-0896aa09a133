package com.htks.domain.school.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/12 14:57
 */
@Data
public class CollegeTrainAssessedAreaVo {

    private Long id;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("上岗证获取截止时间")
    private String postCertificateEndTime;

    @ApiModelProperty("上岗证获取开始时间")
    private String postCertificateStartTime;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;
}
