package com.htks.domain.school.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.domain.AbstractPaginationQueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 大学生 考试记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainExamRecordVo {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("考试编号")
    private String examNumber;

    @ApiModelProperty("考试名称")
    private String examName;

    @ApiModelProperty("考试科目")
    private String examClass;

    @ApiModelProperty("考试开始时间")
    private String examStartTime;

    @ApiModelProperty("考试结束时间")
    private String examEndTime;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("阅卷师工号")
    private String judgeNo;

    @TableField("阅卷师名称")
    private String judgeName;

    @ApiModelProperty("阅卷编号")
    private String judgeNumber;

    @ApiModelProperty("分配状态 已分配：未分配")
    private String allocationStatus;

    @ApiModelProperty("试卷名称")
    private String paperName;

    @ApiModelProperty("试卷名称")
    private String paperType;

    @ApiModelProperty("考试类型")
    private String examType;

    @ApiModelProperty("客观题得分")
    private String objectiveScore;

    @ApiModelProperty("主观题得分")
    private String subjectiveScore;

    @ApiModelProperty("总分")
    private String totalScore;

    @ApiModelProperty("评分理由")
    private String evaluateReason;

    @ApiModelProperty("批阅状态 已批阅：未批阅")
    private String status;

}
