package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 大学生各部门岗位上岗证明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_POST_INFO")
@ApiModel(value = "CollegeTrainPostInfo对象", description = "大学生培训系统 大学生各部门岗位上岗证明细表")
public class CollegeTrainPostInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("区域/制程")
    @TableField("AREA")
    private String area;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty("上岗证编号及名称")
    @TableField("POST_NO")
    private String postNo;

    @ApiModelProperty("实习部门")
    @TableField("PRACTICE_DEPARTMENT")
    private String practiceDepartment;

    @TableField("UPDATED_TIME")
    private Date updatedTime;

    @TableField("UPDATED_USER")
    private String updatedUser;


}
