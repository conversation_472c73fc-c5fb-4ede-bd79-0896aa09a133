package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/15 13:39
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeTrainMeetExcel {

    @ApiModelProperty("序号")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "序号",index = 0)
    private String no;

    @ApiModelProperty("学员工号")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "学员工号",index = 1)
    private String employeeNo;

    @ApiModelProperty("学员姓名")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "学员姓名",index = 2)
    private String employeeName;

    @ApiModelProperty("见面会日期")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "见面会日期",index = 3)
    private String meetDate;

    @ApiModelProperty("评委工号")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "评委工号",index = 4)
    private String judgeNo;

    @ApiModelProperty("评委姓名")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "评委姓名",index = 5)
    private String judgeName;

    @ApiModelProperty("评委评分")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "评委评分",index = 6)
    private BigDecimal score;

    @ExcelIgnore
    private Long assessedId;
}
