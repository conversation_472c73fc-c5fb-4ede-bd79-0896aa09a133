package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 培训组长评价
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeTrainSubjectiveJudgeExcel extends Convert {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("序号")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "序号",index = 0)
    private String no;


    @ApiModelProperty("学员工号")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "学员工号",index = 1)
    private String employeeNo;

    @ApiModelProperty("学员姓名")
    @HeadFontStyle(color = 0)
    @ExcelProperty(value = "学员姓名",index = 2)
    private String employeeName;

    @ApiModelProperty("得分")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "得分",index = 3)
    private String score;

}
