package com.htks.domain.school.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.domain.school.dto.CollegeTrainPostArea;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainAssessedVo {

    private Long id;

    @ApiModelProperty("读后感截止上传时间")
    private String afterReadingUploadDeadTime;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("选岗部门名称")
    private String chooseDepartmentName;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("入职时间")
    private String entryDate;

    @ApiModelProperty("异常答辩截止时间")
    private String exceptionDefenseEndTime;

    @ApiModelProperty("异常答辩开始时间")
    private String exceptionDefenseStartTime;

    @ApiModelProperty("师傅姓名")
    private String masterName;

    @ApiModelProperty("师傅工号")
    private String masterNo;

    @ApiModelProperty("上岗证获取截止时间")
    private String postCertificateEndTime;

    @ApiModelProperty("上岗证获取开始时间")
    private String postCertificateStartTime;

    @ApiModelProperty("工程实操考试截止时间")
    private String practicalOperationEndTime;

    @ApiModelProperty("工程实操考试开始时间")
    private String practicalOperationStartTime;

    @ApiModelProperty("实习部门ID")
    private String practiceDepartmentAreaId;

//    @ApiModelProperty("SOP截止上传时间")
//    private String sopUploadDeadTime;

    @ApiModelProperty("部门区域配置表")
    private CollegeTrainPostArea collegeTrainPostArea;


    @ApiModelProperty("厂别：华天昆山、华天江苏")
    private String factoryFlag;

    @ApiModelProperty("班别")
    private String classNo;

    @ApiModelProperty("进入BU时间")
    private String joinBuTime;

    @ApiModelProperty("状态：开启、关闭")
    private String enabled;

    @ApiModelProperty("实习部门ID")
    @TableField("DEPARTMENT_ID")
    private String departmentId;
}
