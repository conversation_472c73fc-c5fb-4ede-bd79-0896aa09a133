package com.htks.domain.school.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeSOPExcelDTO {


    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号")
    private String index;
    /**
     * 学员工号
     */
    @ApiModelProperty("学员工号")
    @ExcelProperty(value = "学员工号")
    private String number;
    /**
     * SOP名称
     */
    @ApiModelProperty("SOP名称")
    @ExcelProperty(value = "SOP名称")
    private String sopName;
    /**
     * 质量评委评分
     */
    @ApiModelProperty("质量评委评分")
    @ExcelProperty(value = "质量评委评分")
    private String qaScore;
    /**
     * 工程评委评分
     */
    @ApiModelProperty("工程评委评分")
    @ExcelProperty(value = "工程评委评分")
    private String egScore;
}
