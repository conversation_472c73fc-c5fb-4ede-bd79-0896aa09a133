package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 考核项目一栏表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_CATEGORY")
@ApiModel(value = "CollegeTrainCategory对象", description = "大学生培训系统 考核项目一栏表")
public class CollegeTrainCategory extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("评价方式")
    @TableField("EVALUATE_TYPE")
    private String evaluateType;

    @ApiModelProperty("评委")
    @TableField("EVALUATION")
    private String evaluation;

    @ApiModelProperty("考试形式")
    @TableField("EXAM_TYPE")
    private String examType;

    @ApiModelProperty("分制")
    @TableField("MAX_SCORE")
    private String maxScore;

    @ApiModelProperty("取分&题型")
    @TableField("QUESTION_ROLE")
    private String questionRole;

    @ApiModelProperty("培训类别")
    @TableField("TRAIN_CATEGORY")
    private String trainCategory;

    @ApiModelProperty("培训考试项目")
    @TableField("TRAIN_CATEGORY_ITEM")
    private String trainCategoryItem;

    @ApiModelProperty("培训时长")
    @TableField("TRAIN_DURATION")
    private String trainDuration;

    @ApiModelProperty("培训阶段")
    @TableField("TRAIN_STAGE")
    private String trainStage;

    @ApiModelProperty("权重")
    @TableField("WEIGHT")
    private String weight;


}
