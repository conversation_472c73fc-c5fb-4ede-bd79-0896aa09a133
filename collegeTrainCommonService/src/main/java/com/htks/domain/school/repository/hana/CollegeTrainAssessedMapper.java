package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.request.CollegeTrainEnabledRequest;
import com.htks.domain.school.vo.CollegeTrainAssessedAreaVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainAssessedMapper extends BaseMapper<CollegeTrainAssessed> {

    @Select("select distinct ${column} from COLLEGE_TRAIN_ASSESSED where DELETED_FLAG  = FALSE")
    List<String> getSpecifiedColumn(@Param("column") String column);

    @Select("<script>" +
            "SELECT cta.ID, cta.POST_CERTIFICATE_START_TIME,cta.POST_CERTIFICATE_END_TIME, cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT  as practiceDepartmentName ,ctpa.AREA FROM COLLEGE_TRAIN_ASSESSED cta \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON cta.PRACTICE_DEPARTMENT_AREA_ID = ctpa.ID \n" +
            "WHERE cta.DELETED_FLAG = FALSE " +
            " and cta.ID in    " +
            "<foreach collection='assessedIdList' item='item' open='(' separator=',' close=')' >"+
            " #{item} "+
            "</foreach> " +
            "</script>"
    )
    List<CollegeTrainAssessedAreaVo> getByAssessedIdList(@Param("assessedIdList") List<Long> assessedIdList);

    void updateEnabled(CollegeTrainEnabledRequest request);

    String getDepartment(String employeeNo);
    String getArea(String employeeNo);

}
