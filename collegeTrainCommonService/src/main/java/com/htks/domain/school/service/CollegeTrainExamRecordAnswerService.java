package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.domain.school.dto.CollegeTrainExamRecordAnswer;
import com.htks.domain.school.request.CollegeTrainExamRecordAnswerRequest;
import com.htks.domain.school.vo.CollegeTrainExamRecordAnswerVo;

import java.util.List;

/**
 * <p>
 * 岗位认证系统 考试记录答案表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainExamRecordAnswerService extends IService<CollegeTrainExamRecordAnswer> {

    List<CollegeTrainExamRecordAnswerVo> getExamRecordAnswer(Long examId,String questionType);

    void updateExamRecordAnswer(List<CollegeTrainExamRecordAnswerRequest> examRecordAnswerList);
}
