package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 SOP上传/读后感上传
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_SOP_UPLOAD")
@KeySequence(value = "COLLEGE_TRAIN_SOP_UPLOAD_SEQ",dbType = DbType.SAP_HANA)
@ApiModel(value = "CollegeTrainSopUpload对象", description = "大学生培训系统 SOP上传/读后感上传")
public class CollegeTrainSopUpload extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @ApiModelProperty("附件ID")
    @TableField("ATTACHMENT_ID")
    private Long attachmentId;

    @ApiModelProperty("批阅理由")
    @TableField("EVALUATE_REASON")
    private String evaluateReason;

    @ApiModelProperty("评价状态 已评价：未评价")
    @TableField("EVALUATE_STATUS")
    private String evaluateStatus;

    @ApiModelProperty("阅卷师姓名")
    @TableField("GRADER_NAME")
    private String graderName;

    @ApiModelProperty("阅卷师工号")
    @TableField("GRADER_NO")
    private String graderNo;

    @ApiModelProperty("批阅编号")
    @TableField("GRADING_NUMBER")
    private String gradingNumber;

    @ApiModelProperty("得分")
    @TableField("SCORE")
    private Integer score;

    @ApiModelProperty("SOP名称")
    @TableField("SOP_NAME")
    private String sopName;

    @ApiModelProperty("附件名称")
    @TableField("file_NAME")
    private String fileName;

    @ApiModelProperty("1:sop上传 2：读后感上传")
    @TableField("TYPE")
    private Integer type;

}
