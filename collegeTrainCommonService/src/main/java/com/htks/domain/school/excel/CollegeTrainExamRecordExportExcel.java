package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 大学生 考试记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeTrainExamRecordExportExcel {




    private static final long serialVersionUID = 1L;


    @ExcelProperty(value = "序号",index = 0)
    private Integer number;
    @ExcelProperty(value = "考试编号",index = 1)
    private String examNumber;

    @ApiModelProperty("考试名称")
    @ExcelProperty(value = "考试名称",index = 2)
    private String examName;

    @ExcelProperty(value = "试卷类型",index = 3)
    private String paperType;

    @ExcelProperty(value = "实际考试时间",index = 4)
    private String examTime;

    @ExcelIgnore
    @ApiModelProperty("考试开始时间")
    private String examStartTime;

    @ExcelIgnore
    @ApiModelProperty("考试结束时间")
    private String examEndTime;

    @ApiModelProperty("批次")
    @ExcelProperty(value = "批次",index = 5)
    private String batch;

    @ApiModelProperty("学员姓名")
    @ExcelProperty(value = "学员姓名",index = 6)
    private String employeeName;

    @ApiModelProperty("学员工号")
    @ExcelProperty(value = "学员工号",index = 7)
    private String employeeNo;

    @ApiModelProperty("实习部门")
    @ExcelProperty(value = "实习部门",index = 8)
    private String practiceDepartmentName;

    @ApiModelProperty("区域/制程")
    @ExcelProperty(value = "区域/制程",index = 9)
    private String area;

    @ApiModelProperty("分配状态 已分配：未分配")
    @ExcelProperty(value = "分配状态",index = 10)
    private String allocationStatus;

    @ApiModelProperty("阅卷师工号")
    @ExcelProperty(value = "阅卷师工号",index = 11)
    private String judgeNo;

    @TableField("阅卷师名称")
    @ExcelProperty(value = "阅卷师名称",index = 12)
    private String judgeName;

    @ApiModelProperty("阅卷编号")
    @ExcelProperty(value = "批阅编号",index = 13)
    private String judgeNumber;






}
