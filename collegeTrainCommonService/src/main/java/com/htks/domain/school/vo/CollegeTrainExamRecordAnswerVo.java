package com.htks.domain.school.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 11:05
 */
@Data
public class CollegeTrainExamRecordAnswerVo {
    private Long id;

    @ApiModelProperty("试题类型 1 单选题	2 多选题	3 判断题	4 填空题	5 主观题")
    private String questionType;

    @ApiModelProperty("分值")
    private BigDecimal points;

    @ApiModelProperty("问题内容")
    private String questionContent;

    @ApiModelProperty("标准答案, 存ABCD, 多选使用逗号隔开、填空题存正确答案, 主观,不填写")
    private String standardAnswer;

    @ApiModelProperty("学员答案")
    private String answerText;

    @ApiModelProperty("得分")
    private BigDecimal score;

    @ApiModelProperty("附件ID")
    private Long attachmentId;

    @ApiModelProperty("单选题满分")
    private BigDecimal singleOptionAnswer;

    @ApiModelProperty("多选题满分")
    private BigDecimal moreOptionAnswer;

    @ApiModelProperty("判断题满分")
    private BigDecimal judgeAnswer;

    @ApiModelProperty("填空题满分")
    private BigDecimal fillAnswer;

    @ApiModelProperty("解答题满分")
    private BigDecimal shortAnswer;
    @ApiModelProperty("解答题文件地址")
    private String attachmentPath;
    @ApiModelProperty("说明")
    private String note;
}
