package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生 试卷问题表 - 主表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_QUESTION")
@ApiModel(value = "CollegeTrainQuestion对象", description = "大学生 试卷问题表 - 主表")
public class CollegeTrainQuestion extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("试卷ID")
    @TableField("PAPER_ID")
    private Long paperId;

    @ApiModelProperty("问题内容")
    @TableField("QUESTION_CONTENT")
    private String questionContent;

    @ApiModelProperty("试题类型 1 单选题	2 多选题	3 判断题	4 填空题	5 主观题")
    @TableField("QUESTION_TYPE")
    private String questionType;

    @ApiModelProperty("标准答案, 存ABCD, 多选使用逗号隔开、填空题存正确答案, 主观,不填写")
    @TableField("STANDARD_ANSWER")
    private String standardAnswer;

    @TableField("UPDATED_USER")
    private String updatedUser;


}
