package com.htks.domain.school.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.*;
import com.htks.domain.question.dto.QuestionImportExcel;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainPostArea;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.excel.CollegeTrainAssessedExcel;
import com.htks.domain.school.excel.CollegeTrainEngineeringExamRecordExcel;
import com.htks.domain.school.excel.CollegeTrainExamRecordExportExcel;
import com.htks.domain.school.repository.hana.CollegeTrainAssessedMapper;
import com.htks.domain.school.request.CollegeTrainAssessedRequest;
import com.htks.domain.school.request.CollegeTrainEnabledRequest;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainPostAreaService;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.school.vo.CollegeTrainAssessedAreaVo;
import com.htks.domain.school.vo.CollegeTrainAssessedVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CollegeTrainAssessedServiceImpl extends ServiceImpl<CollegeTrainAssessedMapper, CollegeTrainAssessed> implements CollegeTrainAssessedService {
    @Resource
    private CollegeTrainPostAreaService collegeTrainPostAreaService;
    @Resource
    private CommonEmployeeService commonEmployeeService;

    @Resource
    private CollegeTrainAssessedMapper collegeTrainAssessedMapper;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @Override
    public boolean checkExist(String loginName) {
        LambdaQueryWrapper<CollegeTrainAssessed> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CollegeTrainAssessed::getEmployeeNo, loginName)
                .eq(CollegeTrainAssessed::getDeletedFlag, Boolean.FALSE)
                .eq(CollegeTrainAssessed::getEnabled, "开启");
        long count = count(wrapper);
        return count > 0 ? true : false;
    }

    @Override
    public boolean checkExist2(String loginName) {
        LambdaQueryWrapper<CollegeTrainAssessed> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CollegeTrainAssessed::getEmployeeNo, loginName)
                .eq(CollegeTrainAssessed::getDeletedFlag, Boolean.FALSE);
        long count = count(wrapper);
        return count > 0 ? true : false;
    }

    @Override
    public CollegeTrainAssessed getByEmployeeNo(String employeeNumber) {
        LambdaQueryWrapper<CollegeTrainAssessed> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CollegeTrainAssessed::getEmployeeNo, employeeNumber)
                .eq(CollegeTrainAssessed::getDeletedFlag, Boolean.FALSE);
        return getOne(wrapper);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainAssessedExcel> list = new ArrayList<>();

        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();
        List<CollegeTrainPostArea> collegeTrainPostAreaList = collegeTrainPostAreaService.list();
        List<String> chooseDepartmentAreaList = collegeTrainPostAreaList.stream().map(CollegeTrainPostArea::getPracticeDepartment).collect(Collectors.toList());
        List<String> areaList = collegeTrainPostAreaList.stream().map(CollegeTrainPostArea::getArea).collect(Collectors.toList());
        fieldValues.put(4, chooseDepartmentAreaList);
        fieldValues.put(5, chooseDepartmentAreaList);
        fieldValues.put(6, areaList);

        SheetUtil sheetUtil = new SheetUtil(fieldValues);

        String fileName = URLEncoder.encode("大学生信息模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        CustomSheetWriteHandler customSheetWriteHandler = new CustomSheetWriteHandler();
        EasyExcel.write(response.getOutputStream(), CollegeTrainAssessedExcel.class)
                .registerWriteHandler(new CustomSheetWriteHandler())
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("大学生信息").registerWriteHandler(sheetUtil).doWrite(list);
    }

    @Override
    public ResultVO addCollegeTrainAssessed(CollegeTrainAssessed collegeTrainAssessed) {
        boolean exist = checkExist2(collegeTrainAssessed.getEmployeeNo());
        if (exist) {
            return ResultVO.error("工号已存在,请忽重复添加");
        }
        save(collegeTrainAssessed);
        return ResultVO.success();
    }

    @Override
    public ResultVO updateCollegeTrainAssessed(CollegeTrainAssessed collegeTrainAssessed) {
        updateById(collegeTrainAssessed);
        return ResultVO.success();
    }

    @Override
    public ResultVO importTemplate(MultipartFile file) {
        final InputStream in = CommonDecryptFileHelper.commonDecryptFile(file);
        List<CollegeTrainAssessedExcel> collegeTrainAssessedExcelList = null;
        try {
            collegeTrainAssessedExcelList = EasyExcelUtils.readExcel2(in, CollegeTrainAssessedExcel.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }
        List<CollegeTrainAssessedExcel> collegeTrainAssessedExcelListcopy=new ArrayList<>();
 /*       for ( CollegeTrainAssessedExcel collegeTrainAssessedExcel:collegeTrainAssessedExcelList) {
            if (collegeTrainAssessedExcel.getBatch()==null){
                collegeTrainAssessedExcelListcopy.add(collegeTrainAssessedExcel);
            }
        }*/
    //    collegeTrainAssessedExcelList.removeAll(collegeTrainAssessedExcelListcopy);
        collegeTrainAssessedExcelList.remove(0);
        List<CollegeTrainAssessed> list = new ArrayList<>();

        int a=-1;
        for  (int i = 0; i < collegeTrainAssessedExcelList.size(); i++) {
            CollegeTrainAssessedExcel collegeTrainEngineeringExamRecordExcel = collegeTrainAssessedExcelList.get(i);

            if (collegeTrainEngineeringExamRecordExcel.getNumber()==null){

            }else {
                if (collegeTrainEngineeringExamRecordExcel.getNumber().contains("导入文档说明")) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a< collegeTrainAssessedExcelList.size()) {
            collegeTrainAssessedExcelList.subList(a, collegeTrainAssessedExcelList.size()).clear();
        }
        if (ObjectUtil.isNotEmpty(collegeTrainAssessedExcelList)) {
            for (int i = 0; i < collegeTrainAssessedExcelList.size(); i++) {
                CollegeTrainAssessedExcel collegeTrainAssessedExcel = collegeTrainAssessedExcelList.get(i);
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getEmployeeNo(), "第" + (i + 2) + "行工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getBatch(), "第" + (i + 2) + "批次不能为空!");
//                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getPracticeDepartment(), "第" + (i + 2) + "行实习部门不能为空!");
//                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getArea(), "第" + (i + 2) + "行区域/制程不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getPostCertificateStartTime(), "第" + (i + 2) + "行上岗证获取开始时间不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getPostCertificateEndTime(), "第" + (i + 2) + "行上岗证获取截止时间不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getPracticalOperationStartTime(), "第" + (i + 2) + "行工程实操考试开始时间不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getPracticalOperationEndTime(), "第" + (i + 2) + "行工程实操考试截止时间不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getExceptionDefenseStartTime(), "第" + (i + 2) + "行异常报告答辩开始时间不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getExceptionDefenseEndTime(), "第" + (i + 2) + "行异常报告答辩截止时间不能为空!");
//                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getSopUploadDeadTime(), "第" + (i + 2) + "行SOP截止上传时间不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainAssessedExcel.getAfterReadingUploadDeadTime(), "第" + (i + 2) + "行读后感截止上传时间不能为空!");
                //获取员工信息
                CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainAssessedExcel.getEmployeeNo());
                //获取师傅信息
                CommonEmployee masterEmployeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainAssessedExcel.getMasterNo());
                if (ObjectUtil.isEmpty(employeeInfo) || ObjectUtil.isEmpty(masterEmployeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                int column = checkTime(collegeTrainAssessedExcel);
                if (0 != column) {
                    return ResultVO.error("第" + (i + 2) + "行，" + column + "列数据格式错误");
                }
                CollegeTrainPostArea collegeTrainPostArea = new CollegeTrainPostArea();
                if(!StringUtils.isEmpty(collegeTrainAssessedExcel.getPracticeDepartment()) && !StringUtils.isEmpty(collegeTrainAssessedExcel.getArea())){
                    collegeTrainPostArea = collegeTrainPostAreaService.getByDepartmentAndArea(collegeTrainAssessedExcel.getPracticeDepartment(), collegeTrainAssessedExcel.getArea(),collegeTrainAssessedExcel.getFactoryFlag());
                    if (ObjectUtil.isEmpty(collegeTrainPostArea)) {
                        return ResultVO.error(collegeTrainAssessedExcel.getEmployeeNo()+"实习部门和区域未匹配到对应配置");
                    }
                }

                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedExcel.convert(CollegeTrainAssessed.class);
                CollegeTrainAssessed dbCollegeTrainAssessed = getByEmployeeNo(collegeTrainAssessedExcel.getEmployeeNo());
                if (ObjectUtil.isNotEmpty(dbCollegeTrainAssessed)) {
                    collegeTrainAssessed.setId(dbCollegeTrainAssessed.getId());
                }
                collegeTrainAssessed.setEmployeeName(employeeInfo.getEmployeeName());
                collegeTrainAssessed.setEntryDate(employeeInfo.getEntryDate());
                collegeTrainAssessed.setMasterName(masterEmployeeInfo.getEmployeeName());
                collegeTrainAssessed.setPracticeDepartmentAreaId(collegeTrainPostArea.getId() == null ? null : collegeTrainPostArea.getId().toString());
                collegeTrainAssessed.setEnabled("开启");
                collegeTrainAssessed.setChooseDepartmentId(uploadWorkLicense.getDepartmentId(collegeTrainAssessedExcel.getFactoryFlag(),collegeTrainAssessed.getChooseDepartmentName()));
                collegeTrainAssessed.setDepartmentId(uploadWorkLicense.getDepartmentId(collegeTrainAssessedExcel.getFactoryFlag(),collegeTrainAssessedExcel.getPracticeDepartment()));
                list.add(collegeTrainAssessed);
            }
        }
        saveOrUpdateBatch(list);
        return ResultVO.success("成功导入" + list.size() + "数据");
    }

    @Override
    public JsonPagedVO<CollegeTrainAssessedVo> queryPaperList(CollegeTrainAssessedRequest request) {
        Page<CollegeTrainAssessed> page = PageUtils.getPage(request);
//        List<Long> departmentIdList = null;
//        if (ObjectUtil.isNotEmpty(request.getPracticeDepartmentNameList())) {
//            List<CollegeTrainPostArea> list = collegeTrainPostAreaService.getByDepartmentList(request.getPracticeDepartmentNameList());
//            departmentIdList = list.stream().map(CollegeTrainPostArea::getId).collect(Collectors.toList());
//        }
//        List<String> list = new ArrayList<>();
        if (request.getPracticeDepartmentNameList()!=null) {
//        for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
//            list.add((uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i))));
//        }

        List<String> factory = new ArrayList<>();
        if (request.getFactoryFlagList() == null || request.getFactoryFlagList().size() == 0) {
//            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
//                factory.add(uploadWorkLicense.getFactory(request.getPracticeDepartmentNameList().get(i)));
//            }
//            if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
//                request.setFactoryFlagList(factory);
//            } else if (factory.contains("华天昆山")) {
//                request.setFactoryFlagList(factory);
//            } else if (factory.contains("华天江苏")) {
//                request.setFactoryFlagList(factory);
//            }
        }
        }
        LambdaQueryWrapper<CollegeTrainAssessed> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(ObjectUtil.isNotEmpty(request.getBatch()), CollegeTrainAssessed::getBatch, request.getBatch())
                .in(ObjectUtil.isNotEmpty(request.getFactoryFlagList()), CollegeTrainAssessed::getFactoryFlag, request.getFactoryFlagList())
                .in(ObjectUtil.isNotEmpty(request.getEnabledList()), CollegeTrainAssessed::getEnabled, request.getEnabledList())
                .eq(ObjectUtil.isNotEmpty(request.getEmployeeNo()), CollegeTrainAssessed::getEmployeeNo, request.getEmployeeNo())
                .eq(ObjectUtil.isNotEmpty(request.getEmployeeName()), CollegeTrainAssessed::getEmployeeName, request.getEmployeeName())
//                .in(ObjectUtil.isNotEmpty(list), CollegeTrainAssessed::getChooseDepartmentName, list)
                .in(ObjectUtil.isNotEmpty(request.getChooseDepartmentNameList()), CollegeTrainAssessed::getChooseDepartmentId, request.getChooseDepartmentNameList())
                //.in(ObjectUtil.isNotEmpty(departmentIdList), CollegeTrainAssessed::getPracticeDepartmentAreaId, departmentIdList)
                .in(ObjectUtil.isNotEmpty(request.getPracticeDepartmentAreaId()), CollegeTrainAssessed::getPracticeDepartmentAreaId, request.getPracticeDepartmentAreaId())
                .in(ObjectUtil.isNotEmpty(request.getPracticeDepartmentNameList()), CollegeTrainAssessed::getDepartmentId, request.getPracticeDepartmentNameList())
                .ge(ObjectUtil.isNotEmpty(request.getEntryDateStart()), CollegeTrainAssessed::getEntryDate, request.getEntryDateStart())
                .le(ObjectUtil.isNotEmpty(request.getEntryDateEnd()), CollegeTrainAssessed::getEntryDate, request.getEntryDateEnd())
                .orderByDesc(CollegeTrainAssessed::getEmployeeNo);

        IPage<CollegeTrainAssessed> collegeTrainAssessedPage = page(page, queryWrapper);
        IPage<CollegeTrainAssessedVo> collegeTrainAssessedVoPage = collegeTrainAssessedPage.convert(o -> {
            CollegeTrainAssessedVo collegeTrainAssessedVo = o.convert(CollegeTrainAssessedVo.class);
            if(!StringUtils.isEmpty(collegeTrainAssessedVo.getPracticeDepartmentAreaId())){
                CollegeTrainPostArea collegeTrainPostArea = collegeTrainPostAreaService.getById(collegeTrainAssessedVo.getPracticeDepartmentAreaId());
                collegeTrainAssessedVo.setCollegeTrainPostArea(collegeTrainPostArea);
            }else if(!StringUtils.isEmpty(collegeTrainAssessedVo.getDepartmentId())){
                //只选择实习部门，没选择区域
                CollegeTrainPostArea collegeTrainPostArea = new CollegeTrainPostArea();
                collegeTrainPostArea.setPracticeDepartment(uploadWorkLicense.getDepartmentName(collegeTrainAssessedVo.getDepartmentId()));
                collegeTrainAssessedVo.setCollegeTrainPostArea(collegeTrainPostArea);
            }
            return collegeTrainAssessedVo;
        });

        return JsonPagedVO.success(collegeTrainAssessedVoPage.getRecords(),Integer.parseInt(collegeTrainAssessedVoPage.getTotal()+""));
    }

    @Override
    public ResultVO getSpecifiedColumn(String column) {
        List<String> list = this.baseMapper.getSpecifiedColumn(column);
        return ResultVO.success(list);
    }

    @Override
    public List<CollegeTrainAssessedAreaVo> getByAssessedIdList(List<Long> assessedIdList) {
        return this.baseMapper.getByAssessedIdList(assessedIdList);
    }

    @Override
    public ResultVO updateEnabled(CollegeTrainEnabledRequest request) {
        collegeTrainAssessedMapper.updateEnabled(request);
        return ResultVO.success(true);
    }

    @Override
    public void export(CollegeTrainAssessedRequest request, HttpServletResponse response) {
        request.setPageNum(1L);
        request.setPageSize(100000L);
        JsonPagedVO<CollegeTrainAssessedVo> vo = queryPaperList(request);
        List<CollegeTrainAssessedVo> list = (List<CollegeTrainAssessedVo>) vo.getData();
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<CollegeTrainAssessedExcel> collegeTrainAssessedExcelList = new ArrayList<>(list.size());
        for (int i = 0; i <list.size() ; i++) {
            CollegeTrainAssessedExcel collegeTrainAssessedExcel = new CollegeTrainAssessedExcel();
            BeanUtil.copyProperties(list.get(i),collegeTrainAssessedExcel);
            collegeTrainAssessedExcel.setNumber(i+1 + "");
            collegeTrainAssessedExcel.setArea(collegeTrainAssessedMapper.getArea(collegeTrainAssessedExcel.getEmployeeNo()));
            collegeTrainAssessedExcel.setPracticeDepartment(collegeTrainAssessedMapper.getDepartment(collegeTrainAssessedExcel.getEmployeeNo()));
            collegeTrainAssessedExcelList.add(collegeTrainAssessedExcel);
        }
        EasyExcelUtils.exportExcel(response, collegeTrainAssessedExcelList, "大学生信息导出数据", CollegeTrainAssessedExcel.class);
    }


    /**
     * 检查时间是否符合年月日格式,不符合返回对应列,符合返回0
     *
     * @param collegeTrainAssessedExcel 学院培训评估excel
     * @return int
     */
    private int checkTime(CollegeTrainAssessedExcel collegeTrainAssessedExcel) {
        if (collegeTrainAssessedExcel.getJoinBuTime()!=null && !DateUtils.isValidDate(collegeTrainAssessedExcel.getJoinBuTime())) {
            return 11;
        }
        if (!DateUtils.isValidDate(collegeTrainAssessedExcel.getPostCertificateStartTime())) {
            return 12;
        }
        if (!DateUtils.isValidDate(collegeTrainAssessedExcel.getPostCertificateEndTime())) {
            return 13;
        }
        if (!DateUtils.isValidDate(collegeTrainAssessedExcel.getPracticalOperationStartTime())) {
            return 14;
        }
        if (!DateUtils.isValidDate(collegeTrainAssessedExcel.getPracticalOperationEndTime())) {
            return 15;
        }
        if (!DateUtils.isValidDate(collegeTrainAssessedExcel.getExceptionDefenseStartTime())) {
            return 16;
        }
        if (!DateUtils.isValidDate(collegeTrainAssessedExcel.getExceptionDefenseEndTime())) {
            return 17;
        }
        if (!DateUtils.isValidDate(collegeTrainAssessedExcel.getAfterReadingUploadDeadTime())) {
            return 18;
        }
        return 0;
    }
    public class CustomSheetWriteHandler implements SheetWriteHandler {
        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            Row row = sheet.getRow(20);  // 获取第十行
            if (row == null) {
                row = sheet.createRow(20);
            }
            Cell cell = row.createCell(0);  // 获取第一列
            cell.setCellValue("以下说明文字在导入文档中显示:\n" +
                    "导入文档说明:\n" +
                    "(1)红色为必填项:\n" +
                    "(2)工号和师傅工号必须为人事系统中人员信息中已有的数据，如不是则提示导入失败;\n" +
                    "(3)选岗部门:必须为Bumping工程部、WLP工程部、TSV8寸工程部、TSV12寸工程部、CP工程部、FC工程部、FT工程部、产品工程部和资讯技术部的其中一个;\n" +
                    "实习部门:必须为Bumping工程部、WLP工程部、TSV8寸工程部、TSV12寸工程部、CP工程部、FC工程部、FT工程部其中之一:(4)区域/制程:根据填写实习部门，对应《部门区域配置表》表中的数据;\n" +
                    "(5)上岗证获取开始时间、上岗证获取截止时间、工程类实操考试开始时间、工程类实换考试截止时间，异常服告答辩开始时间、异常报告等辩截止时间、SOP上传截止时间和读后感上传截止时间:具体到日即可，如2023-03-28，必须为文本格式，否则导入失败。");  // 填入值
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                if (mergedRegion.getFirstRow() == 20 && mergedRegion.getFirstColumn() == 0 && mergedRegion.getLastColumn() == 6) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 合并第十行的第1到第5列
            sheet.addMergedRegion(new CellRangeAddress(
                    20, // 第十行
                    26, // 第十行
                    0, // 第一列
                    6  // 第五列
            ));
        }
    }
}
