package com.htks.domain.school.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 14:00
 */
@Data
public class StartReviewingRequest {

    @ApiModelProperty("试卷记录主键")
    private Long examId;

    @ApiModelProperty("主观题得分")
    private BigDecimal subjectiveScore;

    @ApiModelProperty("评分理由")
    private String evaluateReason;

    @ApiModelProperty("题目回答得分")
    private List<CollegeTrainExamRecordAnswerRequest> examRecordAnswerList;


}
