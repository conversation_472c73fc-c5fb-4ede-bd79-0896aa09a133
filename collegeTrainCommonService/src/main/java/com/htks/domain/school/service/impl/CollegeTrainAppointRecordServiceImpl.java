package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.EasyExcelUtils;
import com.htks.common.utils.PageUtils;
import com.htks.domain.common.service.CommonService;
import com.htks.domain.school.dto.CollegeTrainAppointRecord;
import com.htks.domain.school.dto.CollegeTrainAppointRecordDetail;
import com.htks.domain.school.enums.AppointTypeEnum;
import com.htks.domain.school.excel.CollegeTrainAppointRecordExportExcel;
import com.htks.domain.school.repository.hana.CollegeTrainAppointRecordMapper;
import com.htks.domain.school.request.CollegeTrainAppointRecordRequest;
import com.htks.domain.school.service.CollegeTrainAppointRecordDetailService;
import com.htks.domain.school.service.CollegeTrainAppointRecordService;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 大学生培训系统 预约记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainAppointRecordServiceImpl extends ServiceImpl<CollegeTrainAppointRecordMapper, CollegeTrainAppointRecord> implements CollegeTrainAppointRecordService {
    @Resource
    private CollegeTrainAppointRecordDetailService collegeTrainAppointRecordDetailService;
    @Resource
    private CommonService service;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @Override
    public JsonPagedVO<CollegeTrainAppointRecordVo> queryPaperList(CollegeTrainAppointRecordRequest request) {
        Page<CollegeTrainAppointRecordVo> page = PageUtils.getPage(request);
        List<String> appointStatusList = request.getAppointStatus();
        List<String> resultStatus = new ArrayList<>();
        if(appointStatusList.size() > 0){
            for (int i = 0; i < appointStatusList.size(); i++) {
                String status = appointStatusList.get(i);
                if("已预约".equals(status)){
                    resultStatus.add("1");
                }else if("已退回".equals(status)){
                    resultStatus.add("2");
                }
            }
            request.setAppointStatus(resultStatus);
        }
        if (request.getPracticeDepartmentNameList()!=null) {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                list.add(uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i)));
            }

            List<String> factory = new ArrayList<>();
            if (request.getFactoryType() == null || request.getFactoryType().equals("")) {
                for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                    factory.add(uploadWorkLicense.getFactory(request.getPracticeDepartmentNameList().get(i)));
                }
                if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
                    request.setFactoryType("");
                } else if (factory.contains("华天昆山")) {
                    request.setFactoryType("华天昆山");
                } else if (factory.contains("华天江苏")) {
                    request.setFactoryType("华天江苏");
                }
            }
            request.setPracticeDepartmentNameList(list);
        }

        IPage<CollegeTrainAppointRecordVo> collegeTrainAppointRecordPage = this.baseMapper.queryPaperList(page,request);
        List<CollegeTrainAppointRecordVo> records = collegeTrainAppointRecordPage.getRecords();
        for (CollegeTrainAppointRecordVo vo:records) {
            String appointStatus = vo.getAppointStatus();
            if("1".equals(appointStatus)){
                vo.setAppointStatus("已预约");
            }else if("2".equals(appointStatus)){
                vo.setAppointStatus("已退回");
            }
        }
        return JsonPagedVO.success(records,Integer.parseInt(collegeTrainAppointRecordPage.getTotal()+""));
    }

    @Override
    public void export(CollegeTrainAppointRecordRequest request, HttpServletResponse response) {
        Page<CollegeTrainAppointRecordVo> page = PageUtils.getPage(request);
        List<String> appointStatusList = request.getAppointStatus();
        List<String> resultStatus = new ArrayList<>();
        if(appointStatusList.size() > 0){
            for (int i = 0; i < appointStatusList.size(); i++) {
                String status = appointStatusList.get(i);
                if("已预约".equals(status)){
                    resultStatus.add("1");
                }else if("已退回".equals(status)){
                    resultStatus.add("2");
                }
            }
            request.setAppointStatus(resultStatus);
        }
        if (request.getPracticeDepartmentNameList()!=null) {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                list.add(uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i)));
            }

            List<String> factory = new ArrayList<>();
            if (request.getFactoryType() == null || request.getFactoryType().equals("")) {
                for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                    factory.add(uploadWorkLicense.getFactory(request.getPracticeDepartmentNameList().get(i)));
                }
                if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
                    request.setFactoryType("");
                } else if (factory.contains("华天昆山")) {
                    request.setFactoryType("华天昆山");
                } else if (factory.contains("华天江苏")) {
                    request.setFactoryType("华天江苏");
                }
            }
            request.setPracticeDepartmentNameList(list);
        }
      //  IPage<CollegeTrainAppointRecordVo> collegeTrainAppointRecordPage = this.baseMapper.queryPaperList(page,request);
        List<CollegeTrainAppointRecordVo> list = this.baseMapper.getList(request);

        //List<CollegeTrainAppointRecordVo> list = collegeTrainAppointRecordPage.getRecords();
        for (CollegeTrainAppointRecordVo collegeTrainAppointRecordVo:list){
            collegeTrainAppointRecordVo.setAppointEndTime(collegeTrainAppointRecordVo.getAppointEndDate()+" "+collegeTrainAppointRecordVo.getAppointEndTime());
            collegeTrainAppointRecordVo.setAppointStartTime(collegeTrainAppointRecordVo.getAppointStartDate()+" "+collegeTrainAppointRecordVo.getAppointStartTime());
        }
        List<CollegeTrainAppointRecordExportExcel> collect = list.stream().map(o -> {
            CollegeTrainAppointRecordExportExcel excel = o.convert(CollegeTrainAppointRecordExportExcel.class);
            excel.setPassedStr(o.getPassed() ? "是" : "否");
            excel.setAppointStatus("1".equals(o.getAppointStatus())? "已预约" : "已退回");
            excel.setTypeStr(AppointTypeEnum.getInfoByCode(o.getType()));
            return excel;
        }).collect(Collectors.toList());
  /*      for (int i = 0; i < collect.size(); i++) {
            collect.get(i).setNumber(String.valueOf(i+1));
        }*/
for (CollegeTrainAppointRecordExportExcel excel :collect){
    if (excel.getType()==2){
        excel.setPassedStr(null);
           }
        }
        EasyExcelUtils.exportExcel(response,collect,"预约记录",CollegeTrainAppointRecordExportExcel.class);
    }

    @Override
    public ResultVO getAppointRecordDetail(Long appointRecordId) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(appointRecordId);
        List<CollegeTrainAppointRecordDetail> collegeTrainAppointRecordDetailList = collegeTrainAppointRecordDetailService.getAppointRecordDetail(appointRecordId);
/*        for (CollegeTrainAppointRecordDetail recordDetail:collegeTrainAppointRecordDetailList){
            if (service.getEmployeeBaseInfo(recordDetail.getEvaluatorNo()).getDepartmentName().equals("产品工程部")){
                    recordDetail.setDeptType(1);
                }else if (service.getEmployeeBaseInfo(recordDetail.getEvaluatorNo()).getDepartmentName().equals("质量部")){
                recordDetail.setDeptType(2);
            }else {
                recordDetail.setDeptType(3);
            }
        }*/
        return ResultVO.success(collegeTrainAppointRecordDetailList);
    }

    @Override
    public ResultVO backAppointRecode(Long appointRecordId) {
        String s = this.baseMapper.queryAppointRecode(appointRecordId);
        if("已考核".equals(s)){
            return ResultVO.error("退回失败:已考核");
        }
        int i = this.baseMapper.updateAppointRecode(appointRecordId);
        if( i > 0){
            return ResultVO.success("退回成功");
        }else {
            return ResultVO.error("退回失败");
        }

    }
}
