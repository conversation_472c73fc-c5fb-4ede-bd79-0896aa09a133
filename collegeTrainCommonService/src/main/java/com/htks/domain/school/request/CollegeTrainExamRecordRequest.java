package com.htks.domain.school.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.common.convert.Search;
import com.htks.domain.AbstractPaginationQueryCondition;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 大学生 考试记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainExamRecordRequest extends Search {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("分配状态 已分配：未分配")
    private String allocationStatus;

    @ApiModelProperty("考试名称")
    private String examName;

    @ApiModelProperty("考试科目")
    private List<String> examType = new ArrayList<>();

    @ApiModelProperty("考试编号")
    private String examNumber;

    @ApiModelProperty("阅卷师工号")
    private String judgeNo;

    @ApiModelProperty("阅卷师名称")
    private String judgeName;

    @ApiModelProperty("实际考试日期起始")
    private String examStartTime;

    @ApiModelProperty("实际考试日期结束")
    private String examEndTime;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private List<String> practiceDepartmentNameList;

    @ApiModelProperty("如果选了区域,直接给对应配置主键")
    private String practiceDepartmentAreaId;

    @ApiModelProperty("阅卷编号")
    private String judgeNumber;



}
