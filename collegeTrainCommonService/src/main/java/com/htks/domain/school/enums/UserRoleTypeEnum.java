package com.htks.domain.school.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户角色类型枚举
 *
 * <AUTHOR>
 * @date 2023/05/09
 */
@Getter
@RequiredArgsConstructor
public enum UserRoleTypeEnum {
    GRADER("101", "阅卷师登录"),
    TRAINING_TEAM_LEADER("102", "培训组长登录"),
    ADMINISTRATOR("100", "管理员登录");


    private final String code;
    private final String info;

    public static UserRoleTypeEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (UserRoleTypeEnum e : UserRoleTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }

}
