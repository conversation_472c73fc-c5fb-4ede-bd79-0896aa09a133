package com.htks.domain.school.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 预约项目类型枚举
 *
 * <AUTHOR>
 * @date 2023/05/09
 */
@Getter
@RequiredArgsConstructor
public enum AppointTypeEnum {
    ENGINEERING(1, "工程类实操"),
    EXCEPTION_DEFENSE(2, "异常答辩");


    private final Integer code;
    private final String info;

    public static AppointTypeEnum getByCode(Integer code) {
        if (ObjectUtil.isNotEmpty(code)) {
            for (AppointTypeEnum e : AppointTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        if (ObjectUtil.isNotEmpty(code)) {
            for (AppointTypeEnum e : AppointTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getInfo();
                }
            }
        }
        return null;
    }

}
