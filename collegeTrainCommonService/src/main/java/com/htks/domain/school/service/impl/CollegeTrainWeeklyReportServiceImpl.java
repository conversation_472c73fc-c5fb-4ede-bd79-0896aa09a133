package com.htks.domain.school.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.io.Closer;
import com.google.common.io.Resources;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.*;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.dto.CollegeTrainWeeklyReport;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.enums.EmployeeStatusEnum;
import com.htks.domain.school.excel.CollegeTrainExamRecordExcel;
import com.htks.domain.school.excel.CollegeTrainSubjectiveJudgeExcel;
import com.htks.domain.school.excel.CollegeTrainWeeklyReportExcel;
import com.htks.domain.school.repository.hana.CollegeTrainWeeklyReportMapper;
import com.htks.domain.school.request.CollegeTrainSubjectiveJudgeRequest;
import com.htks.domain.school.request.CollegeTrainWeeklyReportRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainWeeklyReportService;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.school.vo.CollegeTrainSubjectiveJudgeVo;
import com.htks.domain.school.vo.CollegeTrainWeeklyReportVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p>
 * 大学生培训系统 周报 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Slf4j
@Service
public class CollegeTrainWeeklyReportServiceImpl extends ServiceImpl<CollegeTrainWeeklyReportMapper, CollegeTrainWeeklyReport> implements CollegeTrainWeeklyReportService {
    @Resource
    private CommonEmployeeService commonEmployeeService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;

    @Override
    public void downTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainWeeklyReportExcel> list = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setNo(String.valueOf(i+1));
        }
        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();

        SheetUtil sheetUtil = new SheetUtil(fieldValues);
        CustomSheetWriteHandler customSheetWriteHandler = new CustomSheetWriteHandler();

        String fileName = URLEncoder.encode("周报评价模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        EasyExcel.write(response.getOutputStream(), CollegeTrainWeeklyReportExcel.class)
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("周报评价").registerWriteHandler(sheetUtil)
                .doWrite(list);
    }
    @Override
    public  void downloadTemplate(HttpServletResponse response ) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("周报评价管理模板".getBytes("GB2312"), "iso8859-1") + ".xlsx");//指定下载的文件名

        final Closer closer = Closer.create();
        try {
            final OutputStream os = response.getOutputStream();
            closer.register(os);
            os.write(getImportTemplates("import/周报评价管理模板.xlsx"));
        } catch (IOException e) {
            log.error("IOException:{}", e.getMessage());
        } finally {
            try {
                closer.close();
            } catch (IOException e) {
                log.error("IOException:{}", e.getMessage());
            }
        }
    }
    private static byte[] getImportTemplates(String importTemplatesPath) {
        try {
            return Resources.asByteSource(Resources.getResource(importTemplatesPath)).read();
        } catch (IOException e) {
            log.error("获取文件异常:{}", e.getMessage());
            return new byte[0];
        }
    }
    @Override
    public ResultVO importTemplate(MultipartFile file) {
        final InputStream in = CommonDecryptFileHelper.commonDecryptFile(file);

        List<CollegeTrainWeeklyReportExcel> collegeTrainWeeklyReportExcelList = null;
        try {
            collegeTrainWeeklyReportExcelList = EasyExcelUtils.readExcel2(in, CollegeTrainWeeklyReportExcel.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }

        List<CollegeTrainWeeklyReport> list = new ArrayList<>();

        int a=-1;
        for  (int i = 0; i < collegeTrainWeeklyReportExcelList.size(); i++) {
            CollegeTrainWeeklyReportExcel collegeTrainEngineeringExamRecordExcel = collegeTrainWeeklyReportExcelList.get(i);
            if (collegeTrainEngineeringExamRecordExcel.getNo()!=null){
                if (collegeTrainEngineeringExamRecordExcel.getNo().contains("导入文档说明")||(collegeTrainEngineeringExamRecordExcel.getWeekly()==null&&collegeTrainEngineeringExamRecordExcel.getScore()==null&&collegeTrainEngineeringExamRecordExcel.getJudgeNo()==null)) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a< collegeTrainWeeklyReportExcelList.size()) {
            collegeTrainWeeklyReportExcelList.subList(a, collegeTrainWeeklyReportExcelList.size()).clear();
        }
        collegeTrainWeeklyReportExcelList.remove(0);
        if (ObjectUtil.isNotEmpty(collegeTrainWeeklyReportExcelList)) {
            for (int i = 0; i < collegeTrainWeeklyReportExcelList.size(); i++) {
                CollegeTrainWeeklyReportExcel collegeTrainWeeklyReportExcel = collegeTrainWeeklyReportExcelList.get(i);
                if (collegeTrainWeeklyReportExcel.getJudgeNo() == null && collegeTrainWeeklyReportExcel.getEmployeeNo() == null && collegeTrainWeeklyReportExcel.getWeekly() == null
                &&collegeTrainWeeklyReportExcel.getScore()==null) {

                }else {
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainWeeklyReportExcel.getEmployeeNo(), "第" + (i + 2) + "行学员工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainWeeklyReportExcel.getWeekly(), "第" + (i + 2) + "行周别不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainWeeklyReportExcel.getJudgeNo(), "第" + (i + 2) + "行阅卷师工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainWeeklyReportExcel.getScore(), "第" + (i + 2) + "行得分不能为空!");
                if (!isPositiveInteger(collegeTrainWeeklyReportExcel.getScore())) {
                    return ResultVO.error("第" + (i + 2) + "行得分不是正整数");
                }
                if (!isPositiveInteger(collegeTrainWeeklyReportExcel.getWeekly())) {
                    return ResultVO.error("第" + (i + 2) + "行周别不是正整数");
                }
                int score = Integer.parseInt(collegeTrainWeeklyReportExcel.getScore());
                if (score > 100 || score < 0) {
                    return ResultVO.error("得分数据取值范围是1-100，请检查数据");
                }
                if (!CheckNumberUtils.isNumber(collegeTrainWeeklyReportExcel.getScore() + "") && (score <= 100 && score >= 0)) {
                    return ResultVO.error("第" + (i + 2) + "行得分数据取值范围是1-100");
                }
                int week = Integer.parseInt(collegeTrainWeeklyReportExcel.getWeekly());
                if (!(week <= 10 && week >= 1)) {
                    return ResultVO.error("周别取值范围是1-10，请检查数据");
                }

                //获取员工信息
                CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainWeeklyReportExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(employeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //获取阅卷师信息,必须是在职状态
                CommonEmployee judgeEmployeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainWeeklyReportExcel.getJudgeNo(), EmployeeStatusEnum.ON_THE_JOB.getCode());
                if (ObjectUtil.isEmpty(judgeEmployeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行第5列数据不存在");
                }
                //获取大学生信息
                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(collegeTrainWeeklyReportExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(collegeTrainAssessed)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                CollegeTrainWeeklyReport newCollegeTrainWeeklyReport = new CollegeTrainWeeklyReport();
                //根据员工id进行数据匹配
                CollegeTrainWeeklyReport collegeTrainWeeklyReport = getByCondition(collegeTrainWeeklyReportExcel, collegeTrainAssessed.getId());
                if (ObjectUtil.isNotEmpty(collegeTrainWeeklyReport)) {
                    newCollegeTrainWeeklyReport.setId(collegeTrainWeeklyReport.getId());
                }
                //封装参数进行更新
                newCollegeTrainWeeklyReport.setAssessedId(collegeTrainAssessed.getId());
                newCollegeTrainWeeklyReport.setWeekly(collegeTrainWeeklyReportExcel.getWeekly());
                newCollegeTrainWeeklyReport.setScore(collegeTrainWeeklyReportExcel.getScore());
                newCollegeTrainWeeklyReport.setJudgeNo(collegeTrainWeeklyReportExcel.getJudgeNo());
                newCollegeTrainWeeklyReport.setJudgeName(judgeEmployeeInfo.getEmployeeName());

                list.add(newCollegeTrainWeeklyReport);
            }
        }
        }
        List<CollegeTrainWeeklyReport> listInsert = new ArrayList<>();
        List<CollegeTrainWeeklyReport> listUpdate = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
           int sum= uploadWorkLicense.getWeeklySum(list.get(i).getAssessedId(),Integer.parseInt(list.get(i).getWeekly()));
           if(sum==0){
               listInsert.add(list.get(i));
           }else {
               listUpdate.add(list.get(i));
           }
        }
        if(listInsert.size()>0){
            for (CollegeTrainWeeklyReport collegeTrainWeeklyReportL:listInsert){
                List<CollegeTrainWeeklyReport> list1 = new ArrayList<>();
                list1.add(collegeTrainWeeklyReportL);
                uploadWorkLicense.insertWeeklyReport(list1);
            }
        }
        if(listUpdate.size()>0){
            for (CollegeTrainWeeklyReport collegeTrainWeeklyReportL:listUpdate){
                List<CollegeTrainWeeklyReport> list1 = new ArrayList<>();
                list1.add(collegeTrainWeeklyReportL);
                uploadWorkLicense.updateWeekly(list1);
            }
        }

        return ResultVO.success("成功导入" + list.size() + "数据");
    }

    @Override
    public JsonPagedVO getPageList(CollegeTrainWeeklyReportRequest request) {
        if (request.getPracticeDepartmentNameList()!=null) {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                list.add(uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i)));
            }

            List<String> factory = new ArrayList<>();
            if (request.getFactoryType() == null || request.getFactoryType().equals("")) {
                for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                    factory.add(uploadWorkLicense.getFactory(request.getPracticeDepartmentNameList().get(i)));
                }
                if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
                    request.setFactoryType("");
                } else if (factory.contains("华天昆山")) {
                    request.setFactoryType("华天昆山");
                } else if (factory.contains("华天江苏")) {
                    request.setFactoryType("华天江苏");
                }
            }
            request.setPracticeDepartmentNameList(list);
        }
        Page<CollegeTrainWeeklyReportVo> page = PageUtils.getPage(request);

        IPage<CollegeTrainWeeklyReportVo> collegeTrainWeeklyReportPage = this.baseMapper.queryPaperList(page,request);
        List<CollegeTrainWeeklyReportVo> records = collegeTrainWeeklyReportPage.getRecords();

/*// 按照工号倒序排列，再按照实习周别进行正序排列
        Collections.sort(records, Comparator.comparing(CollegeTrainWeeklyReportVo::getEmployeeNo, Comparator.reverseOrder())
                .thenComparing(CollegeTrainWeeklyReportVo::getWeekly));
        collegeTrainWeeklyReportPage.setRecords(records);*/
        return JsonPagedVO.success(collegeTrainWeeklyReportPage.getRecords(),Integer.parseInt(collegeTrainWeeklyReportPage.getTotal()+""));

    }

    private CollegeTrainWeeklyReport getByCondition(CollegeTrainWeeklyReportExcel collegeTrainWeeklyReportExcel, Long assessedId) {
        LambdaQueryWrapper<CollegeTrainWeeklyReport> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(CollegeTrainWeeklyReport::getDeletedFlag,Boolean.FALSE)
                .eq(CollegeTrainWeeklyReport::getAssessedId,assessedId)
                .eq(CollegeTrainWeeklyReport::getWeekly,collegeTrainWeeklyReportExcel.getWeekly())
                .eq(CollegeTrainWeeklyReport::getJudgeNo,collegeTrainWeeklyReportExcel.getJudgeNo());
        return getOne(lambdaQuery);
    }
    public class CustomSheetWriteHandler implements SheetWriteHandler {
        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();

            // 创建第一行
            Row row = sheet.createRow(1);

            // 在第一行第一列（0-based index）创建单元格并设置内容
            Cell cell = row.createCell(7);
            cell.setCellValue("导入文档说明:\n" +
                    "(1)红色为必填项:\n" +
                    "(2)学员工号和阅卷师工号必须为人事系统中人员信息中已有的数据，如不是则提示导入失败;\n" +
                    "(3)实习周别和得分必须为正整数，周别为1-10，得分为1-100。");

            // 清除特定的合并区域，以确保第一行内容显示正常
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                if (mergedRegion.getFirstRow() == 0 && mergedRegion.getFirstColumn() == 0) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 输出调试信息
            System.out.println("第一行内容设置完成：" + cell.getStringCellValue());
        }
    }
    public static boolean isPositiveInteger(String str) {
        // 使用正则表达式判断字符串是否只包含数字，并且第一个字符不是0
        return str.matches("[1-9]\\d*");
    }
}
