package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 大学生 异常答辩&满意度评价表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_SATISFACTION_EVALUATION")
@ApiModel(value = "CollegeTrainSatisfactionEvaluation对象", description = "大学生 异常答辩&满意度评价表")
public class CollegeTrainSatisfactionEvaluation implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("评价项目")
    @TableField("CATEGORY")
    private String category;

    @ApiModelProperty("评价标准")
    @TableField("EVALUATE_STANDARD")
    private String evaluateStandard;

    @ApiModelProperty("分值")
    @TableField("SCORE")
    private String score;

    @ApiModelProperty("标准1 100%满分")
    @TableField("STANDARD1")
    private String standard1;

    @ApiModelProperty("标准2 80%-100%")
    @TableField("STANDARD2")
    private String standard2;

    @ApiModelProperty("标准3 60%-80%")
    @TableField("STANDARD3")
    private String standard3;

    @ApiModelProperty("标准4 40%-60%")
    @TableField("STANDARD4")
    private String standard4;

    @ApiModelProperty("标准4 0%-40%")
    @TableField("STANDARD5")
    private String standard5;

    @ApiModelProperty("1:异常答辩评分标准 2：满意度评分表")
    @TableField("TYPE")
    private Integer type;


}
