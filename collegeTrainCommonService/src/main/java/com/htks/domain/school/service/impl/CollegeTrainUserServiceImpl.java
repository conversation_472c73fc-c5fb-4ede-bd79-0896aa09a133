package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainUser;
import com.htks.domain.school.repository.hana.CollegeTrainUserMapper;
import com.htks.domain.school.service.CollegeTrainUserService;
import com.htks.domain.student.repository.hana.CourseStudyRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

/**
 * <p>
 * 岗位认证系统 管理员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainUserServiceImpl extends ServiceImpl<CollegeTrainUserMapper, CollegeTrainUser> implements CollegeTrainUserService {
    @Value("${FileUrl.Preview}")
    private String Preview;
    @Resource
    private CourseStudyRepository studyRepository;
    @Override
    public boolean checkExist(String loginName) {
        LambdaQueryWrapper<CollegeTrainUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CollegeTrainUser::getEmployeeNo, loginName);
        long count = count(wrapper);
        return count > 0 ? true : false;
    }

    @Override
    public CollegeTrainUser getByEmployeeNo(String loginName) {
        LambdaQueryWrapper<CollegeTrainUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CollegeTrainUser::getEmployeeNo, loginName);
        return getOne(wrapper);
    }

    @Override
    public List<CollegeTrainUser> getAllJudge(String userRoleId) {
        LambdaQueryWrapper<CollegeTrainUser> wrapper = Wrappers.lambdaQuery();
        wrapper.apply(userRoleId != null, "LOCATE ( '" + userRoleId + "',ROLE_ID ) > 0");
        return studyRepository.getJudge(userRoleId);
    }

    @Override
    public String handleFile(String filePath) {
        String path = null;
        try {
            // 创建 URL 对象
            URL url = new URL(Preview+filePath);

            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方式为 GET
            connection.setRequestMethod("GET");

            // 读取响应
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // 打印响应内容
           path=response.toString();
            // 关闭连接
            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ("http://10.160.240.150:8012/onlinePreview?url="+path);
    }
}
