package com.htks.domain.school.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 大学生 编写SOP等体系主观题评价表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
public class CollegeTrainSopEvaluationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("考核评价项目")
    private String category;

    @ApiModelProperty("岗位")
    private String post;

    @ApiModelProperty("分值1")
    private String score1;

    @ApiModelProperty("分值2")
    private String score2;

    @ApiModelProperty("分值3")
    private String score3;

    @ApiModelProperty("分值4")
    private String score4;

    @ApiModelProperty("标准1")
    private String standard1;

    @ApiModelProperty("标准2")
    private String standard2;

    @ApiModelProperty("标准3")
    private String standard3;

    @ApiModelProperty("标准4")
    private String standard4;

    @ApiModelProperty("总分")
    private String totalScore;

    @ApiModelProperty("1:SOP评分标准 2：异常实操评分表")
    private Integer type;


}
