package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 课程表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_COURSE")
@ApiModel(value = "CollegeTrainCourse对象", description = "大学生培训系统 课程表")
public class CollegeTrainCourse extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程材料")
    @TableField("ATTACHMENT_ID")
    private Long attachmentId;

    @ApiModelProperty("课程所属分类 比如：HR公开课课程、工艺公开课理论课程、BU公共课理论课程")
    @TableField("CATEGORY")
    private Integer category;

    @ApiModelProperty("课程标题")
    @TableField("TITLE")
    private String title;


}
