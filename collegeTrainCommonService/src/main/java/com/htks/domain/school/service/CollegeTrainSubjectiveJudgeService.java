package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainSubjectiveJudge;
import com.htks.domain.school.request.CollegeTrainSubjectiveJudgeRequest;
import com.htks.web.JsonPagedVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 大学生培训系统 培训组长主观分评价 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainSubjectiveJudgeService extends IService<CollegeTrainSubjectiveJudge> {

    JsonPagedVO getPageList(CollegeTrainSubjectiveJudgeRequest request);

    void downTemplate(HttpServletResponse response) throws IOException;

    void startEvaluation(CollegeTrainSubjectiveJudgeRequest request);

    ResultVO importTemplate(MultipartFile file);
}
