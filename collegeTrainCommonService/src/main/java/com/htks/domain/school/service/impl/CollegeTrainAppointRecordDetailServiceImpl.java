package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.htks.domain.school.dto.CollegeTrainAppointRecordDetail;
import com.htks.domain.school.repository.hana.CollegeTrainAppointRecordDetailMapper;
import com.htks.domain.school.service.CollegeTrainAppointRecordDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 大学生培训系统 评价记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainAppointRecordDetailServiceImpl extends ServiceImpl<CollegeTrainAppointRecordDetailMapper, CollegeTrainAppointRecordDetail> implements CollegeTrainAppointRecordDetailService {

    @Override
    public List<CollegeTrainAppointRecordDetail> getAppointRecordDetail(Long appointRecordId) {
        LambdaQueryWrapper<CollegeTrainAppointRecordDetail> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(CollegeTrainAppointRecordDetail::getAppointRecordId,appointRecordId);
        return list(lambdaQuery);
    }
}
