package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 大学生满意度评价表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_SATISFACTION_DEGREE")
@ApiModel(value = "CollegeTrainSatisfactionDegree对象", description = "大学生培训系统 大学生满意度评价表")
public class CollegeTrainSatisfactionDegree implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ASSESSED_ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @ApiModelProperty("领导关系-领导关怀")
    @TableField("LEADER_CARE")
    private Integer leaderCare;

    @ApiModelProperty("领导关系-座谈会")
    @TableField("LEADER_MEET")
    private Integer leaderMeet;

    @ApiModelProperty("领导关心-每周答疑")
    @TableField("LEADER_QUESTION")
    private Integer leaderQuestion;

    @ApiModelProperty("师傅评价-课程&工作安排")
    @TableField("MASTER_COURSE_ARRANGE")
    private Integer masterCourseArrange;

    @ApiModelProperty("师傅评价-专业能力")
    @TableField("MASTER_MAJOR")
    private Integer masterMajor;

    @ApiModelProperty("师傅评价-耐心负责")
    @TableField("MASTER_PATIENCE")
    private Integer masterPatience;

    @ApiModelProperty("师傅评价-实操教学")
    @TableField("MASTER_PRACTICAL_OPERATION")
    private Integer masterPracticalOperation;

    @ApiModelProperty("总分")
    @TableField("SCORE")
    private Integer score;

    @ApiModelProperty("学习感受-实操")
    @TableField("STUDY_PRACTICAL_OPERATION")
    private Integer studyPracticalOperation;

    @ApiModelProperty("学习感受-报告")
    @TableField("STUDY_REPORT")
    private Integer studyReport;

    @ApiModelProperty("学习感受-理论")
    @TableField("STUDY_THEORY")
    private Integer studyTheory;

    @ApiModelProperty("理论培训-授课时长")
    @TableField("THEORY_COURSE_DURATION")
    private Integer theoryCourseDuration;

    @ApiModelProperty("课程互动")
    @TableField("THEORY_COURSE_INTERACTION")
    private Integer theoryCourseInteraction;

    @ApiModelProperty("理论培训-授课专业性")
    @TableField("THEORY_MAJOR")
    private Integer theoryMajor;



}
