package com.htks.domain.school.repository.hana;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.htks.domain.school.dto.CollegeTrainWeeklyReport;
import com.htks.domain.school.request.CollegeTrainWeeklyReportRequest;
import com.htks.domain.school.vo.CollegeTrainWeeklyReportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 大学生培训系统 周报 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Mapper
public interface CollegeTrainWeeklyReportMapper extends BaseMapper<CollegeTrainWeeklyReport> {

    @Select("<script>" +
            "SELECT ctwr.ID, cta.BATCH ,cta.EMPLOYEE_NO ,cta.EMPLOYEE_NAME ,ctpa.PRACTICE_DEPARTMENT as practiceDepartmentName ," +
            " cta.JOIN_BU_TIME as buTime,cta.FACTORY_FLAG as factoryType,cta.CLASS_NO as classNo, "+
            "ctpa.AREA ,ctwr.WEEKLY ,ctwr.JUDGE_NO ,ctwr.JUDGE_NAME ,ctwr.SCORE ,SUBSTRING(ce.ENTRY_DATE,0,4) AS enterDate,\n" +
            //  " FLOOR(ctwr.WEEKLY / 4) + CASE WHEN MOD(ctwr.WEEKLY, 4) > 0 THEN 1 ELSE 0 END AS appraise \n" +
            "ctwr.EVALUATE_WEEKLY_START||'—'||ctwr.EVALUATE_WEEKLY_END AS appraise\n" +
            "FROM COLLEGE_TRAIN_WEEKLY_REPORT ctwr \n" +
            "LEFT JOIN COLLEGE_TRAIN_ASSESSED cta ON cta.ID = ctwr.ASSESSED_ID \n" +
            "LEFT JOIN COMMON_EMPLOYEE ce ON cta.EMPLOYEE_NO = ce.EMPLOYEE_NUMBER \n" +
            "LEFT JOIN COLLEGE_TRAIN_POST_AREA ctpa ON ctpa.ID = cta.PRACTICE_DEPARTMENT_AREA_ID \n" +
            "WHERE ctwr.DELETED_FLAG = FALSE AND cta.DELETED_FLAG = FALSE \n"+
            "<if test=\"request.batch !=null and request.batch !=''\">  " +
            "    and cta.BATCH = #{request.batch}  " +
            "</if>  " +
            "<if test=\"request.factoryType !=null and request.factoryType !=''\">  " +
            "    and cta.FACTORY_FLAG = #{request.factoryType}  " +
            "</if>  " +
            "<if test=\"request.employeeName !=null and request.employeeName !=''\">  " +
            "    and cta.EMPLOYEE_NAME = #{request.employeeName}  " +
            "</if>  " +
            "<if test=\"request.employeeNo !=null and request.employeeNo !=''\">  " +
            "    and cta.EMPLOYEE_NO = #{request.employeeNo}  " +
            "</if>  " +
            "<if test=\"request.practiceDepartmentNameList !=null and request.practiceDepartmentNameList.size>0 \">  " +
            " and ctpa.PRACTICE_DEPARTMENT in    " +
            "<foreach collection=\"request.practiceDepartmentNameList\" item=\"item\" open=\"(\" close=\")\" separator=\",\">"+
            "#{item}"+
            "</foreach>"+
            "</if>  " +
            "<if test=\"request.practiceDepartmentAreaId !=null and request.practiceDepartmentAreaId !=''\">  " +
            "    and ctpa.id = #{request.practiceDepartmentAreaId}  " +
            "</if>  " +
            "<if test=\"request.enterDateStart !=null and request.enterDateStart !='' \">  " +
            "    and SUBSTRING(ce.ENTRY_DATE,0,4) <![CDATA[>= ]]> #{request.enterDateStart}  " +
            "</if>  " +
            "<if test=\"request.enterDateEnd !=null and request.enterDateEnd !=''\">  " +
            "    and SUBSTRING(ce.ENTRY_DATE,0,4) <![CDATA[<= ]]> #{request.enterDateEnd}  " +
            "</if>  " +
            "<if test=\"request.weekly !=null and  request.weekly.length>0\">  " +
            "and ctwr.WEEKLY in"+
            "<foreach collection=\"request.weekly\" item=\"value\" separator=\",\" open=\"(\" close=\")\">\n" +
            "#{value}  " +
            "</foreach> " +
            "</if>  " +
            "<if test=\"request.judgeNo !=null and request.judgeNo !=''\">  " +
            "    and ctwr.JUDGE_NO = #{request.judgeNo}  " +
            "</if>  " +
            "<if test=\"request.judgeName !=null and request.judgeName !=''\">  " +
            "    and ctwr.JUDGE_Name = #{request.judgeName}  " +
            "</if>  " +
            "<if test=\"request.factoryType !=null and request.factoryType !=''\">  " +
            "    and cta.FACTORY_FLAG = #{request.factoryType}  " +
            "</if>  " +
            "<if test=\"request.classType !=null and request.classType !=''\">  " +
            "    and cta.CLASS_NO like CONCAT('%',#{request.classType}) || '%' " +
            "</if>  " +
            "ORDER BY  cta.EMPLOYEE_NO DESC,  ctwr.WEEKLY ASC \n" +
            "</script>")
    IPage<CollegeTrainWeeklyReportVo> queryPaperList(@Param("page") Page<CollegeTrainWeeklyReportVo> page, @Param("request") CollegeTrainWeeklyReportRequest request);
}
