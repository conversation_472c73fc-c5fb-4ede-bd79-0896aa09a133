package com.htks.domain.school.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 员工状态枚举
 *
 * <AUTHOR>
 * @date 2023/05/09
 */
@Getter
@RequiredArgsConstructor
public enum EmployeeStatusEnum {
    ON_THE_JOB("在职", "在职"),
    LEAVE_WITHOUT_PAY("留职", "留职"),
    DEPART("离职", "离职");


    private final String code;
    private final String info;

    public static EmployeeStatusEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (EmployeeStatusEnum e : EmployeeStatusEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
        }
        return null;
    }

}
