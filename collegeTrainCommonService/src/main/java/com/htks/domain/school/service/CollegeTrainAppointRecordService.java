package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainAppointRecord;
import com.htks.domain.school.request.CollegeTrainAppointRecordRequest;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.web.JsonPagedVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 大学生培训系统 预约记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainAppointRecordService extends IService<CollegeTrainAppointRecord> {

    JsonPagedVO<CollegeTrainAppointRecordVo> queryPaperList(CollegeTrainAppointRecordRequest request);

    void export(CollegeTrainAppointRecordRequest request, HttpServletResponse response);

    ResultVO getAppointRecordDetail(Long appointRecordId);
    ResultVO backAppointRecode(Long appointRecordId);
}
