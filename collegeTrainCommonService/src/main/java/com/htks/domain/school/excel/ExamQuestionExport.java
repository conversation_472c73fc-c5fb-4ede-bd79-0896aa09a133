package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.htks.common.convert.Convert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 大学生 考试记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class ExamQuestionExport extends Convert {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号",index = 0)
    private int Number;

    @ApiModelProperty("题目详情")
    @ExcelProperty(value = "题目详情",index = 1)
    private String questionContent;

    @ApiModelProperty("试题类型")
    @ExcelProperty(value = "试题类型",index = 2)
    private String questionType;

    @ApiModelProperty("选项A")
    @ExcelProperty(value = "选项A",index = 3)
    private String optionA;

    @ApiModelProperty("选项B")
    @ExcelProperty(value = "选项B",index = 4)
    private String optionB;

    @ApiModelProperty("选项C")
    @ExcelProperty(value = "选项C",index = 5)
    private String optionC;

    @ApiModelProperty("选项D")
    @ExcelProperty(value = "选项D",index = 6)
    private String optionD;

    @ApiModelProperty("选项E")
    @ExcelProperty(value = "选项E",index = 7)
    private String optionE;

    @ApiModelProperty("选项F")
    @ExcelProperty(value = "选项F",index = 8)
    private String optionF;

    @ApiModelProperty("标准答案")
    @ExcelProperty(value = "标准答案",index = 9)
    private String standardAnswer;

}
