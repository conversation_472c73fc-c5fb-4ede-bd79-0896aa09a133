package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.common.exception.BaseException;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.request.CollegeTrainAssessedRequest;
import com.htks.domain.school.request.CollegeTrainEnabledRequest;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.vo.CollegeTrainAssessedAreaVo;
import com.htks.domain.school.vo.CollegeTrainAssessedVo;
import com.htks.web.JsonPagedVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainAssessedService extends IService<CollegeTrainAssessed> {

    /**
     * 检查存在
     *
     * @param loginName 登录名
     * @return boolean
     */
    boolean checkExist(String loginName);

    /**
     * 检查存在
     *
     * @param loginName 登录名
     * @return boolean
     */
    boolean checkExist2(String loginName);

    /**
     * 通过员工工号获取用户
     *
     * @param employeeNumber 员工数量
     * @return {@link CollegeTrainAssessed}
     */
    CollegeTrainAssessed getByEmployeeNo(String employeeNumber);

    /**
     * 下载模板
     */
    void downloadTemplate(HttpServletResponse response) throws IOException;

    /**
     * 添加大学生信息
     *
     * @param collegeTrainAssessed 学院培训评估
     */
    ResultVO addCollegeTrainAssessed(CollegeTrainAssessed collegeTrainAssessed);

    /**
     * 更新大学生信息
     *
     * @param collegeTrainAssessed 学院培训评估
     * @return {@link ResultVO}
     */
    ResultVO updateCollegeTrainAssessed(CollegeTrainAssessed collegeTrainAssessed);

    ResultVO importTemplate(MultipartFile file) throws IOException;

    JsonPagedVO<CollegeTrainAssessedVo> queryPaperList(CollegeTrainAssessedRequest request);

    ResultVO getSpecifiedColumn(String column);

    List<CollegeTrainAssessedAreaVo> getByAssessedIdList(List<Long> assessedIdList);

    ResultVO updateEnabled(CollegeTrainEnabledRequest request);

    void export(CollegeTrainAssessedRequest request, HttpServletResponse response);
}
