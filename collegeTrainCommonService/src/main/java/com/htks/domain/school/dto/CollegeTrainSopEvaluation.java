package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.common.convert.Convert;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 大学生 编写SOP等体系主观题评价表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_SOP_EVALUATION")
@ApiModel(value = "CollegeTrainSopEvaluation对象", description = "大学生 编写SOP等体系主观题评价表")
public class CollegeTrainSopEvaluation extends Convert implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("考核评价项目")
    @TableField("CATEGORY")
    private String category;

    @ApiModelProperty("岗位")
    @TableField("POST")
    private String post;

    @ApiModelProperty("分值1")
    @TableField("SCORE1")
    private String score1;

    @ApiModelProperty("分值2")
    @TableField("SCORE2")
    private String score2;

    @ApiModelProperty("分值3")
    @TableField("SCORE3")
    private String score3;

    @ApiModelProperty("分值4")
    @TableField("SCORE4")
    private String score4;

    @ApiModelProperty("标准1")
    @TableField("STANDARD1")
    private String standard1;

    @ApiModelProperty("标准2")
    @TableField("STANDARD2")
    private String standard2;

    @ApiModelProperty("标准3")
    @TableField("STANDARD3")
    private String standard3;

    @ApiModelProperty("标准4")
    @TableField("STANDARD4")
    private String standard4;

    @ApiModelProperty("总分")
    @TableField("TOTAL_SCORE")
    private String totalScore;

    @ApiModelProperty("1:SOP评分标准 2：异常实操评分表")
    @TableField("TYPE")
    private Integer type;


}
