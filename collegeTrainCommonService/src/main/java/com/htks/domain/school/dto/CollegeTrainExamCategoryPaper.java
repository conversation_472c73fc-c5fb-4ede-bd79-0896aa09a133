package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大学生 试卷表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_EXAM_CATEGORY_PAPER")
@ApiModel(value = "CollegeTrainExamCategoryPaper对象", description = "大学生 试卷表")
public class CollegeTrainExamCategoryPaper extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("部门")
    @TableField("DEPARTMENT")
    private String department;

    @ApiModelProperty("培训考试项目ID")
    @TableField("EXAM_CATEGORY_ID")
    private Long examCategoryId;

    @ApiModelProperty("试卷名称")
    @TableField("PAPER_NAME")
    private String paperName;

    @TableField("UPDATED_USER")
    private String updatedUser;


}
