package com.htks.domain.school.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.utils.PageUtils;
import com.htks.domain.school.dto.CollegeTrainPostCard;
import com.htks.domain.school.repository.hana.CollegeTrainPostCardMapper;
import com.htks.domain.school.request.CollegeTrainPostCardRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainPostCardService;
import com.htks.domain.school.vo.CollegeTrainAssessedAreaVo;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.domain.school.vo.CollegeTrainPostCardGroupVo;
import com.htks.domain.school.vo.CollegeTrainPostCardVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 大学生培训系统 上岗证 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainPostCardServiceImpl extends ServiceImpl<CollegeTrainPostCardMapper, CollegeTrainPostCard> implements CollegeTrainPostCardService {
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @Override
    public JsonPagedVO<CollegeTrainPostCardVo> queryPaperList(CollegeTrainPostCardRequest request) {
        if (request.getPracticeDepartmentNameList()!=null&&request.getPracticeDepartmentNameList().size()>0) {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                list.add(uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i)));
            }request.setFactoryFlag(uploadWorkLicense.getFactory(request.getPracticeDepartmentNameList().get(0)));
            request.setPracticeDepartmentNameList(list);
        }
        Page<CollegeTrainPostCardVo> page = PageUtils.getPage(request);

        IPage<CollegeTrainPostCardVo> collegeTrainPostCardVoPage = this.baseMapper.queryPaperList(page,request);
        List<CollegeTrainPostCardVo> list = collegeTrainPostCardVoPage.getRecords();
        if (ObjectUtil.isNotEmpty(list)){
            List<Long> assessedIdList = list.stream().map(CollegeTrainPostCardVo::getAssessedId).collect(Collectors.toList());
            List<CollegeTrainAssessedAreaVo> collegeTrainAssessedAreaVoList = collegeTrainAssessedService.getByAssessedIdList(assessedIdList);
            List<CollegeTrainPostCardGroupVo> postCardList = getByAssessedIdList(assessedIdList);
            Map<Long, CollegeTrainAssessedAreaVo> assessedAreaMap = collegeTrainAssessedAreaVoList.stream().collect(Collectors.toMap(CollegeTrainAssessedAreaVo::getId, Function.identity()));
            Map<Long, List<CollegeTrainPostCardGroupVo>> postcardMap = postCardList.stream().collect(Collectors.groupingBy(CollegeTrainPostCardGroupVo::getAssessedId));

            for (CollegeTrainPostCardVo collegeTrainPostCardVo : list) {
                CollegeTrainAssessedAreaVo collegeTrainAssessedAreaVo = assessedAreaMap.get(collegeTrainPostCardVo.getAssessedId());
                List<CollegeTrainPostCardGroupVo> cardList = postcardMap.get(collegeTrainPostCardVo.getAssessedId());
                Map<String, Integer> collect = cardList.stream().collect(Collectors.toMap(CollegeTrainPostCardGroupVo::getPostArea, CollegeTrainPostCardGroupVo::getCount));

                collegeTrainPostCardVo.setBatch(collegeTrainAssessedAreaVo.getBatch());
                collegeTrainPostCardVo.setEmployeeNo(collegeTrainAssessedAreaVo.getEmployeeNo());
                collegeTrainPostCardVo.setEmployeeName(collegeTrainAssessedAreaVo.getEmployeeName());
                collegeTrainPostCardVo.setPracticeDepartmentName(collegeTrainAssessedAreaVo.getPracticeDepartmentName());
                collegeTrainPostCardVo.setArea(collegeTrainAssessedAreaVo.getArea());
                collegeTrainPostCardVo.setPostCertificateStartTime(collegeTrainAssessedAreaVo.getPostCertificateStartTime());
                collegeTrainPostCardVo.setPostCertificateEndTime(collegeTrainAssessedAreaVo.getPostCertificateEndTime());
                collegeTrainPostCardVo.setAreaCount(ObjectUtil.isNotEmpty(collect.get(collegeTrainAssessedAreaVo.getArea())) ? collect.get(collegeTrainAssessedAreaVo.getArea()) : 0);
                collegeTrainPostCardVo.setAllCount(collect.values().stream().mapToInt(Integer::intValue).sum());
                int score = 0;
                if (collegeTrainPostCardVo.getAreaCount() >= 5) {
                    score = 100 + (collegeTrainPostCardVo.getAllCount() - 5);
                } else if (collegeTrainPostCardVo.getAreaCount() < 5 && collegeTrainPostCardVo.getAllCount() > 5) {
                    score = collegeTrainPostCardVo.getAreaCount() * 20 + (collegeTrainPostCardVo.getAllCount() - 5);
                }else {
                    score = collegeTrainPostCardVo.getAreaCount() * 20;
                }
                collegeTrainPostCardVo.setScore(score);
            }
        }
        return JsonPagedVO.success(collegeTrainPostCardVoPage.getRecords(),Integer.parseInt(collegeTrainPostCardVoPage.getTotal()+""));
    }

    private List<CollegeTrainPostCardGroupVo> getByAssessedIdList(List<Long> assessedIdList) {
        return this.baseMapper.getByAssessedIdList(assessedIdList);
    }
}
