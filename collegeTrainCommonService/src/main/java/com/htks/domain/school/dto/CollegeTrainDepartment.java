package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 部门区域对应表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_POST_AREA")
@ApiModel(value = "CollegeTrainDepartment", description = "部门区域对应表")
public class CollegeTrainDepartment implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("实习部门")
    @TableField("PRACTICE_DEPARTMENT")
    private String practiceDepartment;


    @ApiModelProperty("厂别：华天昆山、华天江苏")
    @TableField("FACTORY_FLAG")
    private String factoryFlag;
//    @ApiModelProperty("工程主管")
//    @TableField("PROJECT_LEADER")
//    private String projectLeader;
//
//    @ApiModelProperty("质量主管")
//    @TableField("QUALITY_LEADER")
//    private String qualityLeader;


}
