package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 大学生 考卷问题选项 - 细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_QUESTION_ITEM")
@ApiModel(value = "CollegeTrainQuestionItem对象", description = "大学生 考卷问题选项 - 细表")
public class CollegeTrainQuestionItem implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("选项内容")
    @TableField("ITEM_CONTENT")
    private String itemContent;

    @ApiModelProperty("选项")
    @TableField("ITEM_OPTION")
    private String itemOption;

    @ApiModelProperty("主表id")
    @TableField("QUESTION_ID")
    private Long questionId;


}
