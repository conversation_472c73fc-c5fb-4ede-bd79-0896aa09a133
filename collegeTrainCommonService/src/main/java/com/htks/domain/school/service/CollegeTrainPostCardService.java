package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.domain.school.dto.CollegeTrainPostCard;
import com.htks.domain.school.request.CollegeTrainPostCardRequest;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.domain.school.vo.CollegeTrainPostCardVo;
import com.htks.web.JsonPagedVO;

/**
 * <p>
 * 大学生培训系统 上岗证 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainPostCardService extends IService<CollegeTrainPostCard> {

    JsonPagedVO<CollegeTrainPostCardVo> queryPaperList(CollegeTrainPostCardRequest request);
}
