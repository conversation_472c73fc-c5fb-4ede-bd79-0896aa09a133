package com.htks.domain.school.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.common.convert.Convert;
import com.htks.common.convert.Search;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/15 15:47
 */
@Data
public class CollegeTrainMeetVo extends Convert {
    private Long id;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("平均得分")
    private BigDecimal averageScore;

    @ApiModelProperty("见面会日期")
    private String meetDate;
}
