package com.htks.domain.school.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 16:37
 */
@Data
public class CollegeTrainSopUploadVo {

    private Long id;

    @ApiModelProperty("附件地址")
    private String attachmentPath;

    @ApiModelProperty("批阅理由")
    private String evaluateReason;

    @ApiModelProperty("评价状态 已评价：未评价")
    private String evaluateStatus;

    @ApiModelProperty("阅卷师姓名")
    private String graderName;

    @ApiModelProperty("阅卷师工号")
    private String graderNo;

    @ApiModelProperty("批阅编号")
    private String gradingNumber;

    @ApiModelProperty("得分")
    private Integer score;

    @ApiModelProperty("SOP名称")
    private String sopName;

    @ApiModelProperty("1:sop上传 2：读后感上传")
    private Integer type;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门名称")
    private String practiceDepartmentName;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("文件名字")
    private String fileName;
}
