package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 大学生 考试记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_EXAM_RECORD")
@ApiModel(value = "CollegeTrainExamRecord对象", description = "大学生 考试记录")
public class CollegeTrainExamRecord extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分配状态 已分配：未分配")
    @TableField("ALLOCATION_STATUS")
    private String allocationStatus;

    @ApiModelProperty("COLLEGE_TRAIN_ASSESSED.ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @ApiModelProperty("考试结束时间")
    @TableField("EXAM_END_TIME")
    private String examEndTime;

    @ApiModelProperty("考试名称")
    @TableField("EXAM_NAME")
    private String examName;

    @ApiModelProperty("考试编号")
    @TableField("EXAM_NUMBER")
    private String examNumber;

    @ApiModelProperty("考试开始时间")
    @TableField("EXAM_START_TIME")
    private String examStartTime;

    @TableField("JUDGE_NAME")
    private String judgeName;

    @ApiModelProperty("阅卷师工号")
    @TableField("JUDGE_NO")
    private String judgeNo;

    @ApiModelProperty("阅卷编号")
    @TableField("JUDGE_NUMBER")
    private String judgeNumber;

    @ApiModelProperty("客观题得分")
    @TableField("OBJECTIVE_SCORE")
    private String objectiveScore;

    @ApiModelProperty("主观题得分")
    @TableField("SUBJECTIVE_SCORE")
    private BigDecimal subjectiveScore;

    @ApiModelProperty("总分")
    @TableField("TOTAL_SCORE")
    private String totalScore;

    @TableField("UPDATED_USER")
    private String updatedUser;

    @TableField("EXAM_CATEGORY_ID")
    private Long examCategoryId;

    @TableField("PAPER_NAME")
    @ApiModelProperty("试卷名称")
    private String paperName;

    @TableField("EXAM_TYPE")
    @ApiModelProperty("考试类型")
    private String examType;

    @TableField("EVALUATE_REASON")
    @ApiModelProperty("评分理由")
    private String evaluateReason;
/*    @TableField("note")
    @ApiModelProperty("评分理由")
    private String note;*/

  /*  @ApiModelProperty("考试状态")
    private String examStatus;*/

}
