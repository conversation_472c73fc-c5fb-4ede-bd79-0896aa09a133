package com.htks.domain.school.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.DateUtils;
import com.htks.common.utils.EasyExcelUtils;
import com.htks.common.utils.JudgeNumberUtils;
import com.htks.common.utils.PageUtils;
import com.htks.common.utils.SheetUtil;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.dto.CollegeTrainMeet;
import com.htks.domain.school.dto.CollegeTrainMeetDetail;
import com.htks.domain.school.dto.CollegeTrainSopUpload;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.enums.EmployeeStatusEnum;
import com.htks.domain.school.excel.CollegeTrainEngineeringExamRecordExcel;
import com.htks.domain.school.excel.CollegeTrainMeetExcel;
import com.htks.domain.school.excel.CollegeTrainSopUploadExcel;
import com.htks.domain.school.repository.hana.CollegeTrainMeetMapper;
import com.htks.domain.school.request.CollegeTrainMeetRequest;
import com.htks.domain.school.request.CollegeTrainSopUploadRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainMeetDetailService;
import com.htks.domain.school.service.CollegeTrainMeetService;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.domain.school.vo.CollegeTrainMeetVo;
import com.htks.domain.school.vo.CollegeTrainSopUploadVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.checkerframework.checker.units.qual.min;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 大学生培训系统 见面会 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainMeetServiceImpl extends ServiceImpl<CollegeTrainMeetMapper, CollegeTrainMeet> implements CollegeTrainMeetService {
    @Resource
    private CommonEmployeeService commonEmployeeService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @Resource
    private CollegeTrainMeetDetailService collegeTrainMeetDetailService;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @Override
    public void downTemplate(HttpServletResponse response) throws IOException {
        ArrayList<CollegeTrainMeetExcel> list = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            list.get(i).setNo(String.valueOf(i+1));
        }
        LinkedHashMap<Integer, List<String>> fieldValues = new LinkedHashMap<>();

        SheetUtil sheetUtil = new SheetUtil(fieldValues);
        CustomSheetWriteHandler customSheetWriteHandler = new CustomSheetWriteHandler();

        String fileName = URLEncoder.encode("见面会评价模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        EasyExcel.write(response.getOutputStream(), CollegeTrainMeetExcel.class)
                .registerWriteHandler(new CustomSheetWriteHandler())
                .registerWriteHandler(customSheetWriteHandler)  // 注册自定义监听器
                .sheet("见面会评价").registerWriteHandler(sheetUtil).doWrite(list);
    }

    @Override
    public ResultVO importTemplate(MultipartFile file) {
        List<CollegeTrainMeetExcel> collegeTrainMeetExcelList = null;
        try {
            collegeTrainMeetExcelList = EasyExcelUtils.readExcel(file, CollegeTrainMeetExcel.class);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultVO.error("文件解析错误");
        }
        List<CollegeTrainMeetExcel> remove = new ArrayList<>();

        for (CollegeTrainMeetExcel collegeTrainMeetExcel: collegeTrainMeetExcelList) {
            if (collegeTrainMeetExcel.getNo()==null&&collegeTrainMeetExcel.getEmployeeNo()==null&&collegeTrainMeetExcel.getMeetDate()==null&&collegeTrainMeetExcel.getJudgeNo()==null&&collegeTrainMeetExcel.getScore()==null){
                remove.add(collegeTrainMeetExcel);
            }
        }
        collegeTrainMeetExcelList.removeAll(remove);
        int a = -1;
        for  (int i = 0; i < collegeTrainMeetExcelList.size(); i++) {
            CollegeTrainMeetExcel collegeTrainEngineeringExamRecordExcel = collegeTrainMeetExcelList.get(i);
            if (collegeTrainEngineeringExamRecordExcel.getNo()==null){

            }else {
                if (collegeTrainEngineeringExamRecordExcel.getNo().contains("导入文档说明")) {
                    a = i;
                    break;
                }
            }
        }
        if (a!=-1&&a < collegeTrainMeetExcelList.size()) {
            collegeTrainMeetExcelList.subList(a, collegeTrainMeetExcelList.size()).clear();
        }
        if (ObjectUtil.isNotEmpty(collegeTrainMeetExcelList)) {
            for (int i = 0; i < collegeTrainMeetExcelList.size(); i++) {
                CollegeTrainMeetExcel collegeTrainMeetExcel = collegeTrainMeetExcelList.get(i);
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainMeetExcel.getEmployeeNo(), "第" + (i + 2) + "行学员工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainMeetExcel.getMeetDate(), "第" + (i + 2) + "行见面会日期不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainMeetExcel.getJudgeNo(), "第" + (i + 2) + "行评委工号不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainMeetExcel.getJudgeName(), "第" + (i + 2) + "行评委姓名不能为空!");
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainMeetExcel.getScore(), "第" + (i + 2) + "行评委评分不能为空!");
                if (!DateUtils.isValidDate(collegeTrainMeetExcel.getMeetDate())) {
                    return ResultVO.error("第" + (i + 2) + "行见面会日期格式错误");
                }
                //获取员工信息
                CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainMeetExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(employeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }
                //获取评委信息,必须是在职状态
                CommonEmployee judgeEmployeeInfo = commonEmployeeService.getEmployeeInfo(collegeTrainMeetExcel.getJudgeNo(), EmployeeStatusEnum.ON_THE_JOB.getCode());
                if (ObjectUtil.isEmpty(judgeEmployeeInfo)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }

                //获取大学生信息
                CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(collegeTrainMeetExcel.getEmployeeNo());
                if (ObjectUtil.isEmpty(collegeTrainAssessed)) {
                    return ResultVO.error("第" + (i + 2) + "行未找到与填写学员工号或阅卷师工号匹配的人员");
                }

                collegeTrainMeetExcel.setJudgeName(judgeEmployeeInfo.getEmployeeName());
                collegeTrainMeetExcel.setEmployeeName(employeeInfo.getEmployeeName());
                collegeTrainMeetExcel.setAssessedId(collegeTrainAssessed.getId());
            }
            Map<String, List<CollegeTrainMeetExcel>> map = collegeTrainMeetExcelList.stream().collect(Collectors.groupingBy(CollegeTrainMeetExcel::getEmployeeNo));
            map.keySet().forEach(o -> {
                List<CollegeTrainMeetExcel> collegeTrainMeetExcelList1 = map.get(o);
                CollegeTrainMeetExcel collegeTrainMeetExcel = collegeTrainMeetExcelList1.get(0);
                List<BigDecimal> scoreList = collegeTrainMeetExcelList1.stream().map(CollegeTrainMeetExcel::getScore).collect(Collectors.toList());
                Long meetId;
                String type = "add";
                CollegeTrainMeet dbCollegeTrainMeet = getByCondition(collegeTrainMeetExcel.getAssessedId());
                if (ObjectUtil.isNotEmpty(dbCollegeTrainMeet)){
                    CollegeTrainMeet collegeTrainMeet = new CollegeTrainMeet();
                    collegeTrainMeet.setId(dbCollegeTrainMeet.getId());
                    collegeTrainMeet.setMeetDate(collegeTrainMeetExcel.getMeetDate());
                    collegeTrainMeet.setAverageScore(average(scoreList));
                    meetId = dbCollegeTrainMeet.getId();
                    type = "update";
                    updateById(collegeTrainMeet);
                }else {
                    CollegeTrainMeet collegeTrainMeet = new CollegeTrainMeet();
                    collegeTrainMeet.setAssessedId(collegeTrainMeetExcel.getAssessedId());
                    collegeTrainMeet.setMeetDate(collegeTrainMeetExcel.getMeetDate());
                    collegeTrainMeet.setAverageScore(average(scoreList));
                    save(collegeTrainMeet);
                    meetId = collegeTrainMeet.getId();
                }

                List<CollegeTrainMeetDetail> collect = collegeTrainMeetExcelList1.stream().map(p -> {
                    CollegeTrainMeetDetail collegeTrainMeetDetail = new CollegeTrainMeetDetail();
                    collegeTrainMeetDetail.setMeetId(meetId);
                    collegeTrainMeetDetail.setScore(p.getScore());
                    collegeTrainMeetDetail.setJudgeNo(p.getJudgeNo());
                    collegeTrainMeetDetail.setJudgeName(p.getJudgeName());
                    return collegeTrainMeetDetail;
                }).collect(Collectors.toList());
                collegeTrainMeetDetailService.saveOrUpdateMeetDetail(collect, meetId, type);
            });
        }

        return ResultVO.success("成功导入" + collegeTrainMeetExcelList.size() + "数据");
    }

    private CollegeTrainMeet getByCondition(Long assessedId) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(assessedId);
        LambdaQueryWrapper<CollegeTrainMeet> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CollegeTrainMeet::getAssessedId,assessedId);
        return getOne(queryWrapper);
    }

    @Override
    public ResultVO getMeetDetail(Long meetId) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(meetId);
        LambdaQueryWrapper<CollegeTrainMeetDetail> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(CollegeTrainMeetDetail::getMeetId,meetId)
                .eq(CollegeTrainMeetDetail::getDeletedFlag,Boolean.FALSE);
        List<CollegeTrainMeetDetail> list = collegeTrainMeetDetailService.list(queryWrapper);
        return ResultVO.success(list);
    }

    @Override
    public JsonPagedVO<CollegeTrainMeetVo> queryPaperList(CollegeTrainMeetRequest request) {
        if (request.getPracticeDepartmentNameList()!=null) {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < request.getPracticeDepartmentNameList().size(); i++) {
                list.add(uploadWorkLicense.getDepartmentName(request.getPracticeDepartmentNameList().get(i)));
            }

            List<String> factory = new ArrayList<>();

            request.setPracticeDepartmentNameList(list);
        }
        Page<CollegeTrainMeetVo> page = PageUtils.getPage(request);
        IPage<CollegeTrainMeetVo> collegeTrainMeetVoPage = this.baseMapper.queryPaperList(page,request);
        return JsonPagedVO.success(collegeTrainMeetVoPage.getRecords(),Integer.parseInt(collegeTrainMeetVoPage.getTotal()+""));
    }

    public BigDecimal average(List<BigDecimal> list) {
        BigDecimal max = list.get(0);
        for (BigDecimal item : list) {
            if (item.compareTo(max) > 0){
                max = item;//找出最大值
            }
        }
        BigDecimal min = list.get(0);
        for (BigDecimal item : list) {
            if (item.compareTo(min) < 0) {
                min = item;//找出最小值
            }
        }
        BigDecimal sum = new BigDecimal(0);
        for (BigDecimal item : list) {
            sum = sum.add(item);//数组所有元素之和
        }
        return (sum.subtract(max).subtract(min)).divide(new BigDecimal(list.size() - 2))
                .setScale(1, BigDecimal.ROUND_DOWN);
    }
    public class CustomSheetWriteHandler implements SheetWriteHandler {
        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            Row row = sheet.getRow(20);  // 获取第十行
            if (row == null) {
                row = sheet.createRow(20);
            }
            Cell cell = row.createCell(0);  // 获取第一列
            cell.setCellValue(
                    "导入文档说明:\n" +
                            "(1)红色为必填项:\n" +
                            "(2)学员工号必须为人事系统中人员信息中已有的在职的数据，如不是则提示导入失败;\n" +
                            "(3)见面会日期必须为文本格式，比如2023-04-12。");
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                if (mergedRegion.getFirstRow() == 20 && mergedRegion.getFirstColumn() == 0 && mergedRegion.getLastColumn() == 6) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 合并第十行的第1到第5列
            sheet.addMergedRegion(new CellRangeAddress(
                    20, // 第十行
                    26, // 第十行
                    0, // 第一列
                    6  // 第五列
            ));
        }
    }
}
