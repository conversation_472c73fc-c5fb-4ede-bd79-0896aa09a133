package com.htks.domain.school.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.htks.common.convert.Search;
import com.htks.domain.AbstractPaginationQueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/10 9:53
 */
@Data
public class CollegeTrainAssessedRequest extends Search {

    private Long id;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("选岗部门名称")
    private List<String> chooseDepartmentNameList;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("起始入职时间")
    private String entryDateStart;

    @ApiModelProperty("终止入职时间")
    private String entryDateEnd;

    @ApiModelProperty("实习部门名称")
    private List<String> practiceDepartmentNameList;

    @ApiModelProperty("如果选了区域,直接给对应配置主键")
    private  List<String> practiceDepartmentAreaId;

    @ApiModelProperty("厂别：华天昆山、华天江苏")
    private List<String> factoryFlagList;

    @ApiModelProperty("班别")
    private String classNo;

    @ApiModelProperty("状态：开启、关闭")
    private List<String> enabledList;
}
