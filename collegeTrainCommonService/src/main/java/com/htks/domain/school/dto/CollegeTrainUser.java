package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 岗位认证系统 管理员表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_USER")
@ApiModel(value = "CollegeTrainUser对象", description = "岗位认证系统 管理员表")
public class CollegeTrainUser extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("员工姓名")
    @TableField("EMPLOYEE_NAME")
    private String employeeName;

    @ApiModelProperty("员工工号")
    @TableField("EMPLOYEE_NO")
    private String employeeNo;

    @ApiModelProperty("是否启用")
    @TableField("ENABLED")
    private String enabled;

    @ApiModelProperty("角色:管理员100,阅卷师101,培训组组长102")
    @TableField("ROLE_ID")
    private String roleId;

    @TableField("UPDATED_USER")
    private String updatedUser;

    @TableField("ZN_ID")
    private Long znId;


}
