package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainSopEvaluation;
import com.htks.domain.school.repository.hana.CollegeTrainSopEvaluationMapper;
import com.htks.domain.school.service.CollegeTrainSopEvaluationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 大学生 编写SOP等体系主观题评价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainSopEvaluationServiceImpl extends ServiceImpl<CollegeTrainSopEvaluationMapper, CollegeTrainSopEvaluation> implements CollegeTrainSopEvaluationService {

    @Override
    public ResultVO getList(Integer type) {
        LambdaQueryWrapper<CollegeTrainSopEvaluation> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(CollegeTrainSopEvaluation::getType,type);
        List<CollegeTrainSopEvaluation> list = list(lambdaQuery);
        List<CollegeTrainSopEvaluation> collect = list.stream().map(o -> o.convert(CollegeTrainSopEvaluation.class)).collect(Collectors.toList());
        return ResultVO.success(collect);
    }
}
