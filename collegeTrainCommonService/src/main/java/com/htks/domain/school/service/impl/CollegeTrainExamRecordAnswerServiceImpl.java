package com.htks.domain.school.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.htks.domain.school.dto.CollegeTrainExamRecordAnswer;
import com.htks.domain.school.enums.QuestionTypeEnum;
import com.htks.domain.school.repository.hana.CollegeTrainExamRecordAnswerMapper;
import com.htks.domain.school.request.CollegeTrainExamRecordAnswerRequest;
import com.htks.domain.school.service.CollegeTrainExamRecordAnswerService;
import com.htks.domain.school.vo.CollegeTrainExamRecordAnswerVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 岗位认证系统 考试记录答案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class CollegeTrainExamRecordAnswerServiceImpl extends ServiceImpl<CollegeTrainExamRecordAnswerMapper, CollegeTrainExamRecordAnswer> implements CollegeTrainExamRecordAnswerService {

    @Override
    public List<CollegeTrainExamRecordAnswerVo> getExamRecordAnswer(Long examId,String questionType) {
        List<CollegeTrainExamRecordAnswerVo> list = this.baseMapper.getExamRecordAnswer(examId,questionType);
        for (CollegeTrainExamRecordAnswerVo collegeTrainExamRecordAnswerVo : list) {
            if (QuestionTypeEnum.SINGLE_OPTION.getCode().equals(collegeTrainExamRecordAnswerVo.getQuestionType())) {
                collegeTrainExamRecordAnswerVo.setPoints(collegeTrainExamRecordAnswerVo.getSingleOptionAnswer());
            }
            if (QuestionTypeEnum.MORE_OPTION.getCode().equals(collegeTrainExamRecordAnswerVo.getQuestionType())) {
                collegeTrainExamRecordAnswerVo.setPoints(collegeTrainExamRecordAnswerVo.getMoreOptionAnswer());
            }
            if (QuestionTypeEnum.JUDGE.getCode().equals(collegeTrainExamRecordAnswerVo.getQuestionType())) {
                collegeTrainExamRecordAnswerVo.setPoints(collegeTrainExamRecordAnswerVo.getJudgeAnswer());
            }
            if (QuestionTypeEnum.FILL.getCode().equals(collegeTrainExamRecordAnswerVo.getQuestionType())) {
                collegeTrainExamRecordAnswerVo.setPoints(collegeTrainExamRecordAnswerVo.getFillAnswer());
            }
            if (QuestionTypeEnum.SHORT.getInfo().equals(collegeTrainExamRecordAnswerVo.getQuestionType())) {
                int a=10;
                collegeTrainExamRecordAnswerVo.setShortAnswer(BigDecimal.valueOf(a));
                collegeTrainExamRecordAnswerVo.setPoints(collegeTrainExamRecordAnswerVo.getShortAnswer());
            }
        }
        return list;
    }

    @Override
    public void updateExamRecordAnswer(List<CollegeTrainExamRecordAnswerRequest> examRecordAnswerList) {
        List<CollegeTrainExamRecordAnswer> collect = examRecordAnswerList.stream().map(o -> o.convert(CollegeTrainExamRecordAnswer.class)).collect(Collectors.toList());
        updateBatchById(collect);
    }
}
