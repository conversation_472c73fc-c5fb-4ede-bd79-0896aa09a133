package com.htks.domain.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.htks.domain.school.dto.CollegeTrainDepartment;
import com.htks.domain.school.dto.CollegeTrainPostArea;

import java.util.List;

/**
 * <p>
 * 部门区域对应表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface CollegeTrainPostAreaService extends IService<CollegeTrainPostArea> {

    /**
     * 得到所有部门
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getAllDepartment(String factoryFlag);
    List<CollegeTrainDepartment>getCollegeTrainDepartment();

    /**
     * 通过部门获取区域
     *
     * @param department 部门
     * @return {@link List}<{@link CollegeTrainPostArea}>
     */
    List<CollegeTrainPostArea> getAreaByDepartment(String department,String employeeNo);

    /**
     * 通过部门和区域获取数据
     *
     * @param practiceDepartment 部门
     * @param area             地区
     * @return {@link CollegeTrainPostArea}
     */
    CollegeTrainPostArea getByDepartmentAndArea(String practiceDepartment, String area,String factoryFlag);

    List<CollegeTrainPostArea> getByDepartmentList(List<String> practiceDepartmentNameList);
    List<CollegeTrainPostArea> getByDepartmentListNew(List<String> practiceDepartmentNameList,List<String>factoryFlagList);

    List<CollegeTrainPostArea> getByAreaList(List<String> areaList);

    List<CollegeTrainPostArea> getByDepartmentAndAreaList(List<String> practiceDepartmentNameList, List<String> areaList);
}
