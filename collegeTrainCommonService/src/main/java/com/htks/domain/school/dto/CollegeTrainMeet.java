package com.htks.domain.school.dto;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.htks.domain.school.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 大学生培训系统 见面会
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("COLLEGE_TRAIN_MEET")
@ApiModel(value = "CollegeTrainMeet对象", description = "大学生培训系统 见面会")
@KeySequence(value = "COLLEGE_TRAIN_MEET_SEQ",dbType = DbType.SAP_HANA)
public class CollegeTrainMeet implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("大学生信息配置表COLLEGE_TRAIN_ASSESSED.ASSESSED_ID")
    @TableField("ASSESSED_ID")
    private Long assessedId;

    @ApiModelProperty("平均得分")
    @TableField("AVERAGE_SCORE")
    private BigDecimal averageScore;

    @TableField("CREATED_TIME")
    private Date createdTime;

    @ApiModelProperty("见面会日期")
    @TableField("MEET_DATE")
    private String meetDate;


}
