package com.htks.domain.school.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 17:02
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(25)
public class CollegeTrainSopUploadExcel {
    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号",index = 0)
    @HeadFontStyle(color = 0)
    private String number;

    @ApiModelProperty("学员姓名")
    @ExcelProperty(value = "学员姓名",index = 1)
    private String employeeName;

    @ApiModelProperty("学员工号")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "学员工号",index = 2)
    private String employeeNo;

    @ApiModelProperty("阅卷师工号")
    @HeadFontStyle(color = 10)
    @ExcelProperty(value = "阅卷师工号",index = 3)
    private String graderNo;

    @TableField("阅卷师名称")
    @ExcelProperty(value = "阅卷师名称",index = 4)
    private String graderName;
}
