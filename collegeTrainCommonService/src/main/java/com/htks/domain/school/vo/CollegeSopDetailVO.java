package com.htks.domain.school.vo;

import com.htks.domain.school.dto.CollegeTrainPostArea;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CollegeSopDetailVO {

    private Long id;

    @ApiModelProperty("厂别")
    private String companyName;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("学员姓名")
    private String employeeName;

    @ApiModelProperty("学员工号")
    private String employeeNo;

    @ApiModelProperty("实习部门")
    private String practiceDepartment;

    @ApiModelProperty("区域/制程")
    private String area;

    @ApiModelProperty("SOP名称")
    private String sopName;

    @ApiModelProperty("质量评委评分")
    private String qaScore;

    @ApiModelProperty("工程评委评分")
    private String enScore;

    @ApiModelProperty("得分")
    private String resultScore;

    public CollegeSopDetailVO(Long id, String companyName, String batch) {
        this.id = id;
        this.companyName = companyName;
        this.batch = batch;
    }
}
