package com.htks.domain.admin.service.impl;


import com.htks.domain.AbstractBaseServiceImpl;
import com.htks.domain.admin.AdminCondition;
import com.htks.domain.admin.dto.AdminSearchEntity;
import com.htks.domain.admin.repository.hana.AdminRepository;
import com.htks.domain.admin.service.AdminService;
import com.htks.domain.common.dto.UpgradeUserAdminEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service("AdminServiceImpl")
@Transactional(rollbackFor = Exception.class)
public class AdminServiceImpl extends AbstractBaseServiceImpl implements AdminService {

    private final AdminRepository adminRepository;

    public AdminServiceImpl(AdminRepository adminRepository) {
        this.adminRepository = adminRepository;
    }

    @Override
    public int queryAdminCount(AdminCondition condition) {
        return adminRepository.queryAdminCount(condition);
    }

    @Override
    public List<AdminSearchEntity> queryAdminList(AdminCondition condition) {
        return adminRepository.queryAdminList(condition);
    }

    @Override
    public Boolean deleteAdmin(Long id) {
        return adminRepository.deleteAdmin(id)>0;
    }

    @Override
    public boolean updateAdmin(UpgradeUserAdminEntity upgradeUserAdminEntity) {
        return adminRepository.updateAdmin(upgradeUserAdminEntity)>0;
    }

    @Override
    public boolean addAdmin(UpgradeUserAdminEntity upgradeUserAdminEntity) {
        return adminRepository.addAdmin(upgradeUserAdminEntity)>0;
    }


}
