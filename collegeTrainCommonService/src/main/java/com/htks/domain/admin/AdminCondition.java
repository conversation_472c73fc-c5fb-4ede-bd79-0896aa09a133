package com.htks.domain.admin;

import com.htks.domain.AbstractPaginationQueryCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AdminCondition extends AbstractPaginationQueryCondition {

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "部门")
    private String departmentName;


}
