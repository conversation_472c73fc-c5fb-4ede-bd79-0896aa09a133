package com.htks.domain.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 *  查询结果类
 *
 */
@Getter
@Setter
public class AdminSearchEntity {
    @ApiModelProperty(value = "登录人ID")
    private Long id;

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "角色")
    private String roleId;


}
