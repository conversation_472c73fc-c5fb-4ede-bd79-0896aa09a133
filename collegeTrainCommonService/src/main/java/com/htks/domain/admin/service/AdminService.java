package com.htks.domain.admin.service;

import com.htks.domain.admin.AdminCondition;
import com.htks.domain.admin.dto.AdminSearchEntity;
import com.htks.domain.common.dto.UpgradeUserAdminEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface AdminService {
    int queryAdminCount(final AdminCondition condition);
    List<AdminSearchEntity> queryAdminList(final AdminCondition condition);
    Boolean deleteAdmin(Long id);
    boolean updateAdmin(UpgradeUserAdminEntity upgradeUserAdminEntity);
    boolean addAdmin(UpgradeUserAdminEntity upgradeUserAdminEntity);
}
