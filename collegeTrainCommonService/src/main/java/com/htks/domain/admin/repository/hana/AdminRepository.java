package com.htks.domain.admin.repository.hana;

import com.htks.domain.admin.AdminCondition;
import com.htks.domain.admin.dto.AdminSearchEntity;
import com.htks.domain.common.dto.UpgradeUserAdminEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface AdminRepository {
    int queryAdminCount(final AdminCondition condition);
    List<AdminSearchEntity> queryAdminList(final AdminCondition condition);
    Integer deleteAdmin(Long id);

    UpgradeUserAdminEntity getAdminInfoByEmployNo(final String employeeNo);

    Integer updateAdmin(UpgradeUserAdminEntity upgradeUserAdminEntity);

    Integer addAdmin(UpgradeUserAdminEntity upgradeUserAdminEntity);
}
