package com.htks.utils;

import cn.hutool.json.JSONObject;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.Base64;
import java.util.List;

@Slf4j
public class HttpUtils {

    /**
     * 向目的URL发送post请求
     * @param url       目的url
     * @param params    发送的参数
     * @return  AdToutiaoJsonTokenData
     */
    public static String sendPostRequest(String url, Object params){
        List<MediaType> mediaTypeList=  Lists.newArrayList();
        mediaTypeList.add(MediaType.APPLICATION_JSON_UTF8);

        System.out.println(url+" 请求参数："+params);
        RestTemplate client = new RestTemplate();
        //新建Http头，add方法可以添加参数
        HttpHeaders headers = new HttpHeaders();
        //设置请求发送方式
        HttpMethod method = HttpMethod.POST;
        // 以表单的方式提交
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(mediaTypeList);
        //将请求头部和参数合成一个请求
        HttpEntity<Object> requestEntity = new HttpEntity<>(params, headers);
        //执行HTTP请求，将返回的结构使用String 类格式化（可设置为对应返回值格式的类）
        ResponseEntity<String> response = client.exchange(url, method, requestEntity, String.class);

        return response.getBody();
    }

    /**
     * 向目的URL发送get请求
     * @param url       目的url
     * @param params    发送的参数
     * @return  String
     */
    public static String sendGetRequest(String url, Object params){
        RestTemplate client = new RestTemplate();
        //新建Http头，add方法可以添加参数
        HttpHeaders headers = new HttpHeaders();
        //设置请求发送方式
        HttpMethod method = HttpMethod.GET;
        // 以表单的方式提交
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        //将请求头部和参数合成一个请求
        HttpEntity<Object> requestEntity = new HttpEntity<>(params, headers);
        //执行HTTP请求，将返回的结构使用String 类格式化
        ResponseEntity<String> response = client.exchange(url, method, requestEntity, String.class);

        return response.getBody();
    }

    /**
     * 接收推送的数据 body里的参数
     * @param request
     * @return
     */
    public static String getRequestStr(HttpServletRequest request){
        try {
            // 获取输入流
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));

            // 写入数据到Stringbuilder
            StringBuilder sb = new StringBuilder();
            String line = null;
            while ((line = streamReader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();

        } catch (Exception e) {

            return  null;
        }
    }

    public static InputStream postDecryptFile(String url, MultipartFile file){
        RestTemplate restTemplate = new RestTemplate();
        File newFile = convertToFile(file);
        FileSystemResource fileResource = new FileSystemResource(newFile);
        // 准备MultiValueMap来存储文件数据
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", fileResource);
        // 设置请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        // 创建HttpEntity
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        // 发起POST请求
        JSONObject object = restTemplate.postForObject(
                url,
                requestEntity,
                JSONObject.class);
        if (object != null && (0 == (Integer) com.alibaba.fastjson.JSONObject.parseObject(object.get("header") + "").get("code"))) {
            JSONObject jsonObject =  object.getJSONObject("value");
            String base64 = jsonObject.getStr("base64");
            byte[] bytes = Base64.getDecoder().decode(base64);
            return new ByteArrayInputStream(bytes);
        }else{
            return null;
        }
    }

    public static File convertToFile(MultipartFile file) {
        try {
            File convFile = new File(file.getOriginalFilename());
            convFile.createNewFile();
            @Cleanup InputStream inputStream = file.getInputStream();
            @Cleanup OutputStream outputStream = new FileOutputStream(convFile);
            int byteRead = 0;
            byte[] buffer = new byte[1024];
            while ((byteRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, byteRead);
            }
            return convFile;
        } catch (Exception e) {
            log.error("文件转换异常:", e.getMessage());
        }
        return null;
    }

}
