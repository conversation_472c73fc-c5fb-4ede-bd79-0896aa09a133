package com.htks.common.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.htks.common.convert.Search;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class PageUtils {

    /**
     * 默认页数
     */
    private static final int DEFAULT_PAGE = 1;

    /**
     * 默认每页条目10
     */
    private static final int DEFAULT_LIMIT = 10;

    /**
     *
     * @Title: getPage
     * @Description: 分页默认配置
     * @param: @param search
     * @param: @return
     * @return: Page<T>
     * @throws
     */
    public static <T> Page<T> getPage(Search search) {
        Integer limit = toInt(search.getPageSize(), DEFAULT_LIMIT);
        Integer cursor = toInt(search.getPageNum(), DEFAULT_PAGE);
        Page<T> page = new Page<>(cursor, limit, true);
        return page;
    }

    /**
     * 转换为int<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value
     *            被转换的值
     * @param defaultValue
     *            转换错误时的默认值
     * @return 结果
     */
    private static Integer toInt(Object value, Integer defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        final String valueStr = toStr(value, null);
        if (StringUtils.isEmpty(valueStr)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(valueStr.trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 转换为字符串<br>
     * 如果给定的值为null，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value
     *            被转换的值
     * @param defaultValue
     *            转换错误时的默认值
     * @return 结果
     */
    private static String toStr(Object value, String defaultValue) {
        if (Objects.isNull(value)) {
            return defaultValue;
        }
        if (value instanceof String) {
            return (String) value;
        }
        return value.toString();
    }
}
