package com.htks.common.utils;

import com.alibaba.excel.util.StringUtils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/12 10:33
 */
public class CheckNumberUtils {

    /**
     * 整数校验
     * @attention: 不包含小数位和0
     * @date: 2020年11月12日 0012 10:45
     * @param: number
     * @return: boolean
     */
    public static boolean isNumber(String number) {
        // 非空校验
        if (StringUtils.isEmpty(number)) {
            return false;
        }
        String pattern = "^[1-9]\\d*$";
        // 整数校验（可以是>=1）
        Pattern r = Pattern.compile(pattern);
        return r.matcher(number).matches();
    }
}
