package com.htks.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.util.Collections;

/**
 * 库存管理工具类
 * 基于Redis实现分布式库存管理，防止超卖
 *
 * <AUTHOR> Generated
 * @date 2025/07/09
 */
@Slf4j
@Component
public class StockUtils {

    private final JedisUtils jedisUtils;
    private final DistributedLockUtils distributedLockUtils;
    
    /**
     * 库存前缀
     */
    private static final String STOCK_PREFIX = "stock:";
    
    /**
     * 库存锁前缀
     */
    private static final String STOCK_LOCK_PREFIX = "stock_lock:";
    
    /**
     * 扣减库存的Lua脚本，保证原子性
     */
    private static final String DEDUCT_STOCK_SCRIPT = 
        "local stock = redis.call('get', KEYS[1]) " +
        "if stock == false then " +
        "    return -1 " +
        "end " +
        "local stockNum = tonumber(stock) " +
        "local deductNum = tonumber(ARGV[1]) " +
        "if stockNum >= deductNum then " +
        "    redis.call('decrby', KEYS[1], deductNum) " +
        "    return stockNum - deductNum " +
        "else " +
        "    return -2 " +
        "end";

    @Autowired
    public StockUtils(JedisUtils jedisUtils, DistributedLockUtils distributedLockUtils) {
        this.jedisUtils = jedisUtils;
        this.distributedLockUtils = distributedLockUtils;
    }

    /**
     * 初始化库存
     *
     * @param stockKey 库存key
     * @param quantity 库存数量
     * @return 是否成功
     */
    public boolean initStock(String stockKey, int quantity) {
        if (quantity < 0) {
            log.error("库存数量不能为负数: {}", quantity);
            return false;
        }
        
        String realKey = STOCK_PREFIX + stockKey;
        
        try {
            jedisUtils.set(realKey, String.valueOf(quantity));
            log.info("初始化库存成功，stockKey: {}, quantity: {}", stockKey, quantity);
            return true;
        } catch (Exception e) {
            log.error("初始化库存失败，stockKey: {}", stockKey, e);
            return false;
        }
    }

    /**
     * 获取当前库存
     *
     * @param stockKey 库存key
     * @return 库存数量，-1表示库存不存在
     */
    public int getStock(String stockKey) {
        String realKey = STOCK_PREFIX + stockKey;
        
        try {
            String stock = jedisUtils.get(realKey);
            if (stock == null) {
                return -1;
            }
            return Integer.parseInt(stock);
        } catch (Exception e) {
            log.error("获取库存失败，stockKey: {}", stockKey, e);
            return -1;
        }
    }

    /**
     * 扣减库存（原子操作）
     *
     * @param stockKey 库存key
     * @param quantity 扣减数量
     * @return 扣减后的库存数量，-1表示库存不存在，-2表示库存不足
     */
    public int deductStock(String stockKey, int quantity) {
        if (quantity <= 0) {
            log.error("扣减数量必须大于0: {}", quantity);
            return -2;
        }
        
        String realKey = STOCK_PREFIX + stockKey;
        
        try (Jedis jedis = jedisUtils.getJedis()) {
            // 使用Lua脚本保证原子性
            Object result = jedis.eval(DEDUCT_STOCK_SCRIPT, Collections.singletonList(realKey), 
                                     Collections.singletonList(String.valueOf(quantity)));
            
            int remainingStock = Integer.parseInt(result.toString());
            
            if (remainingStock >= 0) {
                log.info("扣减库存成功，stockKey: {}, deductQuantity: {}, remainingStock: {}", 
                        stockKey, quantity, remainingStock);
            } else if (remainingStock == -1) {
                log.warn("库存不存在，stockKey: {}", stockKey);
            } else if (remainingStock == -2) {
                log.warn("库存不足，stockKey: {}, deductQuantity: {}", stockKey, quantity);
            }
            
            return remainingStock;
        } catch (Exception e) {
            log.error("扣减库存失败，stockKey: {}", stockKey, e);
            return -2;
        }
    }

    /**
     * 增加库存
     *
     * @param stockKey 库存key
     * @param quantity 增加数量
     * @return 增加后的库存数量，-1表示失败
     */
    public int addStock(String stockKey, int quantity) {
        if (quantity <= 0) {
            log.error("增加数量必须大于0: {}", quantity);
            return -1;
        }
        
        String realKey = STOCK_PREFIX + stockKey;
        
        try {
            Long result = jedisUtils.incrBy(realKey, quantity);
            log.info("增加库存成功，stockKey: {}, addQuantity: {}, currentStock: {}", 
                    stockKey, quantity, result);
            return result.intValue();
        } catch (Exception e) {
            log.error("增加库存失败，stockKey: {}", stockKey, e);
            return -1;
        }
    }

    /**
     * 预扣库存（带锁的安全扣减）
     * 适用于需要在业务处理前先锁定库存的场景
     *
     * @param stockKey 库存key
     * @param quantity 扣减数量
     * @param lockTimeout 锁超时时间（秒）
     * @return 预扣结果
     */
    public PreDeductResult preDeductStock(String stockKey, int quantity, int lockTimeout) {
        String lockKey = STOCK_LOCK_PREFIX + stockKey;
        String lockValue = distributedLockUtils.tryLock(lockKey, lockTimeout);
        
        if (lockValue == null) {
            log.warn("获取库存锁失败，stockKey: {}", stockKey);
            return new PreDeductResult(false, -1, null);
        }
        
        try {
            int result = deductStock(stockKey, quantity);
            if (result >= 0) {
                // 扣减成功，返回锁值用于后续释放
                return new PreDeductResult(true, result, lockValue);
            } else {
                // 扣减失败，立即释放锁
                distributedLockUtils.releaseLock(lockKey, lockValue);
                return new PreDeductResult(false, result, null);
            }
        } catch (Exception e) {
            // 异常情况下释放锁
            distributedLockUtils.releaseLock(lockKey, lockValue);
            log.error("预扣库存异常，stockKey: {}", stockKey, e);
            return new PreDeductResult(false, -1, null);
        }
    }

    /**
     * 确认预扣（释放锁）
     *
     * @param stockKey 库存key
     * @param lockValue 锁值
     */
    public void confirmPreDeduct(String stockKey, String lockValue) {
        if (lockValue != null) {
            String lockKey = STOCK_LOCK_PREFIX + stockKey;
            distributedLockUtils.releaseLock(lockKey, lockValue);
            log.info("确认预扣库存，释放锁，stockKey: {}", stockKey);
        }
    }

    /**
     * 回滚预扣（恢复库存并释放锁）
     *
     * @param stockKey 库存key
     * @param quantity 回滚数量
     * @param lockValue 锁值
     */
    public void rollbackPreDeduct(String stockKey, int quantity, String lockValue) {
        try {
            // 恢复库存
            addStock(stockKey, quantity);
            log.info("回滚预扣库存，stockKey: {}, quantity: {}", stockKey, quantity);
        } finally {
            // 释放锁
            if (lockValue != null) {
                String lockKey = STOCK_LOCK_PREFIX + stockKey;
                distributedLockUtils.releaseLock(lockKey, lockValue);
            }
        }
    }

    /**
     * 预扣结果
     */
    public static class PreDeductResult {
        private final boolean success;
        private final int remainingStock;
        private final String lockValue;

        public PreDeductResult(boolean success, int remainingStock, String lockValue) {
            this.success = success;
            this.remainingStock = remainingStock;
            this.lockValue = lockValue;
        }

        public boolean isSuccess() {
            return success;
        }

        public int getRemainingStock() {
            return remainingStock;
        }

        public String getLockValue() {
            return lockValue;
        }
    }
}
