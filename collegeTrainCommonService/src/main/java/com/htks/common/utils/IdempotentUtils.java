package com.htks.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 幂等性工具类
 * 基于Redis实现接口幂等性控制
 *
 * <AUTHOR> Generated
 * @date 2025/07/09
 */
@Slf4j
@Component
public class IdempotentUtils {

    private final JedisUtils jedisUtils;
    
    /**
     * 幂等性token前缀
     */
    private static final String IDEMPOTENT_PREFIX = "idempotent:";
    
    /**
     * 默认token过期时间（秒）
     */
    private static final int DEFAULT_EXPIRE_TIME = 300; // 5分钟

    @Autowired
    public IdempotentUtils(JedisUtils jedisUtils) {
        this.jedisUtils = jedisUtils;
    }

    /**
     * 生成幂等性token
     *
     * @param businessKey 业务标识
     * @return token
     */
    public String generateToken(String businessKey) {
        String token = UUID.randomUUID().toString().replace("-", "");
        String key = IDEMPOTENT_PREFIX + businessKey + ":" + token;
        
        // 设置token，标记为未使用
        jedisUtils.setex(key, DEFAULT_EXPIRE_TIME, "0");
        
        log.debug("生成幂等性token: {}", token);
        return token;
    }

    /**
     * 验证并消费token
     *
     * @param businessKey 业务标识
     * @param token       token
     * @return 是否验证成功
     */
    public boolean validateAndConsumeToken(String businessKey, String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("幂等性token为空");
            return false;
        }
        
        String key = IDEMPOTENT_PREFIX + businessKey + ":" + token;
        
        try {
            String value = jedisUtils.get(key);
            if (value == null) {
                log.warn("幂等性token不存在或已过期: {}", token);
                return false;
            }
            
            if ("1".equals(value)) {
                log.warn("幂等性token已被使用: {}", token);
                return false;
            }
            
            // 标记token为已使用
            jedisUtils.set(key, "1");
            log.debug("成功验证并消费幂等性token: {}", token);
            return true;
        } catch (Exception e) {
            log.error("验证幂等性token异常: {}", token, e);
            return false;
        }
    }

    /**
     * 检查token是否存在且未使用
     *
     * @param businessKey 业务标识
     * @param token       token
     * @return 是否可用
     */
    public boolean isTokenAvailable(String businessKey, String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        
        String key = IDEMPOTENT_PREFIX + businessKey + ":" + token;
        
        try {
            String value = jedisUtils.get(key);
            return "0".equals(value);
        } catch (Exception e) {
            log.error("检查幂等性token状态异常: {}", token, e);
            return false;
        }
    }

    /**
     * 删除token
     *
     * @param businessKey 业务标识
     * @param token       token
     */
    public void removeToken(String businessKey, String token) {
        if (token == null || token.trim().isEmpty()) {
            return;
        }
        
        String key = IDEMPOTENT_PREFIX + businessKey + ":" + token;
        
        try {
            jedisUtils.del(key);
            log.debug("删除幂等性token: {}", token);
        } catch (Exception e) {
            log.error("删除幂等性token异常: {}", token, e);
        }
    }
}
