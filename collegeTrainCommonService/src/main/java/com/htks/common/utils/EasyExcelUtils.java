package com.htks.common.utils;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * Excel处理工具类
 *
 * <AUTHOR>
 * @date 2023/02/08.
 */
@Slf4j
public class EasyExcelUtils<T> {
    public static <T> List<T> readExcel(MultipartFile excelFile, Class classT) throws IOException {
        final InputStream excelStream = excelFile.getInputStream();
        final EasyExcelListener<T> listener =new EasyExcelListener(classT);
        ExcelReader excelReader = EasyExcel.read(excelStream,classT,listener).excelType(ExcelTypeEnum.XLSX).build();
        try{
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
        }finally {
            excelReader.finish();
        }
        return listener.getResult();
    }

    public static <T> List<T> readExcel2(InputStream excelStream, Class classT) throws IOException {
        final EasyExcelListener<T> listener =new EasyExcelListener(classT);
        ExcelReader excelReader = EasyExcel.read(excelStream,classT,listener).build();
        try{
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
        }finally {
            excelReader.finish();
        }
        return listener.getResult();
    }

    public static <T> List<T> readExcel3(InputStream excelStream, Class classT) throws IOException {
        final EasyExcelListener<T> listener =new EasyExcelListener(classT);
        ExcelReader excelReader = EasyExcel.read(excelStream,classT,listener).headRowNumber(2).build();
        try{
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
        }finally {
            excelReader.finish();
        }
        return listener.getResult();
    }

    /**
     * 方法描述: 浏览器点击导出后导出文件
     *
     * @param response 响应
     * @param list     导出数据集合
     * @param fileName 文件名 不含后缀
     * @param clazz    导出数据的数据类型
     */
    public static void exportExcel(HttpServletResponse response, List<?> list, String fileName, Class<?> clazz) {

        String sheetName = fileName;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            //防止中文乱码
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "iso8859-1") + ".xlsx");//指定下载的文件名
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), clazz).autoCloseStream(Boolean.FALSE).sheet(sheetName).doWrite(list);
        } catch (Exception e) {
            log.warn("文件下载失败!");
        }
    }
}
