package com.htks.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.SetParams;

import java.util.Collections;
import java.util.UUID;

/**
 * 分布式锁工具类
 * 基于Redis实现分布式锁，支持自动续期和防止死锁
 *
 * <AUTHOR> Generated
 * @date 2025/07/09
 */
@Slf4j
@Component
public class DistributedLockUtils {

    private final JedisUtils jedisUtils;
    
    /**
     * 锁的前缀
     */
    private static final String LOCK_PREFIX = "distributed_lock:";
    
    /**
     * 默认锁过期时间（秒）
     */
    private static final int DEFAULT_EXPIRE_TIME = 30;
    
    /**
     * 默认获取锁的超时时间（毫秒）
     */
    private static final long DEFAULT_TIMEOUT = 5000L;
    
    /**
     * 释放锁的Lua脚本，保证原子性
     */
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";

    @Autowired
    public DistributedLockUtils(JedisUtils jedisUtils) {
        this.jedisUtils = jedisUtils;
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁的key
     * @return 锁的值（用于释放锁时验证）
     */
    public String tryLock(String lockKey) {
        return tryLock(lockKey, DEFAULT_EXPIRE_TIME, DEFAULT_TIMEOUT);
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey    锁的key
     * @param expireTime 锁过期时间（秒）
     * @return 锁的值（用于释放锁时验证）
     */
    public String tryLock(String lockKey, int expireTime) {
        return tryLock(lockKey, expireTime, DEFAULT_TIMEOUT);
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey    锁的key
     * @param expireTime 锁过期时间（秒）
     * @param timeout    获取锁的超时时间（毫秒）
     * @return 锁的值（用于释放锁时验证），获取失败返回null
     */
    public String tryLock(String lockKey, int expireTime, long timeout) {
        String realKey = LOCK_PREFIX + lockKey;
        String lockValue = UUID.randomUUID().toString();
        
        long startTime = System.currentTimeMillis();
        
        try (Jedis jedis = jedisUtils.getJedis()) {
            while (System.currentTimeMillis() - startTime < timeout) {
                // 使用SET命令的NX和EX参数实现原子性加锁
                SetParams params = new SetParams();
                params.nx(); // 只在key不存在时设置
                params.ex(expireTime); // 设置过期时间
                
                String result = jedis.set(realKey, lockValue, params);
                
                if ("OK".equals(result)) {
                    log.debug("成功获取分布式锁: {}", lockKey);
                    return lockValue;
                }
                
                // 短暂休眠后重试
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("获取锁时被中断: {}", lockKey);
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("获取分布式锁异常: {}", lockKey, e);
        }
        
        log.debug("获取分布式锁超时: {}", lockKey);
        return null;
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey   锁的key
     * @param lockValue 锁的值
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String lockValue) {
        if (lockValue == null) {
            return false;
        }
        
        String realKey = LOCK_PREFIX + lockKey;
        
        try (Jedis jedis = jedisUtils.getJedis()) {
            // 使用Lua脚本保证释放锁的原子性
            Object result = jedis.eval(UNLOCK_SCRIPT, Collections.singletonList(realKey), 
                                     Collections.singletonList(lockValue));
            
            boolean success = "1".equals(result.toString());
            if (success) {
                log.debug("成功释放分布式锁: {}", lockKey);
            } else {
                log.warn("释放分布式锁失败，锁可能已过期或被其他线程释放: {}", lockKey);
            }
            return success;
        } catch (Exception e) {
            log.error("释放分布式锁异常: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 续期分布式锁
     *
     * @param lockKey    锁的key
     * @param lockValue  锁的值
     * @param expireTime 新的过期时间（秒）
     * @return 是否续期成功
     */
    public boolean renewLock(String lockKey, String lockValue, int expireTime) {
        if (lockValue == null) {
            return false;
        }
        
        String realKey = LOCK_PREFIX + lockKey;
        
        // 续期的Lua脚本
        String renewScript = 
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('expire', KEYS[1], ARGV[2]) " +
            "else " +
            "    return 0 " +
            "end";
        
        try (Jedis jedis = jedisUtils.getJedis()) {
            Object result = jedis.eval(renewScript, Collections.singletonList(realKey), 
                                     java.util.Arrays.asList(lockValue, String.valueOf(expireTime)));
            
            boolean success = "1".equals(result.toString());
            if (success) {
                log.debug("成功续期分布式锁: {}", lockKey);
            } else {
                log.warn("续期分布式锁失败，锁可能已过期或被其他线程释放: {}", lockKey);
            }
            return success;
        } catch (Exception e) {
            log.error("续期分布式锁异常: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 检查锁是否存在
     *
     * @param lockKey 锁的key
     * @return 是否存在
     */
    public boolean isLocked(String lockKey) {
        String realKey = LOCK_PREFIX + lockKey;
        try {
            String value = jedisUtils.get(realKey);
            return value != null;
        } catch (Exception e) {
            log.error("检查锁状态异常: {}", lockKey, e);
            return false;
        }
    }
}
