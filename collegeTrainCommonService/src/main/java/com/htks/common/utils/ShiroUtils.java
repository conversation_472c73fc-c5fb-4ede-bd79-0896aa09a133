package com.htks.common.utils;

import static com.google.common.base.MoreObjects.firstNonNull;
import static com.htks.common.SystemConfig.NO_LOGIN;
import static com.htks.common.SystemConfig.ResponseStatusEnum.SESSION_TIME_OUT;

import com.htks.common.exception.BaseRuntimeException;
import lombok.experimental.UtilityClass;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;

/**
 * Shiro工具类
 *
 * <AUTHOR>
 * @date 2018/09/18
 */
@UtilityClass
public final class ShiroUtils {

  public Session getSession() {
    return SecurityUtils.getSubject().getSession();
  }

  public Subject getSubject() {
    return SecurityUtils.getSubject();
  }

  public String getEmployeeNumber() {
    return String.valueOf(firstNonNull(SecurityUtils.getSubject().getPrincipal(), NO_LOGIN));
  }

  public void setSessionAttribute(Object key, Object value) {
    getSession().setAttribute(key, value);
  }

  public Object getSessionAttribute(Object key) {
    Session session = getSession();
    Object obj = session.getAttribute(key);
    if (null == obj) {
      throw new BaseRuntimeException(SESSION_TIME_OUT.getStatusValue());
    }
    return obj;
  }

  public boolean isLogin() {
    return SecurityUtils.getSubject().getPrincipal() != null;
  }

  public void logout() {
    SecurityUtils.getSubject().logout();
  }

}
