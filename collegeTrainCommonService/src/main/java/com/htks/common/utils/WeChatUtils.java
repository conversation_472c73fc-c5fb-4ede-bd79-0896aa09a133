package com.htks.common.utils;

import static com.google.common.base.Strings.isNullOrEmpty;

import com.htks.common.config.WeChatConfig;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 企业微信Utils
 *
 * <AUTHOR>
 * @date 2022/07/21.
 */
@Slf4j
@Component
public final class WeChatUtils {

  @Value("${wechat.collegeTrain.agentId}")
  private Integer agentId;

  @Resource
  private WeChatConfig weChatConfig;

  //发送信息
  public Boolean sendMsg(WxCpMessage wxCpMessage, WxCpMessageService wxCpMessageService, String userWeChatId, String msg) {
    Boolean result = Boolean.TRUE;
    try {
      // 开启微信服务
//      final WxCpService wxCpService = weChatConfig.getWxCpService();
//      final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
//      // 开启微信消息服务
//      final WxCpMessage wxCpMessage = new WxCpMessage();
      if (!isNullOrEmpty(userWeChatId)) {
        wxCpMessage.setToUser(userWeChatId);
        wxCpMessage.setContent(msg);
        wxCpMessage.setMsgType("text");
        wxCpMessage.setAgentId(agentId);
        wxCpMessage.setSafe("0");
        wxCpMessageService.send(wxCpMessage);
      }
    } catch (WxErrorException e) {
      log.error("微信推送异常", e);
      result = Boolean.FALSE;
    }
    return result;
  }

}
