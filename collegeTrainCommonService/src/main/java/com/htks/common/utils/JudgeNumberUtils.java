package com.htks.common.utils;

import cn.hutool.extra.pinyin.PinyinUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Locale;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 16:07
 */
@Slf4j
public class JudgeNumberUtils {

    /**
     * 生成批阅编号，共13位字母加数字，规则为考试名称前两个字的首字母大写+年月日8位数字+3位随机数字
     *
     * @param examName 考试名称
     * @return {@link String}
     */
    public static String getJudgeNumber(String examName) {
        //考试名称前两个字的首字母大写
        String firstLetter = PinyinUtil.getFirstLetter(examName.substring(0,2), "").toUpperCase(Locale.ROOT);
        //年月日8位数字
        String date = DateUtils.formatDateTimeYearMonthDay(LocalDateTime.now());
        //3位随机数字
        String randomNumber = Integer.toString((int) (Math.random() * 900 + 100));
        return firstLetter+date+randomNumber;
    }

    /**
     * 生成sop编号，共14位字母加数字，规则为SOP+年月日8位数字+3位随机数字
     *
     * @return {@link String}
     */
    public static String getSopJudgeNumber() {
        //年月日8位数字
        String date = DateUtils.formatDateTimeYearMonthDay(LocalDateTime.now());
        //3位随机数字
        String randomNumber = Integer.toString((int) (Math.random() * 900 + 100));
        return "SOP"+date+randomNumber;
    }
}
