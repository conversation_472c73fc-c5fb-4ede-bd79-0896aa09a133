package com.htks.common.utils;

import lombok.experimental.UtilityClass;

import java.security.SecureRandom;
import java.util.HashSet;
import java.util.Random;

/**
 * 解决Sonar安全热点问题，重写 org.apache.commons.lang3.RandomStringUtils
 *
 * <AUTHOR>
 * @date 2020/03/09
 */
@UtilityClass
public final class RandomUtils {

  //重写部分
  private static final SecureRandom RANDOM = new SecureRandom();

  public String random(int count) {
    return random(count, false, false);
  }

  public String randomNumeric(int count) {
    return random(count, false, true);
  }

  public String randomAlphanumeric(int count) {
    return random(count, true, true);
  }

  public String random(int count, boolean letters, boolean numbers) {
    return random(count, 0, 0, letters, numbers);
  }

  public String random(int count, int start, int end, boolean letters, boolean numbers) {
    return random(count, start, end, letters, numbers, null, RANDOM);
  }

  public String random(int count, int start, int end, boolean letters, boolean numbers,
      char... chars) {
    return random(count, start, end, letters, numbers, chars, RANDOM);
  }

  public String random(int count, int start, int end, boolean letters, boolean numbers,
      char[] chars, Random random) {
    if (count == 0) {
      return "";
    } else if (count < 0) {
      throw new IllegalArgumentException(
          "Requested random string length " + count + " is less than 0.");
    } else if (chars != null && chars.length == 0) {
      throw new IllegalArgumentException("The chars array must not be empty");
    } else {
      if (start == 0 && end == 0) {
        if (chars != null) {
          end = chars.length;
        } else if (!letters && !numbers) {
          end = 2147483647;
        } else {
          end = 123;
          start = 32;
        }
      } else if (end <= start) {
        throw new IllegalArgumentException(
            "Parameter end (" + end + ") must be greater than start (" + start + ")");
      }

      char[] buffer = new char[count];
      int gap = end - start;

      while (true) {
        while (true) {
          while (count-- != 0) {
            char ch;
            if (chars == null) {
              ch = (char) (random.nextInt(gap) + start);
            } else {
              ch = chars[random.nextInt(gap) + start];
            }

            if (letters && Character.isLetter(ch) || numbers && Character.isDigit(ch)
                || !letters && !numbers) {
              if (ch >= '\udc00' && ch <= '\udfff') {
                if (count == 0) {
                  ++count;
                } else {
                  buffer[count] = ch;
                  --count;
                  buffer[count] = (char) ('\ud800' + random.nextInt(128));
                }
              } else if (ch >= '\ud800' && ch <= '\udb7f') {
                if (count == 0) {
                  ++count;
                } else {
                  buffer[count] = (char) ('\udc00' + random.nextInt(128));
                  --count;
                  buffer[count] = ch;
                }
              } else if (ch >= '\udb80' && ch <= '\udbff') {
                ++count;
              } else {
                buffer[count] = ch;
              }
            } else {
              ++count;
            }
          }

          return new String(buffer);
        }
      }
    }
  }

  public String random(int count, String chars) {
    return chars == null ? random(count, 0, 0, false, false, null, RANDOM)
        : random(count, chars.toCharArray());
  }

  public String random(int count, char... chars) {
    return chars == null ? random(count, 0, 0, false, false, null, RANDOM)
        : random(count, 0, chars.length, false, false, chars, RANDOM);
  }

    /**
     * 随机指定范围内N个不重复的数
     * 利用HashSet的特征，只能存放不同的值
     *
     * @param min          指定范围最小值
     * @param max          指定范围最大值
     * @param m            需要的数量
     * @param n            剩余添加个数
     * @param set<Integer> set 随机数结果集
     */
    public static void randomSet(int min, int max, int m, int n, HashSet<Integer> set) {
        if (n > (max - min + 1) || max < min) {
            return;
        }
        for (int i = 0; i < n; i++) {
            // 调用Math.random()方法
//            int num = (int) (Math.random() * (max - min)) + min;
            set.add(i + 1);// 将不同的数存入HashSet中
        }
        int setSize = set.size();
        // 如果存入的数小于指定生成的个数，则调用递归再生成剩余个数的随机数，如此循环，直到达到指定大小
        if (setSize < m) {
            randomSet(min, max, m, m - setSize, set);// 递归
        }
    }
}
