package com.htks.common.utils;

import static com.google.common.collect.Lists.newArrayList;
import static com.htks.common.SystemConfig.STRING_EMPTY;

import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.apache.shiro.crypto.hash.SimpleHash;
import org.apache.shiro.util.ByteSource;
import org.springframework.util.DigestUtils;


/**
 * 系统工具类
 *
 * <AUTHOR>
 * @date 2018/09/18
 */
@Slf4j
public final class SystemUtils {

  private static final SecureRandom RANDOMIZER = new SecureRandom();
  private int counter = 0;
  private long lastTimeValue = 0L;

  /**
   * Md5
   *
   * @param credentialsSalt 盐值
   * @param credentials     密码
   * @return 加密后的密码
   */
  public static String simpleEncrypt(String credentialsSalt, String credentials) {
    final SimpleHash cipherText = new SimpleHash(Md5Hash.ALGORITHM_NAME, credentials,
        ByteSource.Util.bytes(credentialsSalt));
    return cipherText.toString();
  }

  /**
   * 随机生成定长的字符串(包含字母数字)
   *
   * @param length 生成字符串的长度
   * @return 随机字符串
   */
  public static String obtainRandomAlphanumeric(Integer length) {
    return RandomUtils.randomAlphanumeric(length).toLowerCase();
  }

  public static String obtainEncPassword(String loginInfo, String blankPassword) {
    final String catNameAndPwd = Strings.nullToEmpty(loginInfo).concat(blankPassword);
    return DigestUtils.md5DigestAsHex(catNameAndPwd.getBytes());
  }

  /**
   * 获取字符串标识符
   *
   * @return 字符串标识符
   */
  public String nextStringIdentifier() {
    Long currentRandom = RANDOMIZER.nextLong();

    if (currentRandom < 0L) {
      currentRandom = -currentRandom;
    }

    currentRandom %= 2176782336L;
    currentRandom += 2176782336L;
    Long currentTimeValue;
    Integer currentCount;
    synchronized (this) {
      currentTimeValue = System.currentTimeMillis() / 2000L;
      currentTimeValue %= 46656L;
      currentTimeValue += 46656L;
      if (this.lastTimeValue != currentTimeValue) {
        this.lastTimeValue = currentTimeValue;
        this.counter = 0;
      }

      currentCount = this.counter++;
    }

    return Long.toString(currentRandom, 36).substring(1) + Long.toString(currentTimeValue, 36)
        .substring(1) + Long.toString((long) currentCount, 36);
  }

  /**
   * 生成MD5
   */
  public static String generateMD5(String data) {
    return DigestUtils.md5DigestAsHex(data.getBytes());
  }

  /**
   * 从map中获取元素，拼接成字符串
   */
  public static String getParamStrFromMap(final Map<String, String> params) {
    final List<String> paramList = newArrayList();
    if (params != null && !params.isEmpty()) {
      final Set<String> stringSet = params.keySet();
      final String[] keys = stringSet.toArray(new String[0]);
      Arrays.sort(keys);
      for (String name : keys) {
        paramList.add(Joiner.on("=").join(name, params.get(name)));
      }
    }
    return Joiner.on("&").join(paramList);
  }

  /**
   * 随机获取集合中的元素
   *
   * @param sourceList 源集合
   * @param number     取值个数
   * @param isRepeat   是否可重复
   * @param <T>        class
   * @return 返回
   */
  public static <T> List<T> randomObtainList(final List<T> sourceList, final Integer number, final Boolean isRepeat) {
    final List<T> result = newArrayList();
    final int num = Math.min(sourceList.size(), number);
    for (int i = 0; i < num; i++) {
      final int index = new Random().nextInt(sourceList.size());
      result.add(sourceList.get(index));
      if (!isRepeat) {
        //防止重复取值
        sourceList.remove(index);
      }
    }
    return result;
  }

  /**
   * 计算平均值
   *
   * @param params       入参
   * @param ignoreSingle 是否忽略单值，True单值返回0，False单值返回单值
   * @return 返回
   */
  public static double calculateAverage(final List<Double> params, Boolean ignoreSingle) {
    if (params.isEmpty()) {
      return 0d;
    }
    if (params.size() == 1) {
      return ignoreSingle ? 0d : params.get(0);
    }
    final DoubleSummaryStatistics stats = params.stream().mapToDouble((x) -> x).summaryStatistics();
    if (params.size() == 2) {
      //两个直接取平均值
      return stats.getAverage();
    }
    //去除最大和最小后计算平均值
    return (stats.getSum() - stats.getMax() - stats.getMin()) * 1.0 / (params.size() - 2);
  }

  /**
   * 获取状态值(1：是  0：否)
   *
   * @param statusId 状态id
   * @return 状态值
   */
  public static String getStatusVal(final String statusId) {
    if (StringUtils.isBlank(statusId)) {
      return STRING_EMPTY;
    }
    return "1".equals(statusId) ? "是" : "否";
  }

}
