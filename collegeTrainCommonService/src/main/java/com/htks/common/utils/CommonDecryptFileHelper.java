package com.htks.common.utils;
import cn.hutool.core.date.DateUtil;
import jcifs.CIFSContext;
import jcifs.context.SingletonContext;
import jcifs.smb.*;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Data
public class CommonDecryptFileHelper {

    private static String pathUrl;
    public static CIFSContext context;
    public CommonDecryptFileHelper(String domain, String username, String password, String pathUrl) {
        context = SingletonContext.getInstance().withCredentials(new NtlmPasswordAuthenticator(domain, username, password));
        this.pathUrl = pathUrl;
    }

    /**
     * 把加密的文件放到解密共享盘，再读取解密共享盘的解密文件，返回文件流，再删除共享盘该文件
     * @return
     */
    public static InputStream commonDecryptFile(MultipartFile file) {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        CommonDecryptFileHelper smbFileUtil = new CommonDecryptFileHelper("ht-tech","decrypt_oa","w5VFDabq","smb://*************/decrypt_oa/dev/");
        String fileName = file.getOriginalFilename();
        String time =  DateUtil.format(new Date(), "yyyyMMdd") + new Date().getTime();
        try {
            smbFileUtil.saveFileToSmb2("smb://*************/decrypt_oa/dev/" , time + fileName,file.getInputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        final SmbFile remoteFile = smbFileUtil.getFile("smb://*************/decrypt_oa/dev/" + time + fileName);
        InputStream in = null;
        try {
            in = new BufferedInputStream(new SmbFileInputStream(remoteFile));
        } catch (SmbException e) {
            e.printStackTrace();
        }
        try {
            deleteFile( time + fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return in;
    }

    /**
     * 获取一个Windows 共享目录的文件实例, 文件权限的认证已经放在构造函数里面了, 所以不需要将账号密码再放在url里面
     *
     * @param url 文件路径 可以是 "\\\\10.160.1.10\\FileServer\\.." 也可以是 "smb://10.160.1.10/FileServer/.."
     * @return SmbFile 一个文件实例, 可以调用里面的方法, 判断是否是文件夹, 以及打开io流
     */
    public static SmbFile getFile(String url) {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        url = url.replace('\\', '/');
        if (url.startsWith("//")) {
            url = "smb:" + url;
        } else if (!url.startsWith("smb://")) {
            url = "smb://" + url;
        }
        try {
            return new SmbFile(url, context);
        } catch (MalformedURLException e) {
            throw new IllegalStateException("URL的格式不正确", e);
        }
    }

    public void copyFileToSmb(File file, String fileName) throws Exception {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        SmbFile smbFileDir = getFile(pathUrl);
        if (!smbFileDir.exists()) {
            smbFileDir.mkdirs();
        }
        SmbFile smbFile = getFile((pathUrl + fileName));
        if (smbFile.exists()) {
            return;
        }
        try (InputStream in = new FileInputStream(file);
             OutputStream out = smbFile.getOutputStream()) {
            byte[] buffer = new byte[4096];
            int length;
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
            }
        }
    }

    public void copySmbToFile(File file, String fileName) throws Exception {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        SmbFile smbFileDir = getFile(pathUrl);
        if (!smbFileDir.exists()) {
            smbFileDir.mkdirs();
        }
        if (file.exists()) {
            return;
        }
        SmbFile smbFile = getFile(pathUrl + fileName);
        try (InputStream in = smbFile.getInputStream();
             OutputStream out = new FileOutputStream(file)) {
            byte[] buffer = new byte[4096];
            int length;
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
            }
        }
    }

    public String getBase64ByFileName(String fileName) throws Exception {
        SmbFile smbFile = getFile(pathUrl + fileName);

        try (InputStream inputStream = smbFile.getInputStream()) {
            // 读取文件内容
            byte[] fileContent = IOUtils.toByteArray(inputStream);

            // 将文件内容编码为Base64字符串
            byte[] encodedBytes = Base64.encodeBase64(fileContent);

            return new String(encodedBytes);
        }

    }

    public String getDecryptFileByFileName(File file, String fileName) throws Exception {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        SmbFile smbFile = getFile(pathUrl + fileName);
        if (smbFile.exists()) {
            smbFile.delete();
        }
        //解密
        try (InputStream in = new FileInputStream(file);
             OutputStream out = smbFile.getOutputStream()) {
            byte[] buffer = new byte[4096];
            int length;
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
            }
        }
        Thread.sleep(1000);
        //转base64
        try (InputStream inputStream = smbFile.getInputStream()) {
            byte[] fileContent = IOUtils.toByteArray(inputStream);
            byte[] encodedBytes = Base64.encodeBase64(fileContent);
            return new String(encodedBytes);
        } finally {
            smbFile.delete();
        }
    }

    public void saveFileToSmb(String targetDirPath, String targetFileName, byte[] fileBytes)
            throws Exception {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        SmbFile smbFileDir = getFile(targetDirPath);
        if (!smbFileDir.exists()) {
            smbFileDir.mkdirs();
        }
        SmbFile smbFile = getFile(targetDirPath + targetFileName);

        SmbFileOutputStream smbOutputStream = new SmbFileOutputStream(smbFile);
        smbOutputStream.write(fileBytes);
        smbOutputStream.flush();
        smbOutputStream.close();
    }

    public String createPathIfNotExists(String targetDirPath)
            throws Exception {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        SmbFile smbFileDir = getFile(targetDirPath);
        if (!smbFileDir.exists()) {
            smbFileDir.mkdirs();
        }
        return targetDirPath;
    }

    public String saveFileToSmb2(String targetDirPath, String targetFileName, InputStream in)
            throws Exception {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        createPathIfNotExists(targetDirPath);
        SmbFile smbFile = getFile(targetDirPath + targetFileName);
        SmbFileOutputStream smbFileOutputStream = new SmbFileOutputStream(smbFile);
        BufferedOutputStream out = new BufferedOutputStream(smbFileOutputStream);
        byte[] buffer = new byte[1024];
        int len = 0;
        while ((len = in.read(buffer, 0, buffer.length)) != -1) {
            out.write(buffer, 0, len);
        }
        out.flush();
        out.close();
        in.close();
        return targetDirPath;
    }


    public void renameToSmb(SmbFile fromSmbFile, String targetDirPath, String targetFileName)
            throws Exception {
        System.setProperty("jcifs.smb.client.dfs.disabled", "true");
        SmbFile smbFileDir = getFile(targetDirPath);
        if (!smbFileDir.exists()) {
            smbFileDir.mkdirs();
        }
        SmbFile targetSmbFile = getFile(targetDirPath + targetFileName);
        fromSmbFile.renameTo(targetSmbFile);

    }

    public Object[] readFileFromSmb(String fileName) throws RuntimeException {
        BufferedReader br = null;
        Object[] lines = null;
        try {
            SmbFile smbFile = getFile(pathUrl + fileName);
            if (!smbFile.exists()) {
                throw new Exception("file (" + fileName + ") not found!");
            }
            SmbFileInputStream inputStream = new SmbFileInputStream(smbFile);
            br = new BufferedReader(new InputStreamReader(inputStream));
            lines = br.lines().toArray();
            return lines;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            try {
                br.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    public String readFileBase64FromSmb(String fileName) throws RuntimeException {
        BufferedReader br = null;
        try {
            SmbFile smbFile = getFile(pathUrl + fileName);
            if (!smbFile.exists()) {
                throw new Exception("file (" + fileName + ") not found!");
            }
            SmbFileInputStream inputStream = new SmbFileInputStream(smbFile);
            br = new BufferedReader(new InputStreamReader(inputStream));

            String textContent = br.toString();
            byte[] bytes = textContent.getBytes();
            return new String(Base64.encodeBase64(bytes));
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    public SmbFileInputStream readFileInputStreamFromSmb(String fileName) throws RuntimeException {
        BufferedReader br = null;
        try {
            SmbFile smbFile = getFile(pathUrl + fileName);
            if (!smbFile.exists()) {
                throw new Exception("file (" + fileName + ") not found!");
            }
            SmbFileInputStream inputStream = new SmbFileInputStream(smbFile);
            br = new BufferedReader(new InputStreamReader(inputStream));
            return inputStream;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    public List<String> listFile() throws Exception {
        SmbFile smbFileDir = getFile(pathUrl);
        return Arrays.asList(smbFileDir.list());
    }

    public static void deleteFile(String fileName) throws Exception {//删除目录下的文件
        try {
            SmbFile smbFile = getFile(pathUrl + "/" + fileName);
            if (smbFile.exists()) {
                smbFile.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void deleteDir(String dir) throws Exception {//删除目录下的文件
        List<SmbFile> smbFiles = this.listFileObj(dir);
        for (SmbFile file : smbFiles) {
            file.delete();
        }
    }

    public List<SmbFile> listFileObj(String path) throws Exception {
        SmbFile smbFileDir = getFile(path);
        if (!smbFileDir.exists())//判定路径是否存在
        {
            return new ArrayList<>();
        }
        return Arrays.asList(smbFileDir.listFiles());
    }

}

