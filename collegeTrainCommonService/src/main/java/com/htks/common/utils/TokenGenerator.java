package com.htks.common.utils;

import static com.htks.common.SystemConfig.TOKEN_EXPIRE;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.common.collect.Maps;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * 生成Token
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Slf4j
@UtilityClass
public final class TokenGenerator {

  private static final String EMPLOYEE_NUMBER = "employeeNumber";

  private static final String HT_KS = "htks";

  public String generateValue(final String employeeNumber, final String signature) {
    //当前时间
    final LocalDateTime now = LocalDateTime.now();
    //过期时间
    final LocalDateTime expireTime = now.plusSeconds(TOKEN_EXPIRE);

    // header Map
    final Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
    map.put("alg", "HMAC256");
    map.put("typ", "JWT");

    // build token
    final String token = JWT.create()
        // header
        .withHeader(map)
        .withIssuer(HT_KS)
        // 附带信息
        .withClaim(EMPLOYEE_NUMBER, employeeNumber)
        //expire time
        .withExpiresAt(DateUtils.asDate(expireTime))
        //sign time
        .withIssuedAt(DateUtils.asDate(now))
        // signature
        .sign(Algorithm.HMAC256(signature));

    if (log.isDebugEnabled()) {
      log.debug("生成工号{}的Token:{}", employeeNumber, token);
    }
    return token;
  }

  /**
   * 验证Token
   *
   * @param token Token值
   * @return 用户信息
   */
  public Map<String, Claim> verifyToken(final String token, final String employeeNumber,
      final String signature) {
    final DecodedJWT jwt;
    try {
      final JWTVerifier verifier = JWT.require(Algorithm.HMAC256(signature))
          .withClaim(EMPLOYEE_NUMBER, employeeNumber)
          .build();
      jwt = verifier.verify(token);
      if (log.isDebugEnabled()) {
        log.debug("Token解码:{}", jwt.getClaim(EMPLOYEE_NUMBER).asString());
      }
    } catch (Exception e) {
      log.error("Token解码异常:", e);
      return null;
    }
    return jwt.getClaims();
  }

  /**
   * 根据Token获取员工工号
   *
   * @param token Token
   * @return 员工工号
   */
  public String getEmployeeNumber(final String token) {
    try {
      final DecodedJWT jwt = JWT.decode(token);
      return jwt.getClaim(EMPLOYEE_NUMBER).asString();
    } catch (final JWTDecodeException e) {
      log.error("JWTDecodeException:", e);
      return null;
    }
  }

}
