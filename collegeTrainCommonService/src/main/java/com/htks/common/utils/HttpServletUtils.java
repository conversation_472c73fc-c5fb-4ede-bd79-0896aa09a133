package com.htks.common.utils;

import com.google.common.io.Closer;
import com.google.common.io.Resources;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;


/**
 * 导出数据时设置httpServlet
 * <p>
 *
 */
@Slf4j
@Component
public class HttpServletUtils {
    public static void setDownload(HttpServletRequest request, HttpServletResponse response,
                                   String fileName) throws UnsupportedEncodingException {
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("GB2312"), "iso8859-1")
                        + ".xls"); // 指定下载的文件名
    }

    public static void downloadTemplate(HttpServletResponse response, String filename, String importTemplatesPath) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes("GB2312"), "iso8859-1") + ".xlsx");//指定下载的文件名

        final Closer closer = Closer.create();
        try {
            final OutputStream os = response.getOutputStream();
            closer.register(os);
            os.write(getImportTemplates(importTemplatesPath));
        } catch (IOException e) {
            log.error("IOException:{}", e.getMessage());
        } finally {
            try {
                closer.close();
            } catch (IOException e) {
                log.error("IOException:{}", e.getMessage());
            }
        }
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     */
    private static byte[] getImportTemplates(String importTemplatesPath) {
        try {
            return Resources.asByteSource(Resources.getResource(importTemplatesPath)).read();
        } catch (IOException e) {
            log.error("获取文件异常:{}", e.getMessage());
            return new byte[0];
        }
    }
}
