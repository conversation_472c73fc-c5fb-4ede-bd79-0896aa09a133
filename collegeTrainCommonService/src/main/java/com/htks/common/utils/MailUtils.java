package com.htks.common.utils;

import com.google.common.base.Strings;
import java.io.File;
import java.util.List;
import java.util.Properties;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 邮件工具类
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Slf4j
@Data
@Component
public final class MailUtils {

  @Value("${email.username}")
  private String username;

  @Value("${email.password}")
  private String password;

  @Value("${email.host}")
  private String host;

  @Value("${email.port}")
  private int port;

  @Value("${email.protocol}")
  private String protocol;

  @Value("${email.defaultEncoding}")
  private String defaultEncoding;

  @Value("${email.senderName}")
  private String senderName;

  public MailUtils() {
  }

  public Boolean sendMail(String toEmail, String ccEmail, String bccEmail, String subject, String text, List<File> files) {
    Boolean result = Boolean.FALSE;
    try {
      System.setProperty("mail.mime.splitlongparameters", "false");
      final JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
      javaMailSender.setUsername(username);
      javaMailSender.setPassword(password);
      javaMailSender.setHost(host);
      javaMailSender.setPort(port);
      javaMailSender.setProtocol(protocol);
      javaMailSender.setDefaultEncoding(defaultEncoding);
      final Properties mailProperties = new Properties();
      mailProperties.put("mail.smtp.auth", true);
      mailProperties.put("mail.smtp.starttls.enable", true);
      javaMailSender.setJavaMailProperties(mailProperties);
      final MimeMessage mimeMessage = getMimeMessage(toEmail, ccEmail, bccEmail, subject, text, javaMailSender, files);
      javaMailSender.send(mimeMessage);
      log.info("发往{}邮件【{}】发送成功", toEmail, subject);
      result = Boolean.TRUE;
    } catch (MessagingException e) {
      log.error("发往{}邮件【{}】发送异常:{}", toEmail, subject, e);
    }
    return result;
  }

  private MimeMessage getMimeMessage(String toEmail, String ccEmail, String bccEmail, String subject, String text,
      JavaMailSenderImpl javaMailSender, List<File> files) throws javax.mail.MessagingException {
    MimeMessage mimeMessage = javaMailSender.createMimeMessage();
    MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
    mimeMessageHelper.setFrom(senderName);
    mimeMessageHelper.setTo(toEmail.split(","));
    if (!Strings.isNullOrEmpty(ccEmail)) {
      mimeMessageHelper.setCc(ccEmail.split(","));
    }
    if (!Strings.isNullOrEmpty(bccEmail)) {
      mimeMessageHelper.setBcc(bccEmail.split(","));
    }
    mimeMessageHelper.setSubject(subject);
    mimeMessageHelper.setText(text, true);
    try {
      if (!CollectionUtils.isEmpty(files)) {
        for (File file : files) {
          mimeMessageHelper.addAttachment(MimeUtility.encodeText(file.getName(), "GBK", "B"), file);
        }
      }
    } catch (Exception e) {
      log.error("Exception:", e);
    }
    return mimeMessage;
  }
}
