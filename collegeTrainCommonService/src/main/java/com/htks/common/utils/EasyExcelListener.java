package com.htks.common.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class EasyExcelListener <T> extends AnalysisEventListener<T> {

    @Getter
    List<T> result;

    private Class<T> clazz;


    public EasyExcelListener(Class<T> clazz){
        result = new ArrayList<>();
        this.clazz = clazz;
    }

    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        result.add(t);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
