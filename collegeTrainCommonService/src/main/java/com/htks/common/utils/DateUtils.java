package com.htks.common.utils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

import lombok.Data;
import lombok.experimental.UtilityClass;
import org.joda.time.DateTime;
import org.joda.time.Period;
import org.joda.time.PeriodType;

/**
 * Date工具类
 *
 * <AUTHOR>
 * @date 2020/09/11.
 */
@UtilityClass
public final class DateUtils {

  //日期格式
  private static final DateTimeFormatter FORMATTER_DDHH = DateTimeFormatter.ofPattern("dd日HH时");
  private static final DateTimeFormatter FORMATTER_HH = DateTimeFormatter.ofPattern("H时");
  private static final DateTimeFormatter FORMATTER_YYYYMMDDHHZH = DateTimeFormatter.ofPattern("yyyy年MM月dd日H时");
  private static final DateTimeFormatter FORMATTER_YYYYMMDD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
  private static final DateTimeFormatter FORMATTER_YYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
  private static final DateTimeFormatter FORMATTER_YYYYMMDDHH = DateTimeFormatter.ofPattern("yyyy-MM-dd H");
  private static final DateTimeFormatter FORMATTER_YMD = DateTimeFormatter.ofPattern("yyyyMMdd");

  public static Date asDate(String date, String format) throws ParseException {
    return new SimpleDateFormat(format).parse(date);
  }

  /**
   * 复制一个有效的时间
   *
   * @param srcDate 日期时间
   * @return clone的日期时间。如果为null，则返回null
   */
  public Date obtainValidDate(final Date srcDate) {
    return srcDate == null ? null : new Date(srcDate.getTime());
  }

  /**
   * 复制一个有效的时间
   *
   * @param srcDate 日期时间
   * @return clone的日期时间。如果为null，则返回null
   */
  public Timestamp obtainValidTimestamp(final Timestamp srcDate) {
    return srcDate == null ? null : new Timestamp(srcDate.getTime());
  }

  /**
   * 计算日期差(天数)
   *
   * @param startStr 开始时间
   * @param endStr   结束时间
   * @return 日期差(天数)
   */
  public String calTimeDiffer(String startStr, String endStr) {
    final DateTime startTime = DateTime.parse(startStr);
    final DateTime endTime = DateTime.parse(endStr);

    //计算区间天数
    final Period period = new Period(startTime, endTime, PeriodType.days());
    final int days = period.getDays();

    return String.valueOf(days);
  }

  /**
   * LocalDate -> Date
   */
  public static Date asDate(LocalDate localDate) {
    return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
  }

  /**
   * LocalDateTime -> Date
   */
  public Date asDate(LocalDateTime localDateTime) {
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  /**
   * Date -> LocalDate
   */
  public LocalDate asLocalDate(Date date) {
    return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
  }

  /**
   * Date -> LocalDate
   */
  public static LocalDate asLocalDate(String date) {
    return LocalDate.parse(date, FORMATTER_YYYYMMDD);
  }

  /**
   * Date -> LocalDate
   */
  public LocalDate asLocalDate(String date, String format) {
    return LocalDate.parse(date, DateTimeFormatter.ofPattern(format));
  }

  /**
   * LocalDate -> String
   */
  public static String formatLocalDate(LocalDate localDate) {
    return FORMATTER_YYYYMMDD.format(localDate);
  }

  /**
   * LocalDate -> String
   */
  public static String formatLocalDate(LocalDate localDate, String format) {

    return DateTimeFormatter.ofPattern(format).format(localDate);
  }

  /**
   * Date -> LocalDateTime
   */
  public LocalDateTime asLocalDateTime(Date date) {
    return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
  }

  /**
   * timestamp -> LocalTime
   *
   * @param timeStamp 时间戳
   * @return LocalTime对象
   */
  public LocalTime asLocalTime(long timeStamp) {
    return LocalDateTime.ofInstant(Instant.ofEpochSecond(timeStamp), ZoneId.systemDefault())
        .toLocalTime();
  }

  /**
   * timestamp -> LocalDateTime
   *
   * @param timeStamp 时间戳
   * @return LocalDateTime对象
   */
  public LocalDateTime asLocalDateTime(long timeStamp) {
    return LocalDateTime.ofInstant(Instant.ofEpochSecond(timeStamp), ZoneId.systemDefault());
  }

  /**
   * LocalDateTime -> timestamp
   *
   * @param localDateTime LocalDateTime对象
   * @return 时间戳
   */
  public Long asTimeStamp(LocalDateTime localDateTime) {
    return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
  }

  /**
   * 格式化 yyyy-MM-dd HH:mm:ss
   */
  public static String formatDateTimeSecond(LocalDateTime localDateTime) {
    return FORMATTER_YYYYMMDDHHMMSS.format(localDateTime);
  }

  public static String formatDateTimeYearMonthDay(LocalDateTime localDateTime) {
    return FORMATTER_YMD.format(localDateTime);
  }

  /**
   * 格式化 yyyy-MM-dd HH
   */
  public String formatDateTime(LocalDateTime localDateTime) {
    return FORMATTER_YYYYMMDDHH.format(localDateTime);
  }

  /**
   * 格式化 yyyy年MM月dd日H时
   */
  public String formatDateTimeZh(LocalDateTime localDateTime) {
    return FORMATTER_YYYYMMDDHHZH.format(localDateTime);
  }

  /**
   * 格式化 dd日HH时
   */
  public String formatDateTimeDayHour(LocalDateTime localDateTime) {
    return FORMATTER_DDHH.format(localDateTime);
  }

  /**
   * 格式化 HH时
   */
  public String formatDateTimeHour(LocalDateTime localDateTime) {
    return FORMATTER_HH.format(localDateTime);
  }

  /**
   * 格式化
   */
  public static String formatDateTime(LocalDateTime localDateTime, String format) {
    return DateTimeFormatter.ofPattern(format).format(localDateTime);
  }

  /**
   * 比较第一个日期是否小于第二个日期
   *
   * @param firstDate  第一个日期
   * @param secondDate 第二个日期
   * @return true-小于;false-大于
   */
  public boolean localDateIsBefore(LocalDate firstDate, LocalDate secondDate) {
    return firstDate.isBefore(secondDate);
  }

  /**
   * 计算月份差
   * @param startDate
   * @param endDate
   * @return
   */
  public static long calMonth(String startDate, String endDate) {
    //开始时间
    final LocalDate localDateStart = asLocalDate(startDate);
    //结束时间
    final LocalDate localDateEnd = asLocalDate(endDate);
    //计算月份差
    return localDateStart.until(localDateEnd, ChronoUnit.MONTHS);
  }

  /**
   * 计算日期差
   * @param startDate
   * @param endDate
   * @return
   */
  public static long calDay(String startDate, String endDate) {
    //开始时间
    final LocalDate localDateStart = asLocalDate(startDate);
    //结束时间
    final LocalDate localDateEnd = asLocalDate(endDate);

    //计算日期差
    return localDateStart.until(localDateEnd, ChronoUnit.DAYS);
  }

  /**
   * 获取该日期的第一天及最后一天
   *
   * @param localDate 日期
   * @return
   */
  public static String[] getFirstAndLast(LocalDate localDate) {
    String first = FORMATTER_YYYYMMDD.format(localDate.with(TemporalAdjusters.firstDayOfMonth()));
    String last = FORMATTER_YYYYMMDD.format(localDate.with(TemporalAdjusters.lastDayOfMonth()));
    return new String[]{first, last};
  }

  /**
   * 获取今天
   *
   * @return
   */
  public static String getToday() {
    return FORMATTER_YYYYMMDD.format(LocalDate.now());
  }

  /**
   * 判断字符串是否为合法的日期格式
   *
   * @param dateStr 待判断的字符串
   * @return
   */
  public static boolean isValidDate(String dateStr) {
    //判断结果 默认为true
    boolean judgeResult = true;
    //1、首先使用SimpleDateFormat初步进行判断，过滤掉注入 yyyy-01-32 或yyyy-00-0x等格式
    //此处可根据实际需求进行调整，如需判断yyyy/MM/dd格式将参数改掉即可
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
    try {
      //增加强判断条件，否则 诸如2022-02-29也可判断出去
      format.setLenient(false);
      Date date = format.parse(dateStr);
      System.out.println(date);
    } catch (Exception e) {
      return false;
    }
    //由于上述方法只能验证正常的日期格式，像诸如 0001-01-01、11-01-01，10001-01-01等无法校验，此处再添加校验年费是否合法
    String yearStr = dateStr.split("-")[0];
    if (yearStr.startsWith("0") || yearStr.length() != 4 || dateStr.length() != 10) {
      judgeResult = false;
    }
    return judgeResult;
  }

  public static String addMinutes(String date ,Integer minutes){
      Long time = minutes*60*1000L;
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      Date sdfData = new Date();
      try {
          sdfData = sdf.parse(date);
      } catch (ParseException e) {
          e.printStackTrace();
      }
      Date beforeDate2 = new Date(sdfData.getTime() + time);
      return sdf.format(beforeDate2);
  }

}
