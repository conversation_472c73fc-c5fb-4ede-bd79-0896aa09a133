package com.htks.common.utils;


import com.htks.web.model.DomainUser;
import lombok.extern.slf4j.Slf4j;

import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.*;
import javax.naming.ldap.InitialLdapContext;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Hashtable;

@Slf4j
public class DomainUtil {

  public static String HOST = "***********";
  public static String PORT = "389";


  public static boolean validate(String user, String password) {
    DirContext ctx = null;
    try {
      ctx = connect(user, password);
      if (ctx != null) {
        ctx.close();
      }
    } catch (Exception ignored) {

      return false;

    } finally {
      if (null != ctx) {
        try {
          ctx.close();
          ctx = null;
        } catch (Exception ignored) {
        }
      }
    }
    return true;
  }

  public static DirContext connect(String user, String password) throws Exception {
    DirContext ctx = null;
    Hashtable<String, String> HashEnv = new Hashtable<String, String>();
    HashEnv.put(Context.SECURITY_AUTHENTICATION, "simple"); // LDAP访问安全级别(none,simple,strong)
    HashEnv.put(Context.SECURITY_PRINCIPAL, "ht-tech\\" + user); // AD的用户名
    HashEnv.put(Context.SECURITY_CREDENTIALS, password); // AD的密码
    HashEnv.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory"); // LDAP工厂类
    HashEnv.put("com.sun.jndi.ldap.connect.timeout", "3000");// 连接超时设置为3秒
    HashEnv.put(Context.PROVIDER_URL, "ldap://" + HOST + ":" + PORT);// 默认端口389
    try {
      ctx = new InitialLdapContext(HashEnv, null);
    } catch (AuthenticationException e) {
      log.error("身份验证失败:", e);
      throw new Exception("身份验证失败!");
    } catch (javax.naming.CommunicationException e) {
      log.error("AD域连接失败:", e);
      throw new Exception("AD域连接失败!");
    } catch (Exception e) {
      log.error("AD域连接失败:", e);
      throw new Exception("AD域连接失败!" + e.getMessage());
    }
    return ctx;
  }

  public static DomainUser convert(Attributes Attrs)
      throws NamingException, IllegalAccessException, InstantiationException, NoSuchMethodException, InvocationTargetException {
    DomainUser user = DomainUser.class.getDeclaredConstructor().newInstance();
    NamingEnumeration<? extends Attribute> namingEnumeration = Attrs.getAll();
    Field[] fields = DomainUser.class.getDeclaredFields();
    HashMap<String, String> avs = new HashMap<>();
    while (namingEnumeration.hasMore()) {
      Attribute attribute = (Attribute) namingEnumeration.next();
      String id = attribute.getID().replace("-", "");
      String value = attribute.getAll().nextElement().toString();
      avs.put(id, value);
    }
    for (Field field : fields) {
      field.set(user, avs.get(field.getName()));
    }
    return user;
  }

  public static DomainUser selectUserInfo(String user) {
    DirContext ctx = null;
    try {
      ctx = connect("sqladmin", "ksitadmin");
      SearchControls searchControls = new SearchControls(); //Create the search controls
      searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE); //Specify the search scope
      String searchFilter =
          "(&(objectClass=User)(sAMAccountName=" + user + "))"; //specify the LDAP search filter
      String searchBase = "DC=ht-tech,DC=com"; //Specify the Base for the search//搜索域节点
      searchControls.setReturningAttributes(null);
      NamingEnumeration<SearchResult> answer = ctx.search(searchBase, searchFilter, searchControls);
      if (answer == null) {
        return null;
      }
      if (!answer.hasMoreElements()) {
        return null;
      }
      SearchResult searchResult = answer.next();
      Attributes Attrs = searchResult.getAttributes();
      return convert(Attrs);
    } catch (Exception ignore) {
      ignore.printStackTrace();

    } finally {
      if (ctx != null) {
        try {
          ctx.close();
        } catch (NamingException e) {
          e.printStackTrace();
        }
      }
    }

    return null;
  }

}