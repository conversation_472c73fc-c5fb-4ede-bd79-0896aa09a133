package com.htks.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义限流注解
 *
 * <AUTHOR>
 * @date 2020/09/28.
 */
@Documented
@Target(value = ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DefineRateLimiter {

  //每秒处理数(每秒生成令牌数)
  double permitsPerSecond();

  //超时时间，单位毫秒
  long timeout();
}
