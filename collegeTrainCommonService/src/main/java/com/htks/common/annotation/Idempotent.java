package com.htks.common.annotation;

import java.lang.annotation.*;

/**
 * 幂等性注解
 * 用于标记需要进行幂等性控制的方法
 *
 * <AUTHOR> Generated
 * @date 2025/07/09
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Idempotent {

    /**
     * 业务标识，用于区分不同的业务场景
     * 支持SpEL表达式
     */
    String businessKey() default "";

    /**
     * 幂等性token参数名
     * 默认从请求头中获取 "Idempotent-Token"
     */
    String tokenName() default "Idempotent-Token";

    /**
     * token来源类型
     */
    TokenSource tokenSource() default TokenSource.HEADER;

    /**
     * 失败时的错误消息
     */
    String message() default "请勿重复提交";

    /**
     * token来源枚举
     */
    enum TokenSource {
        /**
         * 从请求头获取
         */
        HEADER,
        /**
         * 从请求参数获取
         */
        PARAMETER,
        /**
         * 从请求体获取
         */
        BODY
    }
}
