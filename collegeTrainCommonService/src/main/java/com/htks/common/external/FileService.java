package com.htks.common.external;

import java.io.File;
import java.io.IOException;

/**
 * 文件服务接口
 *
 * <AUTHOR>
 * @date 2018/01/08
 */
public interface FileService {

  /**
   * 写文件
   *
   * @param fileBytes     文件字节数组
   * @param toDirFullPath 目标文件夹
   * @param fileName      文件名
   */
  void write(byte[] fileBytes, String toDirFullPath, String fileName);

  /**
   * 写文件(可处理图片压缩)
   *
   * @param fileBytes     文件字节数组
   * @param toDirFullPath 目标文件夹
   * @param extension     文件后缀
   * @return 文件名
   */
  String writeExt(byte[] fileBytes, String toDirFullPath, String extension);

  /**
   * 读文件
   *
   * @param fromFullPath 源文件完整路径
   * @return 文件字节数组
   */
  byte[] readFile(String fromFullPath);

  /**
   * 移动文件
   *
   * @param fromFullPath 源文件完整路径
   * @param toFullPath   目标文件完整路径
   */
  void move(String fromFullPath, String toFullPath);

  /**
   * 拷贝文件
   *
   * @param fromFullPath 源文件完整路径
   * @param toFullPath   目标文件完整路径
   */
  void copy(String fromFullPath, String toFullPath);

  /**
   * 删除文件
   *
   * @param file 文件
   */
  void delete(File file);

  /**
   * 创建父级目录
   *
   * @param file file
   * @throws IOException IOException
   */
  void createParentDirs(File file) throws IOException;
}
