package com.htks.common.external.wx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("统一响应结果")
@ToString
public class ResultVO<T> {
    @ApiModelProperty(value = "状态码", notes = "默认1000是成功")
    private int code;
    @ApiModelProperty(value = "响应信息", notes = "来说明响应情况")
    private String message;
    @ApiModelProperty(value = "响应的具体数据")
    private T data;

    public ResultVO(T data) {
        this(ResultCode.SUCCESS, data);
    }

    public ResultVO(Integer code, String msg, T data) {
        this.code = code;
        this.message = msg;
        this.data = data;
    }

    public ResultVO(ResultCode resultCode, T data) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
        this.data = data;
    }

    public ResultVO(Integer code, String msg) {
        this.code = code;
        this.message = msg;
        this.data = null;
    }

    public static ResultVO success(Object data) {
        ResultVO obj = new ResultVO();
        obj.setCode(ResultCode.SUCCESS.getCode());
        obj.setMessage(ResultCode.SUCCESS.getMsg());
        obj.setData(data);
        return obj;
    }

    public static ResultVO success() {
        ResultVO obj = new ResultVO();
        obj.setCode(ResultCode.SUCCESS.getCode());
        obj.setMessage(ResultCode.SUCCESS.getMsg());
        return obj;
    }

    public static ResultVO success(String msg) {
        ResultVO obj = new ResultVO();
        obj.setCode(ResultCode.SUCCESS.getCode());
        obj.setMessage(msg);
        return obj;
    }

    public static ResultVO error(String msg) {
        ResultVO obj = new ResultVO();
        obj.setCode(ResultCode.FAILED.getCode());
        obj.setMessage(msg);
        return obj;
    }

    public static ResultVO error(int code,String msg) {
        ResultVO obj = new ResultVO();
        obj.setCode(ResultCode.FAILED.getCode());
        obj.setMessage(msg);
        return obj;
    }
}
