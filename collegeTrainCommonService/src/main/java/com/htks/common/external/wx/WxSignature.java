package com.htks.common.external.wx;

import lombok.Data;

/**
 * <AUTHOR>
 * @dateTime: 2022-2-25
 * @description； 企业微信实体
 * */
@Data
public class WxSignature {
    private String jsapiTicket;
    //企业微信的corpId
    private String corpid;
    // 企业微信应用id
    private Integer agentId;
    // 签名时间戳
    private long timestamp;
    // 簽名隨機串
    private String nonceStr;
    // 签名
    private String signature;

    public String getSignature(String url) {
        String res = "jsapi_ticket=" + this.jsapiTicket+"&noncestr="+this.nonceStr+"&timestamp="+this.timestamp+"&url="+url;
        return res;
    }

}
