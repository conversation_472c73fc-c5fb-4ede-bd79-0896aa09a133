package com.htks.common.external;


import com.htks.common.config.WeChatConfig;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.external.wx.WxSignature;
import com.htks.common.utils.ShaUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

/**
 * <AUTHOR>
 * @dateTime: 2022-2-25
 * @description: 微信service
 * */
@Service
public class WxService {

    public ResultVO<WxSignature> getWxJsSdkConfig(WeChatConfig wxConfig, String url) throws Exception{
        WxSignature wxSignature = new WxSignature();
        wxSignature.setCorpid(wxConfig.getCorpId());
        wxSignature.setJsapiTicket(wxConfig.getWxCpService().getJsapiTicket());
        wxSignature.setTimestamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        wxSignature.setAgentId(wxConfig.getAgentId());
        wxSignature.setNonceStr(UUID.randomUUID().toString());
        wxSignature.setSignature(ShaUtil.SHA1(wxSignature.getSignature(url)));
        System.out.println(wxSignature.toString());
        return new ResultVO<>(wxSignature);
    }
}
