package com.htks.common.external.impl;

import static com.google.common.collect.Maps.newHashMap;
import static com.htks.common.SystemConfig.DEFAULT_COMMA;
import static com.htks.common.SystemConfig.HALF_FILE_SIZE;

import com.google.common.io.Files;
import com.htks.common.external.FileService;
import com.htks.common.utils.SystemUtils;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 文件服务接口实现类
 *
 * <AUTHOR>
 * @date 2019/07/11
 */
@Slf4j
@Component
public final class FileServiceImpl implements FileService {

  @Override
  public void write(byte[] fileBytes, String toDirFullPath, String fileName) {
    final File outFile = new File(toDirFullPath + fileName);
    try {
      createParentDirs(outFile);
      if (null != fileBytes) {
        Files.write(fileBytes, outFile);
      }
    } catch (IOException e) {
      log.error("Write File Error:", e);
    }
  }

  @Override
  public String writeExt(byte[] fileBytes, String toDirFullPath, String extension) {
    final SystemUtils systemUtils = new SystemUtils();
    final String fileName = systemUtils.nextStringIdentifier() + DEFAULT_COMMA + extension;
    final File outFile = new File(toDirFullPath + fileName);
    try {
      createParentDirs(outFile);
      if (StringUtils.isNotEmpty(extension) && imageTypeMap(extension)) {
        final InputStream byteArrayInputStream = new ByteArrayInputStream(fileBytes);
        final int size = byteArrayInputStream.available();
        //判断附件大小 是否超过0.5M
        if (size > HALF_FILE_SIZE) {
          //大于0.5M则按0.5 的比例压缩
          Thumbnails.of(byteArrayInputStream)
              .size(1920, 1080)
              .outputQuality(0.5f)
              .toFile(outFile);
        } else {
          //小于等于0.5M则按原始比例保存
          Files.write(fileBytes, outFile);
        }
      } else {
        Files.write(fileBytes, outFile);
      }
    } catch (IOException e) {
      log.error("Write File Error:", e);
    }
    return fileName;
  }

  @Override
  public byte[] readFile(String fromFullPath) {
    final File file = new File(fromFullPath);
    if (file.exists()) {
      try {
        return Files.toByteArray(file);
      } catch (IOException e) {
        log.error("Read File Error:", e);
        return new byte[0];
      }
    } else {
      return new byte[0];
    }
  }

  @Override
  public void move(String fromFullPath, String toFullPath) {
    final File fromFile = new File(fromFullPath);
    final File toFile = new File(toFullPath);
    if (fromFile.exists()) {
      try {
        createParentDirs(toFile);
        Files.move(fromFile, toFile);
      } catch (IOException e) {
        log.error("Move File Error:", e);
      }
    }
  }

  @Override
  public void copy(String fromFullPath, String toFullPath) {
    final File fromFile = new File(fromFullPath);
    final File toFile = new File(toFullPath);
    if (fromFile.exists()) {
      try {
        createParentDirs(toFile);
        Files.copy(fromFile, toFile);
      } catch (IOException e) {
        log.error("Copy File Error:", e);
      }
    }
  }

  @Override
  public void delete(File file) {
    final Path path = Paths.get(file.getPath());
    try {
      java.nio.file.Files.delete(path);
    } catch (IOException e) {
      log.error("Delete File Error:{}", e.getMessage());
    }
  }

  /**
   * 创建父级目录
   *
   * @param file file
   * @throws IOException IOException
   */
  @Override
  public void createParentDirs(File file) throws IOException {
    if (!file.getParentFile().exists()) {
      //创建文件目录
      Files.createParentDirs(file);
      if (log.isDebugEnabled()) {
        log.debug("创建文件目录:{}", file.getParentFile());
      }
    }

    //设置文件相关权限
    final Boolean executableResult = file.setExecutable(false);
    final Boolean readableResult = file.setReadable(true, false);
    final Boolean writableResult = file.setWritable(true);

    if (log.isDebugEnabled()) {
      log.debug("设置文件相关权限:{}", file.getPath());
      log.debug("Executable:{}", executableResult);
      log.debug("Readable:{}", readableResult);
      log.debug("Writable:{}", writableResult);
    }

    if (!Boolean.TRUE.equals(executableResult) || !Boolean.TRUE.equals(readableResult)
        || !Boolean.TRUE.equals(writableResult)) {
      log.error("设置文件权限错误.");
    }
  }

  private static Boolean imageTypeMap(String imageType) {
    final Map<String, String> imageTypeMap = newHashMap();
    imageTypeMap.put("jpg", "jpg");
    imageTypeMap.put("jpeg", "jpeg");
    imageTypeMap.put("png", "png");
    if (imageTypeMap.containsValue(imageType.toLowerCase())) {
      return Boolean.TRUE;
    }
    return Boolean.FALSE;
  }
}
