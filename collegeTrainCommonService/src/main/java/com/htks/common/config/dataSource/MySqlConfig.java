package com.htks.common.config.dataSource;

import com.zaxxer.hikari.HikariDataSource;
import javax.sql.DataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * Mysql数据源配置
 *
 * <AUTHOR>
 * @date 2022/03/25.
 */
@Configuration
@MapperScan(basePackages = "com.htks.domain.*.repository.mysql",
    sqlSessionTemplateRef = "sqlSessionTemplateMySql")
public class MySqlConfig {

  @Bean(name = "mysqlDataSource")
  @Qualifier(value = "mysqlDataSource")
  @ConfigurationProperties(prefix = "spring.datasource.mysql.first")
  public DataSource dataSourceMySql() {
    return DataSourceBuilder.create().type(HikariDataSource.class).build();
  }

  @Bean(name = "sqlSessionFactoryMySql")
  public SqlSessionFactory sqlSessionFactoryMySql(
      @Qualifier("mysqlDataSource") DataSource dataSource) throws Exception {
    final ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    final SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
    factory.setDataSource(dataSource);
    factory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
    factory.setConfigLocation(
        new PathMatchingResourcePatternResolver().getResource("classpath:mybatis.xml"));
    factory.setTypeAliasesPackage("com.htks.domain.**.dto");
    return factory.getObject();
  }

  @Bean(name = "dataSourceTransactionManagerMySql")
  public DataSourceTransactionManager dataSourceTransactionManagerMySql(
      @Qualifier("mysqlDataSource") DataSource dataSource) {
    return new DataSourceTransactionManager(dataSource);
  }

  @Bean(name = "sqlSessionTemplateMySql")
  public SqlSessionTemplate sqlSessionTemplateMySql(
      @Qualifier("sqlSessionFactoryMySql") SqlSessionFactory sqlSessionFactory) {
    return new SqlSessionTemplate(sqlSessionFactory);
  }

}
