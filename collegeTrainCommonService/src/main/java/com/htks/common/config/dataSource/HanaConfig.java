package com.htks.common.config.dataSource;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator;
import com.baomidou.mybatisplus.extension.incrementer.SapHanaKeyGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariDataSource;
import javax.sql.DataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import java.util.Arrays;

/**
 * Hana数据源配置
 *
 * <AUTHOR>
 * @date 2022/05/16.
 */
@Configuration
@MapperScan(basePackages = "com.htks.domain.*.repository.hana",
    sqlSessionTemplateRef = "sqlSessionTemplateHana")
public class HanaConfig {

  @Primary
  @Bean(name = "hanaDataSource")
  @Qualifier(value = "hanaDataSource")
  @ConfigurationProperties(prefix = "spring.datasource.hana.first")
  public DataSource dataSourceHana() {
    return DataSourceBuilder.create().type(HikariDataSource.class).build();
  }

  @Primary
  @Bean(name = "sqlSessionFactoryHana")
  public SqlSessionFactory sqlSessionFactoryHana(
      @Qualifier("hanaDataSource") DataSource dataSource) throws Exception {
    final ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    final MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
    factory.setDataSource(dataSource);
    factory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
    factory.setConfigLocation(
        new PathMatchingResourcePatternResolver().getResource("classpath:mybatis.xml"));
    factory.setTypeAliasesPackage("com.htks.domain.**.dto");
    factory.setGlobalConfig(globalConfig());
    factory.setPlugins(mybatisPlusInterceptor());
    return factory.getObject();
  }

  @Bean
  public MybatisPlusInterceptor mybatisPlusInterceptor() {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
    interceptor.addInnerInterceptor(new
            PaginationInnerInterceptor(DbType.SAP_HANA));
    return interceptor;
  }


  @Bean
  public GlobalConfig globalConfig() {
    GlobalConfig conf = new GlobalConfig();
    conf.setDbConfig(new GlobalConfig.DbConfig().setKeyGenerators(Arrays.asList(new SapHanaKeyGenerator())));
    return conf;
  }

  @Primary
  @Bean(name = "dataSourceTransactionManagerHana")
  public DataSourceTransactionManager dataSourceTransactionManagerHana(
      @Qualifier("hanaDataSource") DataSource dataSource) {
    return new DataSourceTransactionManager(dataSource);
  }

  @Primary
  @Bean(name = "sqlSessionTemplateHana")
  public SqlSessionTemplate sqlSessionTemplateHana(
      @Qualifier("sqlSessionFactoryHana") SqlSessionFactory sqlSessionFactory) {
    return new SqlSessionTemplate(sqlSessionFactory);
  }

}
