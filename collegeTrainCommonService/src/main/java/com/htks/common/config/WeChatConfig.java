package com.htks.common.config;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.htks.common.SystemConfig.APP_AGENT_PREFIX;

import com.alibaba.fastjson.JSON;
import com.htks.common.utils.JedisUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 企业微信配置
 *
 * <AUTHOR>
 * @date 2022/07/21.
 */
@Slf4j
@Getter
@Configuration
public class WeChatConfig {

  @Value("${wechat.collegeTrain.secret}")
  private String secret;

  @Value("${wechat.collegeTrain.corpId}")
  private String corpId;

  @Value("${wechat.collegeTrain.agentId}")
  private Integer agentId;

  private final JedisUtils jedisUtils;

  public WeChatConfig(JedisUtils jedisUtils) {
    this.jedisUtils = jedisUtils;
  }

  public WxCpService getWxCpService() {
    final WxCpService wxCpService = new WxCpServiceImpl();
    final WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
    config.setAgentId(agentId);
    config.setCorpSecret(secret);
    config.setCorpId(corpId);
    resetTokenAndJsApi(wxCpService, config);
    return wxCpService;
  }

  public void resetTokenAndJsApi(WxCpService wxCpService, WxCpDefaultConfigImpl wxCpDefaultConfig) {
    wxCpService.setWxCpConfigStorage(wxCpDefaultConfig);
    final String wxAccessToken = APP_AGENT_PREFIX + this.agentId;
    final String json = jedisUtils.get(wxAccessToken);
    if (!isNullOrEmpty(json)) {
      wxCpDefaultConfig = JSON.parseObject(json, WxCpDefaultConfigImpl.class);
    }
    if (wxCpDefaultConfig.isAccessTokenExpired()) {
      try {
        final String accessToken = wxCpService.getAccessToken(false);
        wxCpDefaultConfig.setAccessToken(accessToken);
      } catch (WxErrorException e) {
        if(e.getError().getErrorCode()==45009){
        }else {
          log.error("WxErrorException:", e);
        }
      }
    }
    if (wxCpDefaultConfig.isJsapiTicketExpired()) {
      try {
        final String jsApi = wxCpService.getJsapiTicket();
        wxCpDefaultConfig.setJsapiTicket(jsApi);
      } catch (WxErrorException e) {
        /*if (e.getError().getErrorCode() == 45009) {
        } else {*/
          log.error("WxErrorException:", e);

      }
    }
    jedisUtils.set(wxAccessToken, JSON.toJSONString(wxCpDefaultConfig));
  }

}
