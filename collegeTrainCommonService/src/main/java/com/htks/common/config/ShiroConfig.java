package com.htks.common.config;

import static com.google.common.collect.Maps.newLinkedHashMap;
import static javax.servlet.DispatcherType.ASYNC;
import static javax.servlet.DispatcherType.REQUEST;

import com.htks.domain.sys.shiro.UserAuthorizingRealm;
import com.htks.domain.sys.shiro.filter.UserAuthenticatingFilter;
import java.util.Map;
import javax.servlet.Filter;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.cache.ehcache.EhCacheManager;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.session.mgt.eis.EnterpriseCacheSessionDAO;
import org.apache.shiro.session.mgt.eis.JavaUuidSessionIdGenerator;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.session.mgt.eis.SessionIdGenerator;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

/**
 * Shiro配置
 *
 * <AUTHOR>
 * @date 2020/09/04
 */
@Configuration
public class ShiroConfig {

  @Bean("delegatingFilterProxy")
  public FilterRegistrationBean delegatingFilterProxy() {
    final FilterRegistrationBean<Filter> filterRegistrationBean = new FilterRegistrationBean<>();
    final DelegatingFilterProxy proxy = new DelegatingFilterProxy();
    proxy.setTargetBeanName("shiroFilter");
    proxy.setTargetFilterLifecycle(true);
    filterRegistrationBean.setFilter(proxy);

    //支持异步请求(例如使用webflux时需要)
    filterRegistrationBean.setAsyncSupported(true);
    filterRegistrationBean.setDispatcherTypes(REQUEST, ASYNC);

    return filterRegistrationBean;
  }

  @Bean(name = "sessionManager")
  public SessionManager sessionManager() {
    final DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    //去掉shiro登录时url里的JSESSIONID
    sessionManager.setSessionIdUrlRewritingEnabled(false);
    //指定SessionId
    sessionManager.setSessionIdCookieEnabled(true);
    sessionManager.setSessionIdCookie(sessionIdCookie());
    sessionManager.setSessionDAO(sessionDAO());
    //超时时间(毫秒)
    sessionManager.setGlobalSessionTimeout(60 * 60 * 1000);
    //删除无效的Session
    sessionManager.setDeleteInvalidSessions(true);
    // 定时清理失效Session, 清理用户直接关闭浏览器造成的孤立Session
    sessionManager.setSessionValidationSchedulerEnabled(true);
    sessionManager.setSessionValidationInterval(60 * 60 * 1000);
    return sessionManager;
  }

  @Bean(name = "ehCacheManager")
  public CacheManager getCacheManager() {
    final EhCacheManager cacheManager = new EhCacheManager();
    cacheManager.setCacheManagerConfigFile("classpath:ehcache.xml");
    return cacheManager;
  }

  @Bean(name = "securityManager")
  public SecurityManager securityManager(
      @Qualifier("userAuthorizingRealm") UserAuthorizingRealm userAuthorizingRealm,
      @Qualifier("sessionManager") SessionManager sessionManager,
      @Qualifier("ehCacheManager") CacheManager cacheManager) {
    final DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();

    //允许缓存
    //userAuthorizingRealm.setCachingEnabled(true);
    //允许认证缓存
    //userAuthorizingRealm.setAuthenticationCachingEnabled(true);
    //userAuthorizingRealm.setAuthenticationCacheName("authenticationCache");
    //允许授权缓存
    //userAuthorizingRealm.setAuthorizationCachingEnabled(true);
    //userAuthorizingRealm.setAuthorizationCacheName("authorizationCache");

    securityManager.setRealm(userAuthorizingRealm);
    securityManager.setSessionManager(sessionManager);
    securityManager.setCacheManager(cacheManager);
    return securityManager;
  }

  @Bean(name = "shiroFilter")
  public ShiroFilterFactoryBean shirFilter(
      @Qualifier("securityManager") SecurityManager securityManager,
      @Qualifier("userAuthenticatingFilter") Filter userAuthenticatingFilter) {
    final ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
    shiroFilter.setSecurityManager(securityManager);
    shiroFilter.setLoginUrl("/login");
    //登录成功后要跳转的链接
    //shiroFilter.setSuccessUrl("/index");
    //未授权界面;
    //shiroFilter.setUnauthorizedUrl("/error");

    //配置拦截器
    final Map<String, Filter> filters = newLinkedHashMap();
    filters.put("userAuthenticatingFilter", userAuthenticatingFilter);
    shiroFilter.setFilters(filters);

    //配置过滤器 有顺序
    final Map<String, String> filterMap = newLinkedHashMap();
    //Swagger-Knife4j相关
    filterMap.put("/doc.html", "anon");
    //Swagger-Knife4j相关
    filterMap.put("/webjars/**", "anon");
    //Swagger-Knife4j相关
    filterMap.put("/swagger-resources", "anon");
    //Swagger-Knife4j相关
    filterMap.put("/v2/api-docs", "anon");
    //Swagger-Knife4j相关
    filterMap.put("/v2/api-docs-ext", "anon");
    filterMap.put("/login", "anon");
    filterMap.put("/", "anon");
    filterMap.put("/securedEx/**", "userAuthenticatingFilter");
    filterMap.put("/**", "anon");
    shiroFilter.setFilterChainDefinitionMap(filterMap);

    return shiroFilter;
  }

  @Bean(name = "customFilter")
  public Filter getCustomFilter() {
    return new UserAuthenticatingFilter();
  }

  /**
   * Shiro 生命周期处理器
   */
  @Bean("lifecycleBeanPostProcessor")
  public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
    return new LifecycleBeanPostProcessor();
  }

  /**
   * 会话读写实现类
   */
  @Bean
  public SessionDAO sessionDAO() {
    final EnterpriseCacheSessionDAO sessionDAO = new EnterpriseCacheSessionDAO();
    sessionDAO.setActiveSessionsCacheName("shiro-activeSessionCache");
    sessionDAO.setSessionIdGenerator(getSessionIdGenerator());
    return sessionDAO;
  }

  /**
   * 会话 ID 生成器
   */
  @Bean
  public SessionIdGenerator getSessionIdGenerator() {
    return new JavaUuidSessionIdGenerator();
  }

  /**
   * 会话Cookie模板
   */
  @Bean
  public SimpleCookie sessionIdCookie() {
    final SimpleCookie simpleCookie = new SimpleCookie("sid");
    simpleCookie.setHttpOnly(true);
    //有效期7天
    simpleCookie.setMaxAge(7 * 24 * 60 * 60);
    return simpleCookie;
  }

  @Bean
  public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
    DefaultAdvisorAutoProxyCreator proxyCreator = new DefaultAdvisorAutoProxyCreator();
    proxyCreator.setProxyTargetClass(true);
    return proxyCreator;
  }

  @Bean
  public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(
      SecurityManager securityManager) {
    AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
    advisor.setSecurityManager(securityManager);
    return advisor;
  }
}
