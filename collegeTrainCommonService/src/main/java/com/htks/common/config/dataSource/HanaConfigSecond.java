package com.htks.common.config.dataSource;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * Hana数据源配置
 *
 * <AUTHOR>
 * @date 2022/05/16.
 */
@Configuration
@MapperScan(basePackages = "com.htks.domain.*.repository2.hana",
    sqlSessionTemplateRef = "sqlSessionTemplateHana2")
public class HanaConfigSecond {

  @Bean(name = "hanaDataSource2")
  @Qualifier(value = "hanaDataSource2")
  @ConfigurationProperties(prefix = "spring.datasource.hana.second")
  public DataSource dataSourceHana2() {
    return DataSourceBuilder.create().type(HikariDataSource.class).build();
  }

  @Bean(name = "sqlSessionFactoryHana2")
  public SqlSessionFactory sqlSessionFactoryHana2(
      @Qualifier("hanaDataSource2") DataSource dataSource) throws Exception {
    final ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    final SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
    factory.setDataSource(dataSource);
    factory.setMapperLocations(resolver.getResources("classpath*:mapper2/**/*.xml"));
    factory.setConfigLocation(
            new PathMatchingResourcePatternResolver().getResource("classpath:mybatis.xml"));
    factory.setTypeAliasesPackage("com.htks.domain.**.dto");
    return factory.getObject();
  }

  @Bean(name = "dataSourceTransactionManagerHana2")
  public DataSourceTransactionManager dataSourceTransactionManagerHana2(
      @Qualifier("hanaDataSource2") DataSource dataSource) {
    return new DataSourceTransactionManager(dataSource);
  }

  @Bean(name = "sqlSessionTemplateHana2")
  public SqlSessionTemplate sqlSessionTemplateHana2(
      @Qualifier("sqlSessionFactoryHana2") SqlSessionFactory sqlSessionFactory) {
    return new SqlSessionTemplate(sqlSessionFactory);
  }

}
