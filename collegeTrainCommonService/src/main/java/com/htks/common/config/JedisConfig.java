package com.htks.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Jedis单机模式配置
 *
 * <AUTHOR>
 * @date 2021/07/21.
 */
@Slf4j
@Configuration
public class JedisConfig {

  @Value("${redis.host}")
  private String host;

  @Value("${redis.port}")
  private int port;

  @Value("${redis.password}")
  private String password;

  @Value("${redis.timeout}")
  private int timeout;

  @Value("${redis.database}")
  private int database;

  @Value("${redis.jedis.pool.max-idle}")
  private int maxIdle;

  @Value("${redis.jedis.pool.max-active}")
  private int maxActive;

  @Value("${redis.jedis.pool.min-idle}")
  private int minIdle;

  @Value("${redis.jedis.pool.max-wait}")
  private long maxWait;

  @Bean
  public JedisPool jedisPool() {
    final JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
    jedisPoolConfig.setMaxIdle(maxIdle);
    jedisPoolConfig.setMaxTotal(maxActive);
    jedisPoolConfig.setMinIdle(minIdle);
    jedisPoolConfig.setMaxWaitMillis(maxWait);

    final JedisPool jedisPool = new JedisPool(jedisPoolConfig, host, port, timeout, password, database);
    log.info("初始化Redis连接池JedisPool成功: {}:{}", host, port);

    return jedisPool;
  }

}
