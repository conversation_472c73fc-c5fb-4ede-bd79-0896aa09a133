package com.htks.common.config.dataSource;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * SqlServer 数据源配置
 *
 * <AUTHOR>
 * @date 2022/03/25.
 */
@Configuration
@MapperScan(basePackages = "com.htks.domain.*.repository.c3",
    sqlSessionTemplateRef = "sqlSessionTemplateSqlServer")
public class SqlServerConfig {

  @Bean(name = "sqlServerDataSource")
  @Qualifier(value = "sqlServerDataSource")
  @ConfigurationProperties(prefix = "spring.datasource.c3.c3")
  public DataSource dataSourceSqlServer() {
    return DataSourceBuilder.create().type(HikariDataSource.class).build();
  }

  @Bean(name = "sqlSessionFactorySqlServer")
  public SqlSessionFactory sqlSessionFactorySqlServer(
      @Qualifier("sqlServerDataSource") DataSource dataSource) throws Exception {
    final ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    final SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
    factory.setDataSource(dataSource);
    factory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
    factory.setConfigLocation(
        new PathMatchingResourcePatternResolver().getResource("classpath:mybatis.xml"));
    factory.setTypeAliasesPackage("com.htks.domain.**.dto");
    return factory.getObject();
  }

  @Bean(name = "dataSourceTransactionManagerSqlServer")
  public DataSourceTransactionManager dataSourceTransactionManagerSqlServer(
      @Qualifier("sqlServerDataSource") DataSource dataSource) {
    return new DataSourceTransactionManager(dataSource);
  }

  @Bean(name = "sqlSessionTemplateSqlServer")
  public SqlSessionTemplate sqlSessionTemplateSqlServer(
      @Qualifier("sqlSessionFactorySqlServer") SqlSessionFactory sqlSessionFactory) {
    return new SqlSessionTemplate(sqlSessionFactory);
  }

}
