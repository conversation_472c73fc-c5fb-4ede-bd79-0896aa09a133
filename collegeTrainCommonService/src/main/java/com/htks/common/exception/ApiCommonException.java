package com.htks.common.exception;

import com.htks.common.exception.enums.IResponseEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * API模块异常类
 *
 * <AUTHOR>
 * @date 2022/04/22.
 */
@Getter
@Setter
public class ApiCommonException extends BaseRuntimeException {

  private static final long serialVersionUID = 7375595525193595802L;

  /**
   * 异常信息
   */
  private String msg;

  /**
   * 异常编码
   */
  private int code;

  /**
   * 异常返回码枚举
   */
  private IResponseEnum responseEnum;

  public ApiCommonException(Throwable cause) {
    super(cause);
  }

  public ApiCommonException(String msg) {
    super(msg);
  }


  public ApiCommonException(String msg, Throwable cause) {
    super(msg, cause);
  }

  public ApiCommonException(String msg, int code) {
    super(msg);
    this.msg = msg;
    this.code = code;
  }

  public ApiCommonException(String msg, int code, Throwable e) {
    super(msg, e);
    this.msg = msg;
    this.code = code;
  }

  public ApiCommonException(String msg, IResponseEnum responseEnum) {
    super(msg);
    this.responseEnum = responseEnum;
  }

  public ApiCommonException(String msg, IResponseEnum responseEnum, Throwable e) {
    super(msg, e);
    this.msg = msg;
    this.code = responseEnum.getCode();
    this.responseEnum = responseEnum;
  }

  public ApiCommonException(String msg, IResponseEnum responseEnum, Object[] args) {
    super(responseEnum, args, msg);
    this.code = responseEnum.getCode();
    this.responseEnum = responseEnum;
  }

  public ApiCommonException(String msg, IResponseEnum responseEnum, Object[] args, Throwable e) {
    super(responseEnum, args, msg, e);
    this.code = responseEnum.getCode();
    this.responseEnum = responseEnum;
  }
}
