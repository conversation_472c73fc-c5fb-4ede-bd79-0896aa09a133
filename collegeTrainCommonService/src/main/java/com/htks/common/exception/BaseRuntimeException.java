package com.htks.common.exception;

import com.htks.common.exception.enums.IResponseEnum;

/**
 * 基本Runtime异常
 *
 * <AUTHOR>
 * @date 2019/04/09
 */
public class BaseRuntimeException extends RuntimeException {

  private static final long serialVersionUID = 5815394237149463907L;

  /**
   * 返回码
   */
  private IResponseEnum responseEnum;

  /**
   * 异常消息参数
   */
  private Object[] args;

  public BaseRuntimeException(IResponseEnum responseEnum) {
    super(responseEnum.getMessage());
    this.responseEnum = responseEnum;
  }

  /**
   * @param code 返回码
   * @param msg  异常描述
   */
  public BaseRuntimeException(int code, String msg) {
    super(msg);
    this.responseEnum = new IResponseEnum() {
      @Override
      public int getCode() {
        return code;
      }

      @Override
      public String getMessage() {
        return msg;
      }
    };
  }

  /**
   * @param responseEnum 返回码
   * @param args         异常消息参数
   * @param msg          异常描述
   */
  public BaseRuntimeException(IResponseEnum responseEnum, Object[] args, String msg) {
    super(msg);
    this.responseEnum = responseEnum;
    this.args = args;
  }

  /**
   * @param responseEnum 返回码
   * @param args         异常消息参数
   * @param msg          异常描述
   * @param cause        异常
   */
  public BaseRuntimeException(IResponseEnum responseEnum, Object[] args, String msg,
      Throwable cause) {
    super(msg, cause);
    this.responseEnum = responseEnum;
    this.args = args;
  }

  /**
   * 仅包装错误信息
   *
   * @param msg 异常描述
   */
  public BaseRuntimeException(String msg) {
    super(msg);
  }

  /**
   * @param cause 异常
   */
  public BaseRuntimeException(Throwable cause) {
    super(cause);
  }

  /**
   * 仅包装错误信息
   *
   * @param msg   异常描述
   * @param cause 异常
   */
  public BaseRuntimeException(String msg, Throwable cause) {
    super(msg, cause);
  }
}
