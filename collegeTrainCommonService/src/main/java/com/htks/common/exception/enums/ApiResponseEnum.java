package com.htks.common.exception.enums;

import com.htks.common.exception.assertion.ApiExceptionAssert;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * API模块异常枚举
 *
 * <AUTHOR>
 * @date 2022/04/22.
 */
@Getter
@AllArgsConstructor
public enum ApiResponseEnum implements ApiExceptionAssert {

  //错误编码以 91XX 格式。序号顺序增加
  QUERY_DATE_IS_NULL(9101, "查询日期不能空"),
  START_DATE_IS_NULL(9102, "开始日期不能空"),
  END_DATE_IS_NULL(9103, "结束日期不能空"),
  
  //未知API异常
  DEFAULT(9199, "未知API异常");

  /**
   * 返回码
   */
  private int code;

  /**
   * 返回消息
   */
  private String message;
}
