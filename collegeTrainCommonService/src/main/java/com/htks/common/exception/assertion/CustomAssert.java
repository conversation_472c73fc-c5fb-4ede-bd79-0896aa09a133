package com.htks.common.exception.assertion;

import static com.htks.common.utils.DateUtils.calMonth;
import static org.apache.commons.lang.ArrayUtils.isNotEmpty;

import com.google.common.base.Strings;
import com.htks.common.exception.BaseRuntimeException;
import com.htks.common.utils.DateUtils;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 自定义枚举类异常断言
 * <p>
 * 条件满足时抛出异常
 * <p>
 * 错误码和异常信息定义在枚举类中
 *
 * <AUTHOR>
 * @date 2022/04/22.
 */
public interface CustomAssert {

    /**
     * 创建异常
     *
     * @param args 参数
     * @return 异常信息
     */
    BaseRuntimeException createException(Object... args);

    /**
     * 创建异常
     *
     * @param t    异常
     * @param args 参数
     * @return 异常信息
     */
    BaseRuntimeException createException(Throwable t, Object... args);

    /**
     * 创建异常.
     *
     * @param errMsg 异常信息
     * @param args   参数
     * @return 异常信息
     */
    default BaseRuntimeException createExceptionWithMsg(String errMsg, Object... args) {
        if (isNotEmpty(args)) {
            errMsg = MessageFormat.format(errMsg, args);
        }

        final BaseRuntimeException e = new BaseRuntimeException(errMsg);
        throw createException(e, args);
    }

    /**
     * 创建异常
     *
     * @param errMsg 异常信息
     * @param t      异常
     * @param args   参数
     * @return 异常信息
     */
    default BaseRuntimeException createExceptionWithMsg(String errMsg, Throwable t, Object... args) {
        if (isNotEmpty(args)) {
            errMsg = MessageFormat.format(errMsg, args);
        }

        final BaseRuntimeException e = new BaseRuntimeException(errMsg, t);
        throw createException(e, args);
    }

    /**
     * 断言对象非空
     *
     * @param obj 对象
     */
    default void assertNotNull(Object obj) {
        if (obj == null) {
            throw createException();
        }
    }

    /**
     * 断言对象非空
     * <p>
     * 支持传递参数方式
     *
     * @param obj  对象
     * @param args msg占位符对应的参数列表
     */
    default void assertNotNull(Object obj, Object... args) {
        if (obj == null) {
            throw createException(args);
        }
    }

    /**
     * 断言对象非空
     *
     * @param obj    对象
     * @param errMsg 异常信息
     */
    default void assertNotNullWithMsg(Object obj, String errMsg) {
        if (obj == null) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言对象非空
     * <p>
     * 异常信息支持传递参数方式，支持 {index} 形式的占位符
     * <p>
     * 例如: errMsg传入：参数[{0}]为空, args传入：XXX, 最后输出：参数[XXX]为空
     *
     * @param obj    对象
     * @param errMsg 异常信息
     * @param args   errMsg占位符对应的参数列表
     */
    default void assertNotNullWithMsg(Object obj, String errMsg, Object... args) {
        if (obj == null) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言对象非空
     *
     * @param obj    对象
     * @param errMsg 异常信息
     */
    default void assertNotNullWithMsg(Object obj, Supplier<String> errMsg) {
        if (obj == null) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言对象非空
     * <p>
     * 异常信息支持传递参数方式，支持 {index} 形式的占位符
     * <p>
     * 例如: errMsg传入：参数[{0}]为空, args传入：XXX, 最后输出：参数[XXX]为空
     *
     * @param obj    对象
     * @param errMsg 异常信息
     * @param args   errMsg占位符对应的参数列表
     */
    default void assertNotNullWithMsg(Object obj, Supplier<String> errMsg, Object... args) {
        if (obj == null) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 断言字符串不为空串
     *
     * @param str 字符串
     */
    default void assertNotEmpty(String str) {
        if (Strings.isNullOrEmpty(str)) {
            throw createException();
        }
    }

    /**
     * 断言字符串不为空串
     * <p>
     * 异常信息持传递参数方式
     *
     * @param str  字符串
     * @param args message占位符对应的参数列表
     */
    default void assertNotEmpty(String str, Object... args) {
        if (Strings.isNullOrEmpty(str)) {
            throw createException(args);
        }
    }

    /**
     * 断言字符串不为空串
     *
     * @param str    字符串
     * @param errMsg 异常信息
     */
    default void assertNotEmptyWithMsg(String str, String errMsg) {
        if (Strings.isNullOrEmpty(str)) {
            throw createException(errMsg);
        }
    }

    /**
     * 断言字符串不为空串
     * <p>
     * 异常信息支持传递参数方式，支持 {index} 形式的占位符
     *
     * @param str    字符串
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmptyWithMsg(String str, String errMsg, Object... args) {
        if (str == null || "".equals(str.trim())) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言数组大小不为0
     *
     * @param arrays 数组
     */
    default void assertNotEmpty(Object[] arrays) {
        if (arrays == null || arrays.length == 0) {
            throw createException();
        }
    }

    /**
     * 断言数组大小不为0
     * <p>
     * 异常信息支持传递参数方式
     *
     * @param arrays 数组
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmpty(Object[] arrays, Object... args) {
        if (arrays == null || arrays.length == 0) {
            throw createException(args);
        }
    }

    /**
     * 断言数组大小不为0
     *
     * @param arrays 数组
     * @param errMsg 异常信息
     */
    default void assertNotEmptyWithMsg(Object[] arrays, String errMsg) {
        if (arrays == null || arrays.length == 0) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言数组大小不为0
     *
     * @param arrays 数组
     * @param errMsg 异常信息. 支持 {index} 形式的占位符
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmptyWithMsg(Object[] arrays, String errMsg, Object... args) {
        if (arrays == null || arrays.length == 0) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言数组大小不为0
     *
     * @param arrays 数组
     * @param errMsg 异常信息
     */
    default void assertNotEmptyWithMsg(Object[] arrays, Supplier<String> errMsg) {
        if (arrays == null || arrays.length == 0) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言数组大小不为0
     * <p>
     * 异常信息支持传递参数方式
     *
     * @param arrays 数组
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmptyWithMsg(Object[] arrays, Supplier<String> errMsg, Object... args) {
        if (arrays == null || arrays.length == 0) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 断言集合大小不为0
     *
     * @param c 集合
     */
    default void assertNotEmpty(Collection<?> c) {
        if (c == null || c.isEmpty()) {
            throw createException();
        }
    }

    /**
     * 断言集合大小不为0
     *
     * @param c    集合
     * @param args message占位符对应的参数列表
     */
    default void assertNotEmpty(Collection<?> c, Object... args) {
        if (c == null || c.isEmpty()) {
            throw createException(args);
        }
    }

    /**
     * 断言集合大小不为0
     *
     * @param c      集合
     * @param errMsg 异常信息
     */
    default void assertNotEmptyWithMsg(Collection<?> c, String errMsg) {
        if (c == null || c.isEmpty()) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言集合大小不为0
     *
     * @param c      集合
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmptyWithMsg(Collection<?> c, String errMsg, Object... args) {
        if (c == null || c.isEmpty()) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言集合大小不为0
     *
     * @param c      集合
     * @param errMsg 异常信息
     */
    default void assertNotEmptyWithMsg(Collection<?> c, Supplier<String> errMsg) {
        if (c == null || c.isEmpty()) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言集合大小不为0
     *
     * @param c      集合
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmptyWithMsg(Collection<?> c, Supplier<String> errMsg, Object... args) {
        if (c == null || c.isEmpty()) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 断言Map大小不为0
     *
     * @param map Map
     */
    default void assertNotEmpty(Map<?, ?> map) {
        if (map == null || map.isEmpty()) {
            throw createException();
        }
    }

    /**
     * 断言Map大小不为0
     *
     * @param map  Map
     * @param args message占位符对应的参数列表
     */
    default void assertNotEmpty(Map<?, ?> map, Object... args) {
        if (map == null || map.isEmpty()) {
            throw createException(args);
        }
    }

    /**
     * 断言Map大小不为0
     *
     * @param map    Map
     * @param errMsg 异常信息
     */
    default void assertNotEmptyWithMsg(Map<?, ?> map, String errMsg) {
        if (map == null || map.isEmpty()) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言Map大小不为0
     *
     * @param map    Map
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmptyWithMsg(Map<?, ?> map, String errMsg, Object... args) {
        if (map == null || map.isEmpty()) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言Map大小不为0
     *
     * @param map    Map
     * @param errMsg 异常信息
     */
    default void assertNotEmptyWithMsg(Map<?, ?> map, Supplier<String> errMsg) {
        if (map == null || map.isEmpty()) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言Map大小不为0
     *
     * @param map    Map
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertNotEmptyWithMsg(Map<?, ?> map, Supplier<String> errMsg, Object... args) {
        if (map == null || map.isEmpty()) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 断言布尔值为 False
     *
     * @param expression 布尔变量
     */
    default void assertIsFalse(boolean expression) {
        if (expression) {
            throw createException();
        }
    }

    /**
     * 断言布尔值为 False
     *
     * @param expression 布尔变量
     * @param args       message占位符对应的参数列表
     */
    default void assertIsFalse(boolean expression, Object... args) {
        if (expression) {
            throw createException(args);
        }
    }

    /**
     * 断言布尔值为 False
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息
     */
    default void assertIsFalseWithMsg(boolean expression, String errMsg) {
        if (expression) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言布尔值为 False
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息
     * @param args       message占位符对应的参数列表
     */
    default void assertIsFalseWithMsg(boolean expression, String errMsg, Object... args) {
        if (expression) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言布尔值为 False
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息
     */
    default void assertIsFalseWithMsg(boolean expression, Supplier<String> errMsg) {
        if (expression) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言布尔值为 False
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息, 支持 {index} 形式的占位符
     * @param args       message占位符对应的参数列表
     */
    default void assertIsFalseWithMsg(boolean expression, Supplier<String> errMsg, Object... args) {
        if (expression) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 断言布尔值 True
     *
     * @param expression 布尔变量
     */
    default void assertIsTrue(boolean expression) {
        if (!expression) {
            throw createException();
        }
    }

    /**
     * 断言布尔值 True
     *
     * @param expression 布尔变量
     * @param args       message占位符对应的参数列表
     */
    default void assertIsTrue(boolean expression, Object... args) {
        if (!expression) {
            throw createException(args);
        }
    }

    /**
     * 断言布尔值 True
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息
     */
    default void assertIsTrueWithMsg(boolean expression, String errMsg) {
        if (!expression) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言布尔值 True
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息, 支持 {index} 形式的占位符
     * @param args       message占位符对应的参数列表
     */
    default void assertIsTrueWithMsg(boolean expression, String errMsg, Object... args) {
        if (!expression) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言布尔值 True
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息
     */
    default void assertIsTrueWithMsg(boolean expression, Supplier<String> errMsg) {
        if (!expression) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言布尔值 True
     *
     * @param expression 布尔变量
     * @param errMsg     异常信息, 支持 {index} 形式的占位符
     * @param args       message占位符对应的参数列表
     */
    default void assertIsTrueWithMsg(boolean expression, Supplier<String> errMsg, Object... args) {
        if (!expression) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 断言对象为 null
     *
     * @param obj 对象
     */
    default void assertIsNull(Object obj) {
        if (obj != null) {
            throw createException();
        }
    }

    /**
     * 断言对象为 null
     *
     * @param obj  布尔变量
     * @param args message占位符对应的参数列表
     */
    default void assertIsNull(Object obj, Object... args) {
        if (obj != null) {
            throw createException(args);
        }
    }

    /**
     * 断言对象为 null
     *
     * @param obj    对象
     * @param errMsg 异常信息
     */
    default void assertIsNullWithMsg(Object obj, String errMsg) {
        if (obj != null) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言对象为 null
     *
     * @param obj    布尔变量
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertIsNullWithMsg(Object obj, String errMsg, Object... args) {
        if (obj != null) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言对象为 null
     *
     * @param obj    对象
     * @param errMsg 异常信息
     */
    default void assertIsNullWithMsg(Object obj, Supplier<String> errMsg) {
        if (obj != null) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言对象为 null
     *
     * @param obj    布尔变量
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertIsNullWithMsg(Object obj, Supplier<String> errMsg, Object... args) {
        if (obj != null) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 断言对象相等
     *
     * @param o1 对象
     * @param o2 对象
     */
    default void assertEquals(Object o1, Object o2) {
        if (o1 == o2) {
            return;
        }
        if (o1 == null || !o1.equals(o2)) {
            throw createException();
        }
    }

    /**
     * 断言对象相等
     *
     * @param o1   对象
     * @param o2   对象
     * @param args message占位符对应的参数列表
     */
    default void assertEquals(Object o1, Object o2, Object... args) {
        if (o1 == o2) {
            return;
        }
        if (o1 == null || !o1.equals(o2)) {
            throw createException(args);
        }
    }

    /**
     * 断言对象相等
     *
     * @param o1     对象
     * @param o2     对象
     * @param errMsg 异常信息
     */
    default void assertEqualsWithMsg(Object o1, Object o2, String errMsg) {
        if (o1 == o2) {
            return;
        }
        if (o1 == null || !o1.equals(o2)) {
            throw createExceptionWithMsg(errMsg);
        }
    }

    /**
     * 断言对象相等
     *
     * @param o1     对象
     * @param o2     对象
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertEqualsWithMsg(Object o1, Object o2, String errMsg, Object... args) {
        if (o1 == o2) {
            return;
        }
        if (o1 == null || !o1.equals(o2)) {
            throw createExceptionWithMsg(errMsg, args);
        }
    }

    /**
     * 断言对象相等
     *
     * @param o1     对象
     * @param o2     对象
     * @param errMsg 异常信息
     */
    default void assertEqualsWithMsg(Object o1, Object o2, Supplier<String> errMsg) {
        if (o1 == o2) {
            return;
        }
        if (o1 == null || !o1.equals(o2)) {
            throw createExceptionWithMsg(errMsg.get());
        }
    }

    /**
     * 断言对象相等
     *
     * @param o1     对象
     * @param o2     对象
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertEqualsWithMsg(Object o1, Object o2, Supplier<String> errMsg, Object... args) {
        if (o1 == o2) {
            return;
        }
        if (o1 == null || !o1.equals(o2)) {
            throw createExceptionWithMsg(errMsg.get(), args);
        }
    }

    /**
     * 直接抛出异常
     */
    default void assertFail() {
        throw createException();
    }

    /**
     * 直接抛出异常
     *
     * @param args message占位符对应的参数列表
     */
    default void assertFail(Object... args) {
        throw createException(args);
    }

    /**
     * 直接抛出异常，并包含原异常信息
     * <p>
     * 当捕获非运行时异常时，并该异常进行业务描述时，必须传递原始异常，作为新异常的cause
     *
     * @param t 原始异常
     */
    default void assertFail(Throwable t) {
        throw createException(t);
    }

    /**
     * 直接抛出异常，并包含原异常信息
     * <p>
     * 当捕获非运行时异常时，并该异常进行业务描述时，必须传递原始异常，作为新异常的cause
     *
     * @param t    原始异常
     * @param args message占位符对应的参数列表
     */
    default void assertFail(Throwable t, Object... args) {
        throw createException(t, args);
    }

    /**
     * 直接抛出异常
     *
     * @param errMsg 异常信息
     */
    default void assertFailWithMsg(String errMsg) {
        throw createExceptionWithMsg(errMsg);
    }

    /**
     * 直接抛出异常
     *
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertFailWithMsg(String errMsg, Object... args) {
        throw createExceptionWithMsg(errMsg, args);
    }

    /**
     * 直接抛出异常，并包含原异常信息
     * <p>
     * 当捕获非运行时异常时，并该异常进行业务描述时，必须传递原始异常，作为新异常的cause
     *
     * @param errMsg 异常信息
     * @param t      原始异常
     */
    default void assertFailWithMsg(String errMsg, Throwable t) {
        throw createExceptionWithMsg(errMsg, t);
    }

    /**
     * 直接抛出异常，并包含原异常信息
     * <p>
     * 当捕获非运行时异常时，并该异常进行业务描述时，必须传递原始异常，作为新异常的cause
     *
     * @param errMsg 异常信息
     * @param t      原始异常
     * @param args   message占位符对应的参数列表
     */
    default void assertFailWithMsg(String errMsg, Throwable t, Object... args) {
        throw createExceptionWithMsg(errMsg, t, args);
    }

    /**
     * 直接抛出异常
     *
     * @param errMsg 异常信息
     */
    default void assertFailWithMsg(Supplier<String> errMsg) {
        throw createExceptionWithMsg(errMsg.get());
    }

    /**
     * 直接抛出异常
     *
     * @param errMsg 异常信息
     * @param args   message占位符对应的参数列表
     */
    default void assertFailWithMsg(Supplier<String> errMsg, Object... args) {
        throw createExceptionWithMsg(errMsg.get(), args);
    }

    /**
     * 直接抛出异常，并包含原异常信息
     * <p>
     * 当捕获非运行时异常时，并该异常进行业务描述时，必须传递原始异常，作为新异常的cause
     *
     * @param errMsg 异常信息
     * @param t      原始异常
     */
    default void assertFailWithMsg(Supplier<String> errMsg, Throwable t) {
        throw createExceptionWithMsg(errMsg.get(), t);
    }

    /**
     * 直接抛出异常，并包含原异常信息
     * <p>
     * 当捕获非运行时异常时，并该异常进行业务描述时，必须传递原始异常，作为新异常的cause
     *
     * @param errMsg 异常信息
     * @param t      原始异常
     * @param args   message占位符对应的参数列表
     */
    default void assertFailWithMsg(Supplier<String> errMsg, Throwable t, Object... args) {
        throw createExceptionWithMsg(errMsg.get(), t, args);
    }

    /**
     * 断言日期跨度不能超过一个月
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    default void assertNoMoreThanOneMonth(String startDate, String endDate) {
        if (calMonth(startDate, endDate) > 0) {
            throw createException();
        }
    }
}
