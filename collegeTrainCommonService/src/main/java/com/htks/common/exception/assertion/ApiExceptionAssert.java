package com.htks.common.exception.assertion;

import static org.apache.commons.lang.ArrayUtils.isNotEmpty;

import com.htks.common.exception.ApiCommonException;
import com.htks.common.exception.BaseRuntimeException;
import com.htks.common.exception.enums.IResponseEnum;
import java.text.MessageFormat;

/**
 * API模块异常断言
 *
 * <AUTHOR>
 * @date 2022/04/22.
 */
public interface ApiExceptionAssert extends IResponseEnum, CustomAssert {

  /**
   * 创建异常
   *
   * @param args 参数
   * @return 返回
   */
  @Override
  default BaseRuntimeException createException(Object... args) {
    String msg = this.getMessage();
    if (isNotEmpty(args)) {
      msg = MessageFormat.format(this.getMessage(), args);
    }

    return new ApiCommonException(msg, this, args);
  }

  /**
   * 创建异常
   *
   * @param t    异常
   * @param args 参数
   * @return 返回
   */
  @Override
  default BaseRuntimeException createException(Throwable t, Object... args) {
    String msg = this.getMessage();
    if (isNotEmpty(args)) {
      msg = MessageFormat.format(this.getMessage(), args);
    }

    return new ApiCommonException(msg, this, args, t);
  }
}
