package com.htks.common.exception.base;



import com.htks.common.exception.CustomerException;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: IAssert
 */
public interface IAssert {
	
	
	/**
	 * @return
	 */
    CustomerException newException();

    /**
     * 创建异常
     * @param args
     * @return
     */
    CustomerException newException(Object... args);


    /**
     * 抛出异常
     */
	default void throwException() {
	    throw newException();
    }

    /**
     * 抛出带参数异常
     * @param args
     */
    default void throwException(Object... args) {
        throw newException(args);
    }

    /**
     * <p>断言对象<code>obj</code>非空。如果对象<code>obj</code>为空，则抛出异常
     *
     * @param obj 待判断对象
     */
    default void assertNotNull(Object obj) {
        if (obj == null) {
            throw newException(obj);
        }
    }

    /**
     * <p>断言对象<code>obj</code>非空。如果对象<code>obj</code>为空，则抛出异常
     * <p>异常信息<code>message</code>支持传递参数方式，避免在判断之前进行字符串拼接操作
     *
     * @param obj 待判断对象
     * @param args message占位符对应的参数列表
     */
    default void assertNotNull(Object obj, Object... args) {
        if (obj == null) {
            throw newException(args);
        }
    }

    default void assertNull(Object obj, Object... args) {
        if (obj != null) {
            throw newException(args);
        }
    }

    /**
     * <p>断言对象<code>obj</code>非空。如果对象<code>obj</code>为空，则抛出异常
     *
     * @param obj 待判断对象
     */
    default void assertNotEmpty(Object obj) {
        if (obj == null) {
            throw newException(obj);
        }
        if (obj.getClass().isArray() && Array.getLength(obj) == 0) {
            throw newException(obj);
        }
        if (obj instanceof CharSequence && ((CharSequence) obj).length() == 0) {
            throw newException(obj);
        }
        if (obj instanceof Collection && ((Collection) obj).isEmpty()) {
            throw newException(obj);
        }
        if (obj instanceof Map && ((Map) obj).isEmpty()) {
            throw newException(obj);
        }
        if (obj instanceof String && "".equals(((String) obj).trim())) {
            throw newException(obj);
        }
    }

    /**
     * <p>断言对象<code>obj</code>非空。如果对象<code>obj</code>为空，则抛出异常
     *
     * @param obj 待判断对象
     */
    default void assertNotEmpty(Object obj, Object... args) {
        if (obj == null) {
            throw newException(args);
        }
        if (obj.getClass().isArray() && Array.getLength(obj) == 0) {
            throw newException(args);
        }
        if (obj instanceof CharSequence && ((CharSequence) obj).length() == 0) {
            throw newException(args);
        }
        if (obj instanceof Collection && ((Collection) obj).isEmpty()) {
            throw newException(args);
        }
        if (obj instanceof Map && ((Map) obj).isEmpty()) {
            throw newException(args);
        }
        if (obj instanceof String && "".equals(((String) obj).trim())) {
            throw newException(args);
        }
    }
    
    /**
     * @param condition
     */
    default void isTrue(boolean condition, Object... args) {
        if (condition) {
           throw newException(args);
        }
    }

    /**
     * @param condition
     */
    default void isFalse(boolean condition, Object... args) {
        if (!condition) {
        	throw newException(args);
        }
    }
    
    /**
     * @param condition
     */
    default void isTrue(boolean condition) {
        if (condition) {
           throw newException();
        }
    }

    /**
     * @param condition
     */
    default void isFalse(boolean condition) {
        if (!condition) {
        	throw newException();
        }
    }
}