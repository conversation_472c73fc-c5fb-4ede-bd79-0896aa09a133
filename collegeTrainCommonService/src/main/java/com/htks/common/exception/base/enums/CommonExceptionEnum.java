package com.htks.common.exception.base.enums;


import com.htks.common.exception.base.ICustomExceptionExceptionAssert;
import com.htks.common.exception.base.MessageConsts;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 通用异常枚举类
 */

@AllArgsConstructor
public enum CommonExceptionEnum implements ICustomExceptionExceptionAssert {

	 NOT_NULL("1001", MessageConsts.ENTITY_EMPTY),
	 IS_NULL("105", MessageConsts.ENTITY_EMPTY),
	 NOT_NULL_ARG("101", MessageConsts.INFO_ARG_ENTITY_EMPTY),
	 ERROR_SNOWFLAKE_SIZE("102", MessageConsts.ERROR_SNOWFLAKE_SIZE),
	 OPEARTION_FAILED("103", MessageConsts.OPERATION_FAILED),
	 SYSTEM_ERROR("0", MessageConsts.SYSTEM_ERROR);

	
	 /**
     * 返回码
     */
    private String code;
    /**
     * 返回消息
     */
    private String message;
	@Override
	public String getCode() {
		return code;
	}
	@Override
	public String getMessage() {
		return message;
	}
	
}
