package com.htks.common.exception;

import static com.htks.common.SystemConfig.ResponseStatusEnum.METHOD_ARGUMENT_NOT_VALID_FAILURE;

import cn.hutool.core.util.ObjectUtil;
import com.htks.common.external.wx.ResultVO;
import com.htks.web.Rest;
import com.htks.web.RestBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;


/**
 * 异常处理器
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Slf4j
@RestControllerAdvice
public final class CommonExceptionHandler {

  /**
   * Valid
   * @param e
   * @return
   */
  @ExceptionHandler(ConstraintViolationException.class)
  public Rest ConstraintViolationExceptionHandler(ConstraintViolationException e) {
    String message = e.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining());
    return RestBody.failure(HttpStatus.SC_INTERNAL_SERVER_ERROR,message);
  }

  /**
   * 业务异常
   */
  @ExceptionHandler(CustomerException.class)
  public ResultVO businessException(CustomerException e) {
    if (ObjectUtil.isNotEmpty(e.getCode())) {
      return ResultVO.error(e.getCode(), e.getMessage());
    }
    return ResultVO.error(e.getMessage());
  }

  /**
   * 默认异常处理
   *
   * @param e 异常
   * @return returnInfo
   */
  @ExceptionHandler(Exception.class)
  public Rest handleException(Exception e) {
    if (log.isDebugEnabled()) {
      log.debug(e.getMessage(), e);
    }
    return RestBody.failure(HttpStatus.SC_INTERNAL_SERVER_ERROR, "未知异常，请联系管理员");
  }

  /**
   * 基本异常处理
   *
   * @param e 异常
   * @return returnInfo
   */
  @ExceptionHandler(BaseException.class)
  public Rest handleBaseException(BaseException e) {
    if (log.isDebugEnabled()) {
      log.debug(e.getMessage(), e);
    }
    return RestBody.failure(HttpStatus.SC_INTERNAL_SERVER_ERROR, e.getMessage());
  }

  /**
   * 基本Runtime异常处理
   *
   * @param e 异常
   * @return returnInfo
   */
  @ExceptionHandler(BaseRuntimeException.class)
  public Rest handleBaseRuntimeException(BaseRuntimeException e) {
    if (log.isDebugEnabled()) {
      log.debug(e.getMessage(), e);
    }
    return RestBody.failure(HttpStatus.SC_INTERNAL_SERVER_ERROR, e.getMessage());
  }

  /**
   * 校验异常处理
   *
   * @param e 异常
   * @return returnInfo
   */
  @ExceptionHandler(MethodArgumentNotValidException.class)
  public Rest handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {

    final BindingResult bindingResult = e.getBindingResult();
    final StringBuilder errMsg = new StringBuilder(bindingResult.getFieldErrors().size() * 16);
    errMsg.append("Invalid request:");
    for (int i = 0; i < bindingResult.getFieldErrors().size(); i++) {
      if (i > 0) {
        errMsg.append(",");
      }
      FieldError error = bindingResult.getFieldErrors().get(i);
      errMsg.append(error.getField()).append(":").append(error.getDefaultMessage());
    }

    if (log.isDebugEnabled()) {
      log.debug(errMsg.toString(), e);
    }
    return RestBody.failure(Integer.parseInt(METHOD_ARGUMENT_NOT_VALID_FAILURE.getStatusCode()),
        errMsg.toString());
  }

  /**
   * API模块异常处理
   *
   * @param e 异常
   * @return returnInfo
   */
  @ExceptionHandler(ApiCommonException.class)
  public Rest handleApiCommonException(ApiCommonException e) {
    if (log.isDebugEnabled()) {
      log.debug(e.getMessage(), e);
      //log.debug("API异常 Message:{}", e.getMessage());
      //log.debug("API异常 Code:{}", e.getCode());
      //log.debug("API异常 ResponseEnum-Code:{}", e.getResponseEnum().getCode());
      //log.debug("API异常 ResponseEnum-Message:{}", e.getResponseEnum().getMessage());
    }
    return RestBody.failure(e.getCode(), e.getMessage());
  }


}
