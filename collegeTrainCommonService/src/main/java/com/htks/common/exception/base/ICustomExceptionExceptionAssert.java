package com.htks.common.exception.base;


import com.htks.common.exception.CustomerException;

/**
 * @Description: 主要作用：重写Assert类的方法，格式化异常信息，抛出异常。
 * <AUTHOR>
 */
public interface ICustomExceptionExceptionAssert extends IResponseEnum, IAssert {

    @Override
    default CustomerException newException() {
        return new CustomerException(this);
    }

    @Override
    default CustomerException newException(Object... args) {
        String msg = (String) args[0];
        Integer code = null;
        if (args.length >= 2) {
            code = (Integer) args[1];
        }
        return new CustomerException(this,msg,code);
    }


}
