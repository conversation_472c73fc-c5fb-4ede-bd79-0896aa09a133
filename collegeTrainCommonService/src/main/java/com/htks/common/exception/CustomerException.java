package com.htks.common.exception;

import cn.hutool.core.util.ObjectUtil;
import com.htks.common.exception.base.IResponseEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/12 10:28
 */
public class CustomerException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public CustomerException(String message) {
        this.message = message;
    }

    public CustomerException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public CustomerException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public CustomerException(IResponseEnum responseEnum) {
        this(responseEnum.getMessage(),Integer.parseInt(responseEnum.getCode()));
    }

    public CustomerException(IResponseEnum responseEnum,String message) {
        this(ObjectUtil.isNotEmpty(message) ? message : responseEnum.getMessage(), Integer.parseInt(responseEnum.getCode()));
    }

    public CustomerException(IResponseEnum responseEnum,String message,Integer code) {
        this(ObjectUtil.isNotEmpty(message) ? message : responseEnum.getMessage(), ObjectUtil.isNotEmpty(code)? code:Integer.parseInt(responseEnum.getCode()));
    }
}
