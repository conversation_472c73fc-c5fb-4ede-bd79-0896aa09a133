package com.htks.common.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * Sys模块异常类
 *
 * <AUTHOR>
 * @date 2020/09/07
 */
@Getter
@Setter
public class SysCommonException extends BaseException {

  private static final long serialVersionUID = 1221135541147951454L;

  /**
   * 异常信息
   */
  private String msg;

  /**
   * 异常编码
   */
  private String code;

  public SysCommonException(String msg) {
    super(msg);
    this.msg = msg;
  }

  public SysCommonException(String msg, Throwable e) {
    super(msg, e);
    this.msg = msg;
  }

  public SysCommonException(String msg, String code) {
    super(msg);
    this.msg = msg;
    this.code = code;
  }

  public SysCommonException(String msg, String code, Throwable e) {
    super(msg, e);
    this.msg = msg;
    this.code = code;
  }

}
