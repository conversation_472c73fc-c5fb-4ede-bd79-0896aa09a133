package com.htks.common;

import java.math.BigDecimal;
import lombok.Getter;
import org.springframework.stereotype.Component;


/**
 * Shiro工具类
 *
 * <AUTHOR>
 * @date 2018/09/18
 */
@Getter
@Component
public final class SystemConfig {

  /**
   * 自定义logback的marker
   */
  public static final String LOGBACK_MARKER_BOURNE = "MARKER_BOURNE";

  /**
   * TOKEN过期时间，单位为秒
   */
  public static final Integer TOKEN_EXPIRE = 60 * 60 * 1;

  /**
   * 未登录标识
   */
  public static final String NO_LOGIN = "NO_LOGIN";

  /**
   * 限流--每秒处理数(每秒生成令牌数)
   */
  public static final double DEFAULT_PERMIT_PER_SECOND = 100d;

  /**
   * 限流--超时时间，单位毫秒
   */
  public static final long DEFAULT_TRY_ACQUIRE_TIMEOUT = 10000L;

  /**
   * SHIRO_SESSION_KEY
   */
  public static final String SHIRO_SESSION_KEY = "SHIRO_SESSION_KEY";

  /**
   * 默认字符集
   */
  public static final String DEFAULT_CHARSET = "UTF-8";
  public static final String CHARSET_GBK = "GBK";

  /**
   * 默认编码方式
   */
  public static final String DEFAULT_ENCODING = "text/html;charset=UTF-8";

  /**
   * 默认Bean验证错误
   */
  public static final String BEAN_VALIDATOR_ERROR = "验证Bean:{}, 错误信息:{}";

  /**
   * 符号
   */
  public static final String DEFAULT_COMMA = ".";
  public static final String ENGLISH_COMMA = ",";
  public static final String ENGLISH_COLON = ":";
  public static final String CHINESE_COMMA = "，";
  public static final String FORWARD_SLASH = "/";
  public static final String STRING_EMPTY = "";
  public static final String STRING_EMPTY_SPACE = " ";
  public static final String STRING_LF_CR = "\r\n";

  public static final BigDecimal BIG_DECIMAL_ZERO_TWO = BigDecimal.valueOf(0.00);
  public static final BigDecimal BIG_DECIMAL_ZERO = BigDecimal.valueOf(0.0000);
  public static final BigDecimal BIG_DECIMAL_TEN_THOUSAND = BigDecimal.valueOf(10000);

  /**
   * 时长-一天
   */
  public static final Long TIME_ONE_DAY = 60 * 60 * 24L;

  /**
   * 时长-一小时
   */
  public static final Long TIME_ONE_HOUR = 60 * 60L;

  /**
   * 时长-30分钟
   */
  public static final Long TIME_THIRTY_MINUTES = 30 * 60L;

  /**
   * 中文时间单位
   */
  public static final String STRING_DAY = "天";
  public static final String STRING_HOUR = "小时";
  public static final String STRING_MINUTE = "分钟";
  public static final String STRING_SECOND = "秒";

  /**
   * 常用日期格式
   */
  public static final String DATE_FORMAT_DEFAULT = "yyyy-MM-dd HH:mm:ss";
  public static final String DATE_FORMAT_YYYY_MM_DD_HH = "yyyy-MM-dd HH";
  public static final String DATE_FORMAT_YYYY_MM = "yyyy-MM";
  public static final String DATE_FORMAT_M = "M月";
  public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
  public static final String DATE_FORMAT_SHORT_DATE = "yyyy-MM-dd";
  public static final String DATE_FORMAT_SLASH_DATE = "yyyy/MM/dd";
  public static final String DATE_FORMAT_CHINA_DATE = "yyyy年MM月dd日";
  public static final String DATE_FORMAT_YYYYMM = "yyyyMM";
  public static final String DATE_FORMAT_YYYYMMDDHH = "yyyyMMddHH";
  public static final String DATE_FORMAT_YYYYMMDDHHMI = "yyyyMMddHHmm";
  public static final String DATE_FORMAT_YYYYMMDDHHMISS = "yyyyMMddHHmmss";

  //企业微信
  public static final String APP_SEND_AGENT_PREFIX = "wx";
  //企业微信
  public static final String APP_AGENT_PREFIX = "wx";

  public static final String ENTER = "\n";

  public static final String END = "-- END --";


  /**
   * 文件大小的一半（0.5M）
   */
  public static final int HALF_FILE_SIZE = 1024 * 512;

  /**
   * 状态标识
   */
  public static final String SUCCESS = "SUCCESS";
  public static final String ERROR = "ERROR";
  public static final String WARN = "WARN";

  /**
   * 私有构造器
   */
  private SystemConfig() {
  }

  /**
   * 接口响应状态枚举类
   */
  @Getter
  public enum ResponseStatusEnum {
    //错误编码以 90XX 格式。序号顺序增加
    HANDLE_SUCCESS("200", "操作成功"),
    HANDLE_FAILURE("9000", "操作失败"),
    INVALID_KAPTCHA("9001", "验证码已失效"),
    CURRENT_LIMIT("9002", "系统繁忙，请稍后重试"),
    LOGIN_FAILURE("9003", "登录失败"),
    VEHICLEWARNING_SAVE_FAILURE("9004", "新增失败"),
    VEHICLEWARNING_UPDATE_FAILURE("9005", "更新失败"),
    UPLOAD_FAILURE("9006", "上传文件出错"),
    STATUS_EXCEPTION("9007", "您操作的数据状态不满足操作的条件"),
    TIME_EXCEPTION("9008", "时间格式转换异常"),
    METHOD_ARGUMENT_NOT_VALID_FAILURE("9009", "实体校验出错"),
    SESSION_TIME_OUT("9010", "会话超时，请重新登陆"),
    NO_FOUND("9011", "暂无查询结果"),
    NO_DATA("9012", "暂无数据"),
    NO_RIGHT("9013", "暂无权限"),
    DEFAULT("500", "未知异常,请联系管理员");

    private String statusCode;

    private String statusValue;

    ResponseStatusEnum(String code, String value) {
      statusCode = code;
      statusValue = value;
    }

    public String getStatusCode() {
      return statusCode;
    }

    public void setStatusCode(String statusCode) {
      this.statusCode = statusCode;
    }

    public String getStatusValue() {
      return statusValue;
    }

    public void setStatusValue(String statusValue) {
      this.statusValue = statusValue;
    }

    public static ResponseStatusEnum fromStatusCode(String statusCode) {
      for (ResponseStatusEnum tmp : ResponseStatusEnum.values()) {
        if (tmp.getStatusCode().equals(statusCode)) {
          return tmp;
        }
      }
      return DEFAULT;
    }
  }

  /**
   * 固定技能id枚举类
   */
  @Getter
  public enum SkillEnum {

    SKILL_TUTOR("C_TutorSatisfy", "导师讲课能力"),
    LECTURER("C_LecturerSatisfy", "讲师讲课能力"),
    UN_KNOW("-1", "未知");

    private final String skillCode;

    private final String skillName;

    SkillEnum(String skillCode, String skillName) {
      this.skillCode = skillCode;
      this.skillName = skillName;
    }
  }
}
