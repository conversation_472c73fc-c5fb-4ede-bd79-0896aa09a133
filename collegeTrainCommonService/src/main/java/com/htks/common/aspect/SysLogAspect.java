package com.htks.common.aspect;

import com.alibaba.fastjson.JSON;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.annotation.SysLog;
import com.htks.common.utils.IPUtils;
import com.htks.common.utils.ShiroUtils;
import com.htks.domain.sys.dto.SysLogEntity;
import com.htks.domain.sys.service.SysLogService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.left;

/**
 * 系统日志，切面处理类
 *
 * <AUTHOR>
 * @date 2020/09/04
 */
@Slf4j
@Aspect
@Component
public class SysLogAspect {

  private static final Integer MAX_LEN = 4096;

  private final SysLogService sysLogService;

  /**
   * 系统标识
   */
  @Value("${systemIdentifier}")
  private String systemIdentifier;

  @Autowired
  public SysLogAspect(SysLogService sysLogService) {
    this.sysLogService = sysLogService;
  }

  private static HttpServletRequest getHttpServletRequest() {
    return ((ServletRequestAttributes) Objects
        .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
  }

  /**
   * 定义一个切点，拦截带有SysLog注解的方法
   */
  @Pointcut("@annotation( com.htks.common.annotation.SysLog)")
  public void logPointCut() {
    //do nothing
  }

  /**
   * 定义一个切点，拦截web包及所有子包里的任意Controller类的public方法
   */
  @Pointcut("execution(public * com.htks.web..*Controller.*(..))")
  public void defineLogAspect() {
    //do nothing
  }

  //@Around(value = "logPointCut()")
  @Around(value = "defineLogAspect()")
  public Object around(ProceedingJoinPoint point) throws Throwable {
    final long beginTime = System.currentTimeMillis();
    //执行方法
    final Object result = point.proceed();
    //执行时长(毫秒)
    final Long time = System.currentTimeMillis() - beginTime;

    //保存日志
    saveSysLog(point, time);

    return result;
  }

  private void saveSysLog(ProceedingJoinPoint joinPoint, Long time) {
    final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    final Method method = signature.getMethod();

    final IgnoreSysLog ignoreSysLog = method.getDeclaredAnnotation(IgnoreSysLog.class);
    if (null != ignoreSysLog) {
      //如果有@IgnoreSysLog注解，则不记录日志
      if (log.isDebugEnabled()) {
        log.debug("方法:{}忽略日志...", method.getName());
      }
      return;
    }

    final SysLogEntity sysLog = new SysLogEntity();
    final SysLog syslog = method.getAnnotation(SysLog.class);
    final ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
    if (syslog != null) {
      //SysLog注解上的描述
      sysLog.setOperation(syslog.value());
    } else if (apiOperation != null) {
      //ApiOperation注解上的描述
      sysLog.setOperation(apiOperation.value());
    } else {
      sysLog.setOperation("未知操作");
    }

    //请求的方法名
    final String className = joinPoint.getTarget().getClass().getName();
    final String methodName = signature.getName();
    sysLog.setMethodName(className + "." + methodName + "()");

    //请求的参数
    final Object[] args = joinPoint.getArgs();
    String params="未知参数";
    try {
      params = JSON.toJSONString(args);
    } catch (Exception e) {
      log.error("Exception:", e);
    }
    sysLog.setParams(left(params, MAX_LEN));

    //获取request
    final HttpServletRequest request = getHttpServletRequest();
    //设置IP地址
    sysLog.setIp(IPUtils.getIpAddr(request));
    //员工工号
    final String employeeNumber = ShiroUtils.getEmployeeNumber();

    sysLog.setEmployeeName(employeeNumber);
    sysLog.setCreater("admin");
    sysLog.setUpdater("admin");

    //耗时
    sysLog.setOperationTime(time);

    //系统标识
    sysLog.setSystemIdentifier(systemIdentifier);

    //保存系统日志
    sysLogService.saveSysLog(sysLog);
  }
}
