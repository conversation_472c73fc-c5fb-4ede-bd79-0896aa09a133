package com.htks.common.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.Idempotent;
import com.htks.common.exception.CustomerException;
import com.htks.common.utils.IdempotentUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 幂等性切面
 * 拦截带有@Idempotent注解的方法，进行幂等性控制
 *
 * <AUTHOR> Generated
 * @date 2025/07/09
 */
@Slf4j
@Aspect
@Component
@Order(1) // 确保在事务切面之前执行
public class IdempotentAspect {

    private final IdempotentUtils idempotentUtils;
    private final ExpressionParser parser = new SpelExpressionParser();

    @Autowired
    public IdempotentAspect(IdempotentUtils idempotentUtils) {
        this.idempotentUtils = idempotentUtils;
    }

    @Around("@annotation(idempotent)")
    public Object around(ProceedingJoinPoint joinPoint, Idempotent idempotent) throws Throwable {
        // 获取业务标识
        String businessKey = getBusinessKey(joinPoint, idempotent);
        
        // 获取幂等性token
        String token = getIdempotentToken(joinPoint, idempotent);
        
        // 验证并消费token
        if (!idempotentUtils.validateAndConsumeToken(businessKey, token)) {
            log.warn("幂等性验证失败，businessKey: {}, token: {}", businessKey, token);
            throw new CustomerException(idempotent.message());
        }
        
        try {
            // 执行原方法
            return joinPoint.proceed();
        } catch (Exception e) {
            // 如果业务执行失败，可以考虑是否要恢复token（根据业务需求决定）
            log.error("业务执行异常，businessKey: {}, token: {}", businessKey, token, e);
            throw e;
        }
    }

    /**
     * 获取业务标识
     */
    private String getBusinessKey(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        String businessKey = idempotent.businessKey();
        
        if (!StringUtils.hasText(businessKey)) {
            // 如果没有指定业务标识，使用方法签名作为默认值
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            businessKey = signature.getDeclaringTypeName() + "." + signature.getName();
        } else if (businessKey.contains("#")) {
            // 支持SpEL表达式
            businessKey = parseSpEL(businessKey, joinPoint);
        }
        
        return businessKey;
    }

    /**
     * 获取幂等性token
     */
    private String getIdempotentToken(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        String token = null;
        
        switch (idempotent.tokenSource()) {
            case HEADER:
                token = getTokenFromHeader(idempotent.tokenName());
                break;
            case PARAMETER:
                token = getTokenFromParameter(joinPoint, idempotent.tokenName());
                break;
            case BODY:
                token = getTokenFromBody(joinPoint, idempotent.tokenName());
                break;
        }
        
        if (!StringUtils.hasText(token)) {
            throw new CustomerException("幂等性token不能为空");
        }
        
        return token;
    }

    /**
     * 从请求头获取token
     */
    private String getTokenFromHeader(String tokenName) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getHeader(tokenName);
        }
        return null;
    }

    /**
     * 从请求参数获取token
     */
    private String getTokenFromParameter(ProceedingJoinPoint joinPoint, String tokenName) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getParameter(tokenName);
        }
        return null;
    }

    /**
     * 从请求体获取token
     */
    private String getTokenFromBody(ProceedingJoinPoint joinPoint, String tokenName) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg != null) {
                    try {
                        // 尝试从JSON对象中获取token
                        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(arg));
                        if (jsonObject.containsKey(tokenName)) {
                            return jsonObject.getString(tokenName);
                        }
                    } catch (Exception e) {
                        // 忽略解析异常，继续尝试其他参数
                    }
                }
            }
        }
        return null;
    }

    /**
     * 解析SpEL表达式
     */
    private String parseSpEL(String spel, ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Parameter[] parameters = method.getParameters();
            Object[] args = joinPoint.getArgs();
            
            EvaluationContext context = new StandardEvaluationContext();
            
            // 设置方法参数到SpEL上下文
            for (int i = 0; i < parameters.length; i++) {
                context.setVariable(parameters[i].getName(), args[i]);
            }
            
            Expression expression = parser.parseExpression(spel);
            Object value = expression.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}", spel, e);
            return spel;
        }
    }
}
