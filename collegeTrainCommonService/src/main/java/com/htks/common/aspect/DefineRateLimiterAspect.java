package com.htks.common.aspect;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.htks.common.SystemConfig.DATE_FORMAT_DEFAULT;
import static com.htks.common.SystemConfig.DEFAULT_PERMIT_PER_SECOND;
import static com.htks.common.SystemConfig.DEFAULT_TRY_ACQUIRE_TIMEOUT;
import static com.htks.common.SystemConfig.ERROR;
import static com.htks.common.SystemConfig.ResponseStatusEnum.CURRENT_LIMIT;
import static java.lang.Integer.parseInt;
import static java.util.Objects.requireNonNull;

import com.google.common.util.concurrent.RateLimiter;
import com.htks.common.annotation.DefineRateLimiter;
import com.htks.common.annotation.IgnoreRateLimiter;
import com.htks.web.RestBody;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 自定义限流，切面处理类
 *
 * <AUTHOR>
 * @date 2020/09/28.
 */
@Slf4j
@Aspect
@Component
public class DefineRateLimiterAspect {

  /**
   * 保存接口路径和限流器的对应关系
   */
  private final ConcurrentHashMap<String, RateLimiter> rateLimiters = new ConcurrentHashMap<>();

  /**
   * 定义一个切点，拦截web包及所有子包里的任意Controller类的public方法
   */
  @Pointcut("execution(public * com.htks.web..*Controller.*(..))")
  public void defineRateLimiterAspect() {
    //do nothing
  }

  /**
   * 使用环绕通知拦截所有Controller请求
   * <p>
   * 使用自定义限流注解
   */
  //@Around("defineRateLimiterAspect()")
  public Object doBefore(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
    final MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
    final Method method = signature.getMethod();
    if (null == method) {
      return null;
    }

    final DefineRateLimiter defineRateLimiter = method
        .getDeclaredAnnotation(DefineRateLimiter.class);
    if (null == defineRateLimiter) {
      return proceedingJoinPoint.proceed();
    }

    //处理限流令牌
    final Boolean result = handlerDefineRateLimiter(defineRateLimiter.permitsPerSecond(),
        defineRateLimiter.timeout());
    if (Boolean.TRUE.equals(result)) {
      return null;
    }

    return proceedingJoinPoint.proceed();
  }

  /**
   * 使用环绕通知拦截所有Controller请求
   * <p>
   * 使用忽略限流注解
   */
  @Around("defineRateLimiterAspect()")
  public Object doSmartBefore(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
    final MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
    final Method method = signature.getMethod();
    if (null == method) {
      return null;
    }

    final IgnoreRateLimiter ignoreRateLimiter = method
        .getDeclaredAnnotation(IgnoreRateLimiter.class);
    if (null != ignoreRateLimiter) {
      //如果有@IgnoreRateLimiter注解，则不限流
      if (log.isDebugEnabled()) {
        log.debug("方法:{}忽略限流...", method.getName());
      }
      return proceedingJoinPoint.proceed();
    }

    //处理限流令牌
    final Boolean result = handlerDefineRateLimiter(DEFAULT_PERMIT_PER_SECOND,
        DEFAULT_TRY_ACQUIRE_TIMEOUT);
    if (Boolean.TRUE.equals(result)) {
      return null;
    }

    return proceedingJoinPoint.proceed();
  }

  /**
   * 处理限流
   *
   * @param permitsPerSecond 每秒处理数(每秒生成令牌数)
   * @param timeout          超时时间，单位毫秒
   * @return 是否限流 : True限流, False不限流
   */
  private Boolean handlerDefineRateLimiter(final Double permitsPerSecond, final Long timeout) {
    final ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
        .getRequestAttributes();
    final String requestUri = requireNonNull(requestAttributes).getRequest().getRequestURI();
    RateLimiter rateLimiter = rateLimiters.get(requestUri);
    if (null == rateLimiter) {
      //配置每秒令牌数
      rateLimiter = RateLimiter.create(permitsPerSecond);
      //保存URI和限流器的对应关系
      final RateLimiter rateLimiterPrevious = rateLimiters.putIfAbsent(requestUri, rateLimiter);
      if (null != rateLimiterPrevious) {
        rateLimiter = rateLimiterPrevious;
      }
    }
    //获取令牌，成功获取返回true，没有获取到令牌返回false，如果超时则立即返回false，不会阻塞
    final Boolean tryAcquire = rateLimiter.tryAcquire(timeout, TimeUnit.MILLISECONDS);

    if (Boolean.FALSE.equals(tryAcquire)) {
      final String now = LocalDateTime.now()
          .format(DateTimeFormatter.ofPattern(DATE_FORMAT_DEFAULT));
      log.error("请求URI:{}无法获得令牌, 于{}触发限流...", requestUri, now);
      doFallback(requestAttributes);
      return Boolean.TRUE;
    }

    if (log.isDebugEnabled()) {
      log.debug("请求URI:{}成功获得令牌...", requestUri);
    }

    return Boolean.FALSE;
  }

  /**
   * 失败处理
   *
   * @param requestAttributes 请求
   */
  private void doFallback(final ServletRequestAttributes requestAttributes) {
    final HttpServletResponse response = requireNonNull(requestAttributes).getResponse();
    requireNonNull(response).setContentType("application/json;charset=utf-8");
    try (PrintWriter writer = response.getWriter()) {
      writer.print(toJSONString(RestBody.build(
          parseInt(CURRENT_LIMIT.getStatusCode()),
          ERROR,
          null,
          CURRENT_LIMIT.getStatusValue(),
          "")));
    } catch (IOException e) {
      log.error("IO异常:", e);
    }
  }
}
