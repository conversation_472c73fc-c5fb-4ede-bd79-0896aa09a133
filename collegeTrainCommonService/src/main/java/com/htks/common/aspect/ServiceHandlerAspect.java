package com.htks.common.aspect;

import static com.htks.common.SystemConfig.DATE_FORMAT_DEFAULT;
import static org.joda.time.DateTime.now;

import com.htks.common.annotation.ServiceAfterReturn;
import com.htks.common.annotation.ServiceExceptionHandler;
import com.htks.common.utils.MailUtils;
import java.lang.reflect.Method;
import java.util.Arrays;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.stereotype.Component;

/**
 * Service层异常处理，切面处理类
 *
 * <AUTHOR>
 * @date 2022/05/25.
 */
@Slf4j
@Aspect
@Component
public final class ServiceHandlerAspect {

  @Resource
  private MailUtils mailUtils;

  private static final String TO_MAILS = "<EMAIL>";

  private static final String CC_MAILS = "";

  /**
   * 定义一个切点，拦截带有ServiceExceptionHandler注解的方法
   */
  @Pointcut("@annotation(com.htks.common.annotation.ServiceExceptionHandler)  || @within(com.htks.common.annotation.ServiceExceptionHandler)")
  public void pointCut() {
    //do nothing
  }

  /**
   * 定义一个切点，拦截带有ServiceAfterReturn注解的方法
   */
  @Pointcut("@annotation(com.htks.common.annotation.ServiceAfterReturn)  || @within(com.htks.common.annotation.ServiceAfterReturn)")
  public void afterReturnPointCut() {
    //do nothing
  }

  @Before("pointCut()")
  public void beforeAdvice() {
    //do nothing
  }

  @After("pointCut()")
  public void afterAdvice() {
    //do nothing
  }

  @AfterThrowing(throwing = "e", pointcut = "pointCut()")
  public void afterThrowing(JoinPoint joinPoint, Exception e) {
    final String now = DateTime.now().toString(DATE_FORMAT_DEFAULT);
    final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    final Method method = signature.getMethod();
    final ServiceExceptionHandler annotation = method.getAnnotation(ServiceExceptionHandler.class);
    final String notes = annotation.notes();
    final String cause = e.getCause() != null ? e.getCause().getMessage() : "";
    final String msg = e.getMessage() != null ? e.getMessage() : "";
    final String trace = Arrays.toString(e.getStackTrace());
    final String subject = "异常报警【" + notes + "】";
    final String test = "<%@ page contentType=\"text/html;charset=UTF-8\" language=\"java\" pageEncoding=\"UTF-8\" %>\n"
        + "<html>\n"
        + "<head>\n"
        + "    <link rel=\"icon\" href=\"img/cardlogo.png\" type=\"image/x-icon\">\n"
        + "    <link rel=\"shortcut icon\" href=\"img/cardlogo.png\" type=\"image/x-icon\">\n"
        + "    <style>\n"
        + "        table td {\n"
        + "            vertical-align: top;\n"
        + "            border: solid 1px #888;\n"
        + "            padding: 10px;\n"
        + "        }\n"
        + "    </style>\n"
        + "</head>\n"
        + "<body>\n"
        + "<h1>" + subject + "</h1>\n"
        + "<table>\n"
        + "    <tr>\n"
        + "        <td>Time</td>\n"
        + "        <td>" + now + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>Method</td>\n"
        + "        <td>" + joinPoint.getSignature() + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>Notes</td>\n"
        + "        <td>" + notes + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>Exception</td>\n"
        + "        <td>" + e.getClass().getName() + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>Cause</td>\n"
        + "        <td>" + cause + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>Message</td>\n"
        + "        <td>" + msg.substring(0, Math.min(msg.length(), 1024 * 1024)) + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>Trace</td>\n"
        + "        <td>\n"
        + "            <pre>" + trace.substring(0, Math.min(trace.length(), 1024 * 1024)) + "</pre>\n"
        + "        </td>\n"
        + "    </tr>\n"
        + "</table>\n"
        + "</body>\n"
        + "</html>";
    mailUtils.sendMail(TO_MAILS, CC_MAILS, null, subject, test, null);
  }

  @Around(value = "pointCut()")
  public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
    final DateTime begin = now();
    final Object proceedResult = joinPoint.proceed();
    final long millSeconds = new Duration(begin, now()).getMillis();
    final long minutes = new Duration(begin, now()).getStandardMinutes();
    final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    final Method method = signature.getMethod();
    final ServiceExceptionHandler annotation = method.getAnnotation(ServiceExceptionHandler.class);
    final String notes = annotation.notes();
    final String subject = "执行情况【" + notes + "】";
    final String test = "<%@ page contentType=\"text/html;charset=UTF-8\" language=\"java\" pageEncoding=\"UTF-8\" %>\n"
        + "<html>\n"
        + "<head>\n"
        + "    <link rel=\"icon\" href=\"img/cardlogo.png\" type=\"image/x-icon\">\n"
        + "    <link rel=\"shortcut icon\" href=\"img/cardlogo.png\" type=\"image/x-icon\">\n"
        + "    <style>\n"
        + "        table td {\n"
        + "            vertical-align: top;\n"
        + "            border: solid 1px #888;\n"
        + "            padding: 10px;\n"
        + "        }\n"
        + "    </style>\n"
        + "</head>\n"
        + "<body>\n"
        + "<h1>" + subject + "</h1>\n"
        + "<table>\n"
        + "    <tr>\n"
        + "        <td>时间</td>\n"
        + "        <td>" + begin.toString(DATE_FORMAT_DEFAULT) + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>方法</td>\n"
        + "        <td>" + joinPoint.getSignature() + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>注释</td>\n"
        + "        <td>" + notes + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>耗时</td>\n"
        + "        <td>" + millSeconds + "毫秒(约" + minutes + "分钟)</td>\n"
        + "    </tr>\n"
        + "</table>\n"
        + "</body>\n"
        + "</html>";
    mailUtils.sendMail(TO_MAILS, CC_MAILS, null, subject, test, null);
    return proceedResult;
  }

  @AfterReturning(value = "afterReturnPointCut()", returning = "methodResult")
  public void afterReturning(JoinPoint joinPoint, Object methodResult) {
    final DateTime begin = now();
    final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    final Method method = signature.getMethod();
    final ServiceAfterReturn annotation = method.getAnnotation(ServiceAfterReturn.class);
    final String notes = annotation.notes();
    final String subject = "返回结果【" + notes + "】";
    final String test = "<%@ page contentType=\"text/html;charset=UTF-8\" language=\"java\" pageEncoding=\"UTF-8\" %>\n"
        + "<html>\n"
        + "<head>\n"
        + "    <link rel=\"icon\" href=\"img/cardlogo.png\" type=\"image/x-icon\">\n"
        + "    <link rel=\"shortcut icon\" href=\"img/cardlogo.png\" type=\"image/x-icon\">\n"
        + "    <style>\n"
        + "        table td {\n"
        + "            vertical-align: top;\n"
        + "            border: solid 1px #888;\n"
        + "            padding: 10px;\n"
        + "        }\n"
        + "    </style>\n"
        + "</head>\n"
        + "<body>\n"
        + "<h1>" + subject + "</h1>\n"
        + "<table>\n"
        + "    <tr>\n"
        + "        <td>时间</td>\n"
        + "        <td>" + begin.toString(DATE_FORMAT_DEFAULT) + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>方法</td>\n"
        + "        <td>" + joinPoint.getSignature() + "</td>\n"
        + "    </tr>\n"
        + "    <tr>\n"
        + "        <td>返回</td>\n"
        + "        <td>" + methodResult + "</td>\n"
        + "    </tr>\n"
        + "</table>\n"
        + "</body>\n"
        + "</html>";
    mailUtils.sendMail(TO_MAILS, CC_MAILS, null, subject, test, null);
  }

}
