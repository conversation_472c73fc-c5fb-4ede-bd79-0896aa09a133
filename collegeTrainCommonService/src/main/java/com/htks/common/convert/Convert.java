package com.htks.common.convert;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @ClassName:  Convert
 * @Description: 普通实体类超类
 */
public class Convert implements Serializable {

    /**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 3789065856817967402L;

	/**
     * 获取自动转换后的JavaBean对象
     *
     * @param clazz 需要获取的类型
     * @param <T> 返回的对象
     * @return 返回值
     */
    public <T> T convert(Class<T> clazz) {
        return BeanConverter.convert(clazz, this);
    }
}
