package com.htks.common.convert;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.htks.common.convert.jdk8.Jdk8Module;
import com.htks.common.convert.jsr310.Jsr310Module;
import com.htks.common.convert.jsr310.Jsr310ModuleConfig;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName:  BeanConverter
 * @Description: 对象转换器
 */
public final class BeanConverter {

    private BeanConverter() {}

    private static final ModelMapper modelMapper;

    static {
        modelMapper = new ModelMapper();
        Jsr310ModuleConfig config = Jsr310ModuleConfig.builder()
                .dateTimePattern("yyyy-MM-dd HH:mm:ss")
                .datePattern("yyyy-MM-dd")
                .zoneId(ZoneOffset.UTC)
                .build();
        modelMapper.registerModule(new Jsr310Module(config)).registerModule(new Jdk8Module());
        modelMapper.getConfiguration().setFullTypeMatchingRequired(true);
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
    }

    /**
     * 获取 modelMapper
     *
     */
    public static ModelMapper getModelMapper() {
        return modelMapper;
    }


    /**
     * 列表转换
     *
     * @param clazz the clazz
     * @param list  the list
     */
    public static <T> List<T> convert(Class<T> clazz, List<?> list) {
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list.stream().map(e -> convert(clazz, e)).collect(Collectors.toList());
    }

    /**
     * 单个对象转换
     *
     * @param targetClass 目标对象
     * @param source      源对象
     * @return 转换后的目标对象
     */
    public static <T> T convert(Class<T> targetClass, Object source) {
        return getModelMapper().map(source, targetClass);
    }

}
