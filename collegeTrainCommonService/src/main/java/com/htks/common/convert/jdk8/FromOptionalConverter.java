package com.htks.common.convert.jdk8;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.util.Optional;
import java.util.regex.MatchResult;

/**
 * <AUTHOR>
 * @ClassName:  FromOptionalConverter
 * @Description:
 */
public class FromOptionalConverter implements ConditionalConverter<Optional<Object>, Object> {

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType) {
        return (Optional.class.equals(sourceType) && !Optional.class.equals(destinationType))
                ? MatchResult.FULL
                : MatchResult.NONE;
    }

    @Override
    public Object convert(MappingContext<Optional<Object>, Object> mappingContext) {
        if (mappingContext != null) {
            if (mappingContext.getSource().isPresent() && !ObjectUtils.isNull(mappingContext.getSource())) {

                Optional<Object> source = mappingContext.getSource();
                MappingContext<Object, Object> propertyContext = mappingContext.create(
                        source, mappingContext.getDestinationType());
                return mappingContext.getMappingEngine().map(propertyContext);

            }
        }
        return null;
    }
}
