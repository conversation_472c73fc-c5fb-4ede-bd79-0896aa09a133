package com.htks.common.convert;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName:  Search   
 * @Description: 搜索父类
 * @author: kewen
 */
@Data
public class Search extends Convert implements Serializable {
	
	/**   
	 * @Fields serialVersionUID : 
	 */
	private static final long serialVersionUID = 27288328311076185L;
	
	/**
	 * 开始查询条数
	 */
	@ApiModelProperty("开始查询条数")
	private Long pageNum;
	
	/**
	 * 每页展示条数
	 */
	@ApiModelProperty("每页展示条数")
	private Long pageSize;

	
	
}
