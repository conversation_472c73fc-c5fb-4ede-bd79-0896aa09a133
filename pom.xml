<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.htks</groupId>
  <artifactId>collegeTrainBackPro</artifactId>
  <version>1.0.0</version>
  <packaging>pom</packaging>
  <url>http://maven.apache.org</url>
  <name>00-大学生培训系统-后端</name>

  <modules>
    <module>collegeTrainCommonService</module><!--01-共用Service -->
    <module>collegeTrainWeb</module><!--02-Mis端 -->
  </modules>

  <dependencyManagement>
    <!-- 模块说明：这里声明多个子模块 -->
    <dependencies>
      <!--子版本依赖-->
      <dependency>
        <groupId>com.htks</groupId>
        <artifactId>collegeTrainCommonService</artifactId>
        <version>1.0.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.5.10</version>
  </parent>

  <properties>
    <okhttp3.version>4.8.1 </okhttp3.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <collegeTrainCommonService-version>1.0.0</collegeTrainCommonService-version>
    <HikariCP.version>4.0.3</HikariCP.version>
    <mybatis.spring.boot.version>2.2.2</mybatis.spring.boot.version>
    <mysql-version>8.0.28</mysql-version>
    <sqlserver-version>4.0</sqlserver-version>
    <ngdbc-version>2.4.76</ngdbc-version>
    <lombok-version>1.18.22</lombok-version>
    <joda-time-version>2.10.14</joda-time-version>
    <guava-version>31.1-jre</guava-version>
    <fastjson-version>1.2.83</fastjson-version>
    <common-lang3-version>3.12.0</common-lang3-version>
    <commons-io-version>2.11.0</commons-io-version>
    <org.apache.shiro-version>1.9.0</org.apache.shiro-version>
    <httpcore-version>4.4.15</httpcore-version>
    <java-jwt-version>3.10.3</java-jwt-version>
    <knife4j-version>2.0.9</knife4j-version>
    <mapstruct-version>1.4.2.Final</mapstruct-version>
    <thumbnailator-version>0.4.17</thumbnailator-version>
    <junit-version>4.13</junit-version>
    <easyexcel-version>3.0.5</easyexcel-version>
    <json-lib-version>2.4</json-lib-version>
    <jakarta.mail-version>1.6.5</jakarta.mail-version>
    <weixin-java-cp-version>4.3.7.B</weixin-java-cp-version>
    <jcifs-version>2.1.32</jcifs-version>

  </properties>

  <repositories>
    <repository>
      <id>central</id><!-- 修改中央仓库 -->
      <name>aliyun nexus</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled><!--禁止从公共仓库下载snapshot构件-->
      </snapshots>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>central</id><!-- 修改中央仓库 -->
      <name>aliyun nexus</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>

</project>
