# 接口幂等性和防超卖使用指南

## 概述

本项目实现了完整的接口幂等性控制和库存防超卖机制，基于Redis提供分布式环境下的高可用解决方案。

## 核心组件

### 1. 分布式锁工具类 (DistributedLockUtils)

提供基于Redis的分布式锁功能，支持：
- 原子性加锁/解锁
- 锁自动过期
- 防止死锁
- 锁续期

**使用示例：**
```java
@Autowired
private DistributedLockUtils distributedLockUtils;

// 获取锁
String lockValue = distributedLockUtils.tryLock("my_lock", 30, 5000);
if (lockValue != null) {
    try {
        // 执行业务逻辑
    } finally {
        // 释放锁
        distributedLockUtils.releaseLock("my_lock", lockValue);
    }
}
```

### 2. 幂等性工具类 (IdempotentUtils)

提供接口幂等性控制，支持：
- Token生成和验证
- 防重复提交
- 自动过期清理

**使用示例：**
```java
@Autowired
private IdempotentUtils idempotentUtils;

// 生成token
String token = idempotentUtils.generateToken("business_key");

// 验证并消费token
boolean valid = idempotentUtils.validateAndConsumeToken("business_key", token);
```

### 3. 幂等性注解 (@Idempotent)

通过注解方式实现方法级别的幂等性控制：

```java
@Idempotent(businessKey = "reservation:#employeeNo:#type", 
           message = "请勿重复提交预约申请")
@PostMapping("/addReservation")
public ResultVO addReservation(@RequestBody AppointMent reservation) {
    // 业务逻辑
}
```

**注解参数说明：**
- `businessKey`: 业务标识，支持SpEL表达式
- `tokenName`: token参数名，默认"Idempotent-Token"
- `tokenSource`: token来源（HEADER/PARAMETER/BODY）
- `message`: 失败时的错误消息

### 4. 库存管理工具类 (StockUtils)

提供分布式库存管理，防止超卖：
- 原子性库存扣减
- 预扣库存机制
- 库存回滚
- 实时库存查询

**使用示例：**
```java
@Autowired
private StockUtils stockUtils;

// 初始化库存
stockUtils.initStock("product_001", 100);

// 扣减库存
int remaining = stockUtils.deductStock("product_001", 1);
if (remaining >= 0) {
    // 扣减成功
} else {
    // 库存不足
}

// 预扣库存（带锁）
StockUtils.PreDeductResult result = stockUtils.preDeductStock("product_001", 1, 30);
if (result.isSuccess()) {
    try {
        // 执行业务逻辑
        stockUtils.confirmPreDeduct("product_001", result.getLockValue());
    } catch (Exception e) {
        // 业务失败，回滚库存
        stockUtils.rollbackPreDeduct("product_001", 1, result.getLockValue());
    }
}
```

## 实际应用场景

### 1. 预约系统防超卖

```java
@Transactional(rollbackFor = Exception.class)
@Idempotent(businessKey = "reservation:#employeeNo:#type")
@PostMapping("/addReservation")
public ResultVO addReservation(@RequestBody AppointMent reservation) {
    // 1. 预扣库存
    StockUtils.PreDeductResult result = stockUtils.preDeductStock(stockKey, 1, 30);
    
    if (!result.isSuccess()) {
        return new ResultVO(1001, "预约名额已满");
    }
    
    try {
        // 2. 执行业务逻辑
        reservationService.addAppointment(reservation);
        
        // 3. 确认预扣
        stockUtils.confirmPreDeduct(stockKey, result.getLockValue());
        
        return new ResultVO(1000, "预约成功");
    } catch (Exception e) {
        // 4. 异常回滚
        stockUtils.rollbackPreDeduct(stockKey, 1, result.getLockValue());
        throw e;
    }
}
```

### 2. 商品秒杀场景

```java
@Idempotent(businessKey = "seckill:#userId:#productId")
@PostMapping("/seckill")
public ResultVO seckill(@RequestParam String userId, @RequestParam String productId) {
    String stockKey = "seckill:" + productId;
    
    // 原子性扣减库存
    int remaining = stockUtils.deductStock(stockKey, 1);
    
    if (remaining < 0) {
        return new ResultVO(1001, "商品已售罄");
    }
    
    try {
        // 创建订单
        orderService.createOrder(userId, productId);
        return new ResultVO(1000, "抢购成功");
    } catch (Exception e) {
        // 失败时恢复库存
        stockUtils.addStock(stockKey, 1);
        throw e;
    }
}
```

## 前端集成

### 1. 获取幂等性Token

```javascript
// 获取token
const response = await fetch('/improved/appointment/token', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'employeeNo=12345&type=1'
});
const result = await response.json();
const token = result.data;
```

### 2. 携带Token调用接口

```javascript
// 调用业务接口
const response = await fetch('/improved/appointment/addReservation', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Idempotent-Token': token  // 在请求头中携带token
    },
    body: JSON.stringify(reservationData)
});
```

## 配置说明

### Redis配置
```yaml
redis:
  database: 0
  host: ***********
  port: 6379
  password: root
  timeout: 6000
  jedis:
    pool:
      max-active: 16
      max-idle: 8
      min-idle: 2
      max-wait: -1
```

## 监控和运维

### 1. 关键指标监控
- Redis连接池状态
- 锁获取成功率
- 库存扣减成功率
- 幂等性token使用情况

### 2. 异常处理
- 锁超时处理
- Redis连接异常
- 库存数据不一致

### 3. 性能优化
- 合理设置锁超时时间
- 优化Redis连接池配置
- 定期清理过期数据

## 注意事项

1. **锁的粒度**：选择合适的锁粒度，避免锁竞争过于激烈
2. **超时设置**：合理设置锁和token的超时时间
3. **异常处理**：确保在异常情况下正确释放锁和回滚库存
4. **数据一致性**：在分布式环境下注意数据一致性问题
5. **性能考虑**：高并发场景下注意Redis性能瓶颈

## 测试验证

运行测试类 `IdempotentAndStockTest` 验证功能：

```bash
mvn test -Dtest=IdempotentAndStockTest
```

测试覆盖：
- 幂等性token生成和验证
- 分布式锁并发测试
- 库存管理功能测试
- 超卖防护验证
- 预扣库存机制测试
