<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>collegeTrainBackPro</artifactId>
    <groupId>com.htks</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>collegeTrainBack</artifactId>
  <name>02-Mis端</name>

  <properties>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.htks</groupId>
      <artifactId>collegeTrainCommonService</artifactId>
      <version>${collegeTrainCommonService-version}</version>
    </dependency>
    <dependency>
      <!--支持Undertow高性能Web服务器 -->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-undertow</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <!--支持全栈式Web开发，包括Tomcat和spring-webmvc -->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <!-- 排除Tomcat依赖，使用Undertow -->
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!--支持Spring WebFlux响应式Web框架，响应式的、异步非阻塞的 -->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
      <exclusions>
        <!-- 排除Tomcat依赖，使用Undertow -->
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!--支持热部署-->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <!--支持参数验证-->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <!--支持面向方面的编程AOP-->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <dependency>
      <!--支持单元测试-->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!--支持xml或properties配置-->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <!--支持日志框架Logback-->
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-logging</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>log4j-over-slf4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!-- Hikari DB连接池 -->
    <dependency>
      <groupId>com.zaxxer</groupId>
      <artifactId>HikariCP</artifactId>
      <version>${HikariCP.version}</version>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>${mysql-version}</version>
    </dependency>
    <dependency>
      <groupId>com.sap.cloud.db.jdbc</groupId>
      <artifactId>ngdbc</artifactId>
      <version>${ngdbc-version}</version>
    </dependency>
    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
      <version>${mybatis.spring.boot.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>${httpcore-version}</version>
    </dependency>
    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
      <version>${java-jwt-version}</version>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>${joda-time-version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>${guava-version}</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson-version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok-version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${common-lang3-version}</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>${commons-io-version}</version>
    </dependency>
    <dependency>
      <groupId>net.sf.json-lib</groupId>
      <artifactId>json-lib</artifactId>
      <version>${json-lib-version}</version>
      <classifier>jdk15</classifier>
    </dependency>

    <!--org apache shrio-->
    <dependency>
      <groupId>org.apache.shiro</groupId>
      <artifactId>shiro-core</artifactId>
      <version>${org.apache.shiro-version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.shiro</groupId>
      <artifactId>shiro-web</artifactId>
      <version>${org.apache.shiro-version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.shiro</groupId>
      <artifactId>shiro-ehcache</artifactId>
      <version>${org.apache.shiro-version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.shiro</groupId>
      <artifactId>shiro-spring</artifactId>
      <version>${org.apache.shiro-version}</version>
    </dependency>

    <!-- MapStruct 类型转换 -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${mapstruct-version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>${mapstruct-version}</version>
      <scope>compile</scope>
    </dependency>

    <!-- EasyExcel -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>${easyexcel-version}</version>
    </dependency>

    <!-- swagger -->
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-spring-boot-starter</artifactId>
      <version>${knife4j-version}</version>
    </dependency>

  <!--nimio文件上传-->
  <dependency>
    <groupId>io.minio</groupId>
    <artifactId>minio</artifactId>
    <version>8.5.4</version>
  </dependency>
</dependencies>
  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**.*</include>
          <include>**/*.*</include><!-- i18n能读取到 -->
          <include>**/*/*.*</include>
        </includes>
      </resource>
      <resource>
        <filtering>true</filtering><!-- yaml中可通过@value@的方式引用pom中的变量 -->
        <directory>src/main/resources/</directory>
        <includes>
          <include>application.yml</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <encoding>utf-8</encoding>
          <meminitial>128m</meminitial>
          <maxmem>256m</maxmem>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <!-- 生成javadoc文档包的插件 -->
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.1.0</version>
        <configuration>
          <charset>UTF-8</charset>
          <encoding>UTF-8</encoding>
          <docencoding>UTF-8</docencoding>
          <bottom>
            <![CDATA[Copyright &copy; 2008-{currentYear} HT-Tech, LLC.</a>  All Rights Reserved]]>
          </bottom>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <fork>true</fork><!--开启热部署-->
          <jvmArguments>-Dfile.encoding=UTF-8</jvmArguments>
          <includeSystemScope>true</includeSystemScope>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <artifactId>sonar-maven-plugin</artifactId>
        <version>3.6.0.1398</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <!-- 执行测试用例的插件 -->
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M3</version>
        <configuration>
          <skipTests>true</skipTests>
        </configuration>
      </plugin>
      <plugin>
        <groupId>cz.habarta.typescript-generator</groupId>
        <!-- Typescript-generator -->
        <artifactId>typescript-generator-maven-plugin</artifactId>
        <version>2.35.1025</version>
        <executions>
          <execution>
            <id>generate</id>
            <goals>
              <goal>generate</goal>
            </goals>
            <phase>process-classes</phase>
          </execution>
        </executions>
        <configuration>
          <jsonLibrary>jackson2</jsonLibrary>
          <classPatterns>
            <classPattern>com.htks.web.*.vo.*</classPattern>
            <classPattern>com.htks.web.*.*Controller</classPattern>
          </classPatterns>
          <outputKind>module</outputKind>
          <scanSpringApplication>true</scanSpringApplication>
          <generateSpringApplicationClient>true</generateSpringApplicationClient>
          <extensions>
            <extension>cz.habarta.typescript.generator.ext.AxiosClientExtension</extension>
          </extensions>
          <restOptionsType>AxiosRequestConfig</restOptionsType>
          <stringQuotes>singleQuotes</stringQuotes>
          <restNamespacing>perResource</restNamespacing>
          <outputFileType>implementationFile</outputFileType>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>cz.habarta.typescript-generator</groupId>
            <artifactId>typescript-generator-spring</artifactId>
            <version>2.35.1025</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>

    <finalName>
      ${project.artifactId}-${project.version}
    </finalName>

  </build>
</project>
