package com.htks.web;

import com.htks.common.config.WeChatConfig;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.external.WxService;
import com.htks.common.external.wx.WxSignature;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.api.impl.WxCpOAuth2ServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 * @description:
 * <AUTHOR>
 * @create: 2021-05-09 17:04:23
 */
@Api(tags = {"企业微信 - 用户消息"})
@RestController
@RequestMapping("/wx/**")
public class WxController {

    public static final Logger logger = LoggerFactory.getLogger(WxController.class);

    @Resource
    private WeChatConfig wxConfig;


    @Resource
    private WxService wxService;

    private WxCpOAuth2Service auth2Service = null;

    // 获取userId wx/getUserInfo
    @ApiOperation("获取userId")
    @PostMapping("/getUserInfo")
    public ResultVO<String> getUserInfo(@RequestParam("code")String code, @RequestParam(value = "userId",required = false)String userId)throws Exception {
        logger.info("编码：{}",code);
        logger.info("企业微信用户userId：{}",userId);

        WxCpOauth2UserInfo wxCpOauth2UserInfo = null;
        if(StringUtils.isEmpty(userId)) {
            if (null == auth2Service){
                auth2Service = new WxCpOAuth2ServiceImpl(wxConfig.getWxCpService());
            }
            wxCpOauth2UserInfo = auth2Service.getUserInfo(wxConfig.getAgentId(),code);
            logger.info("企业微信用户信息：{}",wxCpOauth2UserInfo);
        } else {
            return new ResultVO<>(userId);
        }
        return new ResultVO<>(wxCpOauth2UserInfo.getUserId());
    }

    // 获取微信sdk
    @ApiOperation("获取微信sdk")
    @PostMapping("/getWxJsSdkConfig")
    public ResultVO<WxSignature> getWxJsSdkConfig(@RequestParam(value = "url")String url)throws Exception {
        logger.info("客户url：{}",url);
        return wxService.getWxJsSdkConfig(wxConfig,url);
    }

}
