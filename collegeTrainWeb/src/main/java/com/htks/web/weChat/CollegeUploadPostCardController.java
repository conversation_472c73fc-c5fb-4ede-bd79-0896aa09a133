package com.htks.web.weChat;



import com.htks.common.external.FileService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.examiner.dto.Colleger;
import com.htks.domain.examiner.service.ExaminerService;
import com.htks.domain.pushCourse.impl.PushPostExamSchedule;
import com.htks.domain.student.dto.PostCard;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.domain.student.service.SatisfactionDegreeService;
import com.htks.domain.student.service.UploadPostCardService;
import com.htks.web.common.vo.AttachmentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Api(tags = {"大学生 - 上岗证上传"})
@RestController
public class CollegeUploadPostCardController {
    /**
     * 阅卷师企业微信端
     *
     * <AUTHOR>
     * @date 2023/5/10.
     */
    @Autowired
    private FileService fileService;
@Autowired
   private PushPostExamSchedule pushPostExamSchedule;

    @Autowired
    private ExaminerService examinerService;
    @Autowired
    private SatisfactionDegreeService satisfactionDegreeService;

    @Autowired
    private UploadPostCardService uploadPostCardService;
@Autowired
private UploadWorkLicense uploadWorkLicense;

    @ApiOperation("0101 - 信息自动填入")
    @PostMapping("/post/approvalHomepage")
    public ResultVO approvalHomepage(@RequestParam String employeeNo) {
        try {
            Colleger colleger = examinerService.startApproval(employeeNo);
            return new ResultVO(1000,"查询成功",colleger);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0102 - 展示考核通过的上岗证")
    @PostMapping("/post/getPassedPost")
    public ResultVO getPassedPost(@RequestParam String area,String employeeNo) {
        try {
            List<String> li = uploadPostCardService.getWorkLicense(area, employeeNo);
            return new ResultVO(1000,"查询成功",li);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0103 - 上岗证上传开始、截止时间获取")
    @PostMapping("/post/getStartEndTime")
    public ResultVO getStartEndTime(String employeeNo) {
        try {
            String back="0";
            String start = uploadPostCardService.postUploadStartTime(employeeNo);
            String end = uploadPostCardService.postUploadEndTime(employeeNo);
            Date dateToday = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String currentTime = sdf.format(dateToday);
            if (currentTime.compareTo(start) < 0 || currentTime.compareTo(end) > 0) {
                 back= "上岗证上传时间为" + start + "~" + end + "请在上传时间段内进行上传，谢谢配合！";
            }
            return new ResultVO(1000,"查询成功",back);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }

    }

    @ApiOperation("0104 - 获取学员已经上传的本部门上岗证")
    @PostMapping("/post/showDepartmentPost")
    public ResultVO showDepartmentPost(String area,String employeeNo) {
        try {
            List<PostCard> li = uploadPostCardService.findDepartmentCard(area, employeeNo);
            li = li.stream()
                    .sorted(Comparator.comparingLong(PostCard::getId).reversed())
                    .collect(Collectors.toList());
            Map<String, List<PostCard>> map = li.stream().collect(Collectors.groupingBy(PostCard::getPostArea));
            Map<String, List<PostCard>> sortedMap = new HashMap<>();

// 遍历原始的映射
            for (Map.Entry<String, List<PostCard>> entry : map.entrySet()) {
                // 对每个值列表进行排序，并存储到新的映射中
                List<PostCard> sortedList = entry.getValue().stream()
                        .sorted(Comparator.comparingLong(PostCard::getId).reversed())
                        .collect(Collectors.toList());
                sortedMap.put(entry.getKey(), sortedList);
            }
            map = map.entrySet().stream()
                    .sorted((entry1, entry2) -> {
                        // 比较两个值列表中第一个PostCard对象的ID，降序排列
                        long id1 = entry1.getValue().isEmpty() ? Long.MIN_VALUE : entry1.getValue().get(0).getId();
                        long id2 = entry2.getValue().isEmpty() ? Long.MIN_VALUE : entry2.getValue().get(0).getId();
                        return Long.compare(id2, id1);
                    })
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue, // 如果键冲突，保留旧值
                            LinkedHashMap::new // 保持顺序
                    ));
                return new ResultVO(1000,"查询成功",map);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0105 - 获取学员已经上传的区域上岗证")
    @PostMapping("/post/showAreaPost")
    public ResultVO showAreaPost(String area,String employeeNo) {
        try {
            List<PostCard> li = uploadPostCardService.findAreaCard(area, employeeNo);
            Map<String, List<PostCard>> map = li.stream().collect(Collectors.groupingBy(PostCard::getBatch));
            Map<String, List<PostCard>> sortedMap = new HashMap<>();

// 遍历原始的映射
            for (Map.Entry<String, List<PostCard>> entry : map.entrySet()) {
                // 对每个值列表进行排序，并存储到新的映射中
                List<PostCard> sortedList = entry.getValue().stream()
                        .sorted(Comparator.comparingLong(PostCard::getId).reversed())
                        .collect(Collectors.toList());

                sortedMap.put(entry.getKey(), sortedList);
            }   map = map.entrySet().stream()
                    .sorted((entry1, entry2) -> {
                        // 比较两个值列表中第一个PostCard对象的ID，降序排列
                        long id1 = entry1.getValue().isEmpty() ? Long.MIN_VALUE : entry1.getValue().get(0).getId();
                        long id2 = entry2.getValue().isEmpty() ? Long.MIN_VALUE : entry2.getValue().get(0).getId();
                        return Long.compare(id2, id1);
                    })
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue, // 如果键冲突，保留旧值
                            LinkedHashMap::new // 保持顺序
                    ));
            return new ResultVO(1000,"查询成功",map);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }

    }

   /* @Transactional(rollbackFor = Exception.class)
    @ApiOperation("0106 - 上传上岗证")
    @PostMapping("/post/uploadPostCard")
    public ResultVO uploadPostCard(@RequestBody List<PostCard> postCard) {
        //todo 待修改
        try {
            for (int i = 0; i < postCard.size(); i++) {
                Long access_id = satisfactionDegreeService.getAccessId( postCard.get(i).getSTUDENT_NO());
                uploadPostCardService.uploadWorkLicense(postCard.get(i),access_id);
            }
            return new ResultVO(1000,"上传成功");
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"上传失败，请联系后台管理人员处理");
        }
    }*/
   @Transactional(rollbackFor = Exception.class)
   @ApiOperation("0106 - 上传上岗证")
   @PostMapping("/post/uploadPostCard")
   public ResultVO uploadPostCard(@RequestParam String area,String postName, String employeeNo, String fileA, String fileAName, String fileB,String fileBName,String batch) {
       //todo 待修改
       final AttachmentVO attachmentResult = new AttachmentVO();
       try {
           Long access_id = satisfactionDegreeService.getAccessId(employeeNo);
           String postId=  uploadWorkLicense.getpostCardId(area,postName,employeeNo);
           uploadPostCardService.uploadWorkLicense(area, postName, access_id,batch,postId);

               // 获取文件名
               String fileName =fileAName;
               //文件类型
               String fileTypeA = "";
               int dotIndex = fileName.lastIndexOf(".");
               if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
                   fileTypeA= fileName.substring(dotIndex + 1).toLowerCase();
               }
               AttachmentEntity attachmentEntity = new AttachmentEntity();
               attachmentEntity.setAttachmentName(fileName);
               attachmentEntity.setAttachmentType(fileTypeA);
               if (!fileB.isEmpty()){
               attachmentEntity.setAttachmentPath(fileA+","+fileB);
               }else {
                   attachmentEntity.setAttachmentPath(fileA);
               }
               attachmentEntity.setAttachmentMemo(employeeNo);
               Long id = uploadPostCardService.getPostCardId(area, postName,employeeNo);

               uploadPostCardService.addPostCardPicture(attachmentEntity, id,access_id);
/*           if (fileB.equals("")||fileB == null)
           {
           }
           else {
               //原始文件名称
               // 获取文件名
               String fileName = fileBName;
               //文件类型
               String fileTypeB = "";
               int dotIndex = fileName.lastIndexOf(".");
               if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
                   fileTypeB= fileName.substring(dotIndex + 1).toLowerCase();
               }
               AttachmentEntity attachmentEntity = new AttachmentEntity();
               attachmentEntity.setAttachmentName(fileName);
               attachmentEntity.setAttachmentType(fileTypeB);
               attachmentEntity.setAttachmentPath(fileB);
               attachmentEntity.setAttachmentMemo(employeeNo);
               Long id = uploadPostCardService.getPostCardId(access_id, postName);
               uploadPostCardService.addPostCardPicture(attachmentEntity, id,null);
           }*/
           return new ResultVO(1000, "上传成功");
       } catch (Exception e) {
           e.printStackTrace();
           return new ResultVO(1001, "上传失败，请联系后台管理人员处理");
       }

   }


    public ResultVO fileUpload(@RequestParam("file") MultipartFile file,String postName,String employeeNo,String tempPath) {
        final AttachmentVO attachmentResult = new AttachmentVO();
        try {
            //原始文件名称
            Date timeToday = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddhhmmss");
            String currentTime2 = simpleDateFormat.format(timeToday);
            final String originalFilename = currentTime2+file.getOriginalFilename();
            //文件类型
            String fileType;
            if (originalFilename != null) {
                fileType = originalFilename.substring(originalFilename.indexOf(".") + 1).toLowerCase();
                tempPath=tempPath+"\\"+"PostCard"+"\\"+employeeNo+"\\";
                final String attachmentURI = tempPath +originalFilename;
                //写文件 - 存到临时文件路径
                fileService.write(file.getBytes(), tempPath, originalFilename);
                attachmentResult.setAttachmentName(originalFilename);
                attachmentResult.setAttachmentType(fileType.toUpperCase());
                attachmentResult.setAttachmentPath(attachmentURI);
                AttachmentEntity attachmentEntity = new AttachmentEntity();
                BeanUtils.copyProperties(attachmentEntity, attachmentResult);
                Long access_id = satisfactionDegreeService.getAccessId(employeeNo);
                Long id=uploadPostCardService.getPostCardId(employeeNo,postName,employeeNo);
                uploadPostCardService.addPostCardPicture(attachmentEntity,id,null);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException | IOException e) {
            e.printStackTrace();
        }

        return new ResultVO(attachmentResult);
    }






    


    @ApiOperation("0105 - 获取上岗证测试")
    @PostMapping("/post/uploadPostCardTest")
    @Transactional(rollbackFor = Exception.class)
    public ResultVO uploadPostCardTest(@RequestParam String area,String no) {
        try {
            long id = uploadPostCardService.getWorkLicense21(no);
            List<String> li = uploadPostCardService.getWorkLicense22(id);
            List<String> list = uploadPostCardService.getWorkLicense23( area,li);
            return new ResultVO(1000,"上传成功",list);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"上传失败，请联系后台管理人员处理");
        }
    }



    @ApiOperation("0105 - 获取上岗证测试222222")
    @PostMapping("/post/uploadPostCardTest22")
    @Transactional(rollbackFor = Exception.class)
    public ResultVO uploadPostCardTest33() {
        try {
            pushPostExamSchedule.pushPostExam();
            return new ResultVO(1000,"上传成功");
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"上传失败，请联系后台管理人员处理");
        }
    }


    @ApiOperation("0105 - 获取上岗证测试3333333")
    @PostMapping("/post/uploadPostCardTest33")
    @Transactional(rollbackFor = Exception.class)
    public ResultVO uploadPostCardTes22t() {
        try {
            pushPostExamSchedule.sendReservation();
            return new ResultVO(1000,"上传成功");
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"上传失败，请联系后台管理人员处理");
        }
    }
    @ApiOperation("0105 - 获取上岗证开始结束时间")
    @PostMapping("/post/getPostCardTime")
    @Transactional(rollbackFor = Exception.class)
    public ResultVO getPostCardTime(String employeeNo) {
        try {
            return new ResultVO(1000,"成功",uploadPostCardService.getPostTime(employeeNo));
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"上传失败，请联系后台管理人员处理");
        }
    }
    @ApiOperation(" - 展示学员所属部门的区域")
    @PostMapping("/post/getAreaList")
    public ResultVO getAreaList(@RequestParam String employeeNo) {
        try {
            List<String> li = uploadPostCardService.getAreaList(employeeNo);
            return new ResultVO(1000,"查询成功",li);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("展示有上岗证通过的区域")
    @PostMapping("/post/getWorkLicenseArea")
    public ResultVO getWorkLicenseArea(@RequestParam String employeeNo) {
        try {
            List<String> li = uploadPostCardService.getWorkLicenseArea("",employeeNo);
            List<String> distinctList = li.stream()
                    .distinct()
                    .collect(Collectors.toList());
            return new ResultVO(1000,"查询成功",distinctList);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }
    }

}
