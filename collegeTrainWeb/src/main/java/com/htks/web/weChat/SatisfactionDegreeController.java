package com.htks.web.weChat;


import com.htks.common.external.wx.ResultVO;
import com.htks.domain.examiner.dto.Colleger;
import com.htks.domain.examiner.dto.Examiner;
import com.htks.domain.examiner.service.ExaminerService;
import com.htks.domain.student.service.SatisfactionDegreeService;
import com.htks.domain.student.service.impl.SatisfactionDegreeServiceImpl;
import com.htks.domain.student.dto.SatisfactionDegree;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Api(tags = {"大学生 - 满意度调查"})
@RestController
public class SatisfactionDegreeController {

    @Autowired
    SatisfactionDegreeService satisfactionDegreeService;
    @Autowired
    SatisfactionDegreeServiceImpl satisfactionDegreeServiceimpl;
    @Autowired
    ExaminerService examinerService;


    @ApiOperation("0101 - 信息自动填入")

    @PostMapping("/evaluate/approvalHomepage")
    public ResultVO approvalHomepage(@RequestParam String employeeNo) {
        try {
            Colleger colleger = examinerService.startApproval(employeeNo);
            return new ResultVO(1000, "查询成功", colleger);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "自动插入失败，请联系后台管理员处理");
        }
    }


    @ApiOperation("0102 - 日期检测")
    @PostMapping("/evaluate/dateCheck")
    public String dateCheck(@RequestParam String employeeNo) throws ParseException {
        String message;
        //获取今天日期
        Date datetoday = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String currentTime = sdf.format(datetoday);
        Date date = sdf.parse(currentTime);

        datetoday = java.sql.Date.valueOf(sdf.format(date));


        Integer appraiseTime = satisfactionDegreeService.appraiseIsNull(employeeNo);
        //判断是第一次填写还是后续更新
        if (appraiseTime == 0) {
            //获取入职日期
            String onboardingtime = satisfactionDegreeService.onboardingTime(employeeNo);
            Date date_onboarding = sdf.parse(onboardingtime);
            // satisfactionDegree.setCREATED_TIME(date_onboarding);
            //获取入职第二周周一日期
            Date date2 = satisfactionDegreeServiceimpl.getThisWeekMonday(date_onboarding);
            Calendar cal = Calendar.getInstance();
            cal.setTime(date2);
            cal.add(Calendar.DATE, 7);
            Date nextWonday = cal.getTime();
            String nextmonday = sdf.format(nextWonday);
            nextWonday = sdf.parse(nextmonday);
            //今天日期与入职第二周周一日期做比较
            if (datetoday.compareTo(nextWonday) >= 0) {
                message = "0";
            } else {
                message = "还未到评价时间";
            }
        } else {
            //获取上次更新日期
            Date date_onboarding = sdf.parse(satisfactionDegreeService.newDate(employeeNo));
            // satisfactionDegree.setCREATED_TIME(date_onboarding);
            //获取上次更新后第四周周一日期
            Date date2 = satisfactionDegreeServiceimpl.getThisWeekMonday(date_onboarding);
            Calendar cal = Calendar.getInstance();
            cal.setTime(date2);
            cal.add(Calendar.DATE, 28);
            Date nextthursday = cal.getTime();
            nextthursday = java.sql.Date.valueOf(sdf.format(nextthursday));
            //今天日期与更新后第四周周一日期做比较
            if (datetoday.compareTo(nextthursday) >= 0) {
                message = "0";

            } else {
                message = "您本周期已评价完成，不可再进行评价，谢谢！";
            }
        }
        return message;
    }

    @ApiOperation("0103 - 插入评价分数")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/evaluate/insertDegree")
    public ResultVO insertDegree(@RequestBody SatisfactionDegree satisfactionDegree)  {
        try {
            Date datetoday = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String currentTime = sdf.format(datetoday);
            Date date = sdf.parse(currentTime);
            datetoday = java.sql.Date.valueOf(sdf.format(date));
            satisfactionDegree.setCREATED_TIME(datetoday.toString());
            Long a = satisfactionDegreeService.getAccessId(satisfactionDegree.getEmployeeNo());
            Integer count=satisfactionDegreeService.judgeIsfirst(a);
   //         if (count==0) {
                satisfactionDegree.setSCORE(satisfactionDegree.getTHEORY_COURSE_DURATION()
                        + satisfactionDegree.getTHEORY_MAJOR()
                        + satisfactionDegree.getTHEORY_COURSE_INTERACTION()
                        + satisfactionDegree.getMASTER_MAJOR()
                        + satisfactionDegree.getMASTER_COURSE_ARRANGE()
                        + satisfactionDegree.getMASTER_PRACTICAL_OPERATION()
                        + satisfactionDegree.getMASTER_PATIENCE()
                        + satisfactionDegree.getLEADER_QUESTION()
                        + satisfactionDegree.getLEADER_MEET()
                        + satisfactionDegree.getLEADER_CARE()
                        + satisfactionDegree.getSTUDY_PRACTICAL_OPERATION()
                        + satisfactionDegree.getSTUDY_THEORY()
                        + satisfactionDegree.getSTUDY_REPORT()
                );
                satisfactionDegreeService.inserDegree(satisfactionDegree, a);
                Integer score = satisfactionDegreeService.getNewScore(satisfactionDegree.getEmployeeNo());

                return new ResultVO(1000, "插入成功", "您的满意度评价总分为" + score);
  /*          }else {
                return new ResultVO(1000, "插入失败", "请勿重复插入");
            }*/
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "插入失败");
        }
    }

}
