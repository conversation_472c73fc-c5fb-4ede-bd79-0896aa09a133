package com.htks.web.weChat;

import com.htks.common.external.FileService;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.domain.question.dto.QuestionItemEntity;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainAttachment;
import com.htks.domain.school.dto.CollegeTrainExamRecord;
import com.htks.domain.school.dto.CollegeTrainExamRecordAnswer;
import com.htks.domain.student.dto.*;
import com.htks.domain.student.repository.hana.ExamRepository;
import com.htks.domain.student.service.ExamService;
import com.htks.domain.student.service.SatisfactionDegreeService;
import com.htks.domain.student.service.UploadPostCardService;
import com.htks.web.common.vo.AttachmentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.io.File;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Api(tags = {"大学生 - 考试"})
@RestController
public class ExamController {
    @Autowired
    ExamService examService;

    @Autowired
    private SatisfactionDegreeService satisfactionDegreeService;

    @Autowired
    private UploadPostCardService uploadPostCardService;

    @Autowired
    private ExamRepository examRepository;

    @ApiOperation("0101 - 查询其他未考试考试信息")
    @PostMapping("exam/getExamByDate")
    public ResultVO getExamByDate(@RequestParam String employeeNo) {
        try {
            Date timeToday = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime2 = simpleDateFormat.format(timeToday);
            List<ExamRecord> list = examService.getAllExam(employeeNo, currentTime2);
            for (int i = 0; i < list.size(); i++) {
                if(list.get(i).getExamCategoryId()==8){
                    String start = uploadPostCardService.postUploadStartTime(employeeNo);
                    String end = uploadPostCardService.postUploadEndTime(employeeNo);
                    list.get(i).setExamStartTime(start);
                    list.get(i).setExamEndTime(end);
                }
            }
            return new ResultVO(1000, "查询成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0102 - 查询区域内的未考试上岗证考试")
    @PostMapping("/exam/getPostExamByDate")
    public ResultVO approvalHomepage(@RequestParam String employeeNo, @RequestParam(required = false) String area) {
        try {
            Date timeToday = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            String currentTime2 = simpleDateFormat.format(timeToday);
            if (area == null || area.equals("")) {
                area = examService.getAreaByDepartment(employeeNo);
            }

            List<ExamRecord> list = examService.getExamByArea(employeeNo, area);

            List<ExamRecord> examRecord = examService.getPassExamByArea(employeeNo,area);
             examRecord = examRecord.stream()
                    .collect(Collectors.toMap(ExamRecord::getPostInfoId, // 以 id 作为键
                            record -> record, // 对于重复键，保留第一个实例
                            (existing, replacement) -> existing)) // 合并函数，保留现有实例
                    .values()
                    .stream()
                    .collect(Collectors.toList());

            List<String>postId=new ArrayList<>();
            CollegeTrainAssessed collegeTrainAssessed=examRepository.getcollegeTrainAssessed(employeeNo);
            for (int i = 0; i <examRecord.size() ; i++) {
                if (examRecord.get(i).getTotalScore()==null||examRecord.get(i).getTotalScore()<100){
                    examRecord.get(i).setExamStartTime(collegeTrainAssessed.getPostCertificateStartTime());
                    examRecord.get(i).setExamEndTime(collegeTrainAssessed.getPostCertificateEndTime());
                    examRecord.get(i).setPaperName(examRepository.paperName(examRecord.get(i).getPostInfoId()));
                    list.add(examRecord.get(i));
                }else {
                    postId.add(examRecord.get(i).getPostInfoId());
                }
            }
            if (list.size()==0){

            }else {
                for (int i = 0; i < list.size(); i++) {
                    if(list.get(i).getTotalScore()==null) {
                        String paperName = list.get(i).getPaperName();
                        list.get(i).setExamName("上岗证考核-" + paperName);
                    }
                    list.get(i).setPaperType("岗位级");
                    list.get(i).setCategoryItem("上岗证考核");
                    list.get(i).setTestTime("60");
                }
            }
            for (ExamRecord examQuestion:list){
                    examQuestion.setSingleOptionAnswer(4);
                    examQuestion.setMoreOptionAnswer(6);
                    examQuestion.setJudgeAnswer(4);
                    examQuestion.setFillAnswer(6);
            }
             list =  list.stream()
                     .sorted(Comparator.comparing(ExamRecord::getTotalScore,
                             Comparator.nullsLast(Comparator.reverseOrder())))
                     .collect(Collectors.toList());
            list = list.stream()
                    .filter(record -> !postId.contains(record.getPostInfoId()))
                    .collect(Collectors.toList());
            if (!area.equals(examRepository.getArea(employeeNo))){
                for (int i = 0; i < list.size(); i++) {
                    list.get(i).setIsCore(null);
                }
            }
            return new ResultVO(1000, "查询成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }


    @ApiOperation("0104 - 获取其他考试试题")
    @PostMapping("/exam/getOtherExamQuestion")
    public ResultVO getOtherExamQuestion(@RequestParam String categoryItem,String employeeNo) {
        return examService.getOtherExamQuestion(categoryItem,employeeNo);
    }

   @ApiOperation("0105 - 查看其他已完成考试")
    @PostMapping("/exam/getPassAllExam")
    public ResultVO getPassAllExam(@RequestParam String employeeNo) {
        try {
            List<ExamRecord> examRecord = examService.getPassAllExam(employeeNo);
            examRecord.stream() .forEach(ExamRecord -> ExamRecord.setExamStatus("合格"));
            for(ExamRecord examRecord1:examRecord){
                examRecord1.setPaperType(examService.getPaperTypeByid(examRecord1.getExamCategoryId()));
            }
            List<ExamRecord> sortedList = examRecord.stream()
                    .sorted(Comparator.comparing(ExamRecord::getExamStartTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());
            sortedList = sortedList.stream()
                    .sorted(Comparator.comparingLong(ExamRecord::getId).reversed())
                    .collect(Collectors.toList());
            return new ResultVO(1000, "查询成功", sortedList);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }


    @ApiOperation("0106 - 查看已完成的上岗证考试")
    @PostMapping("/exam/getPassExamByArea")
    public ResultVO getPassExamByArea(@RequestParam String employeeNo) {
        try { double b=0;
            List<ExamRecord> examRecord = examService.getPassExamByArea(employeeNo,"");
            Long access_id=examRepository.getId(employeeNo);
            for (ExamRecord examRecord1:examRecord) {
                examRecord1.setTestTime("60");
                examRecord1.setCategoryItem("上岗证考核");
                examRecord1.setPaperType("岗位级");
                if (examRecord1.getTotalScore() == null) {
                    String paperName = examRepository.paperName(examRecord1.getPostInfoId());
                    Integer postSum = examRepository.getPostCountById(access_id, examRecord1.getId(),examRecord1.getPostInfoId());
                    if (postSum == 1) {
                        examRecord1.setExamName("上岗证考核" + paperName);
                        examRecord1.setExamType("正式考试");
                    } else {
                        examRecord1.setExamName("上岗证考核" + paperName + "补考_" + postSum);
                        examRecord1.setExamType("个人发起补考");
                    }
                    examRecord1.setTotalScore(b);
                    examRecord1.setPaperName(examRepository.paperName(examRecord1.getPostInfoId()));
                }
            }
            examRecord.stream() .filter(ExamRecord -> ExamRecord.getTotalScore()>=100).forEach(ExamRecord -> ExamRecord.setExamStatus("合格"));
            examRecord.stream() .filter(ExamRecord -> ExamRecord.getTotalScore()<100).forEach(ExamRecord -> ExamRecord.setExamStatus("不合格"));
            for (ExamRecord examQuestion:examRecord){
                examQuestion.setSingleOptionAnswer(4);
                examQuestion.setMoreOptionAnswer(6);
                examQuestion.setJudgeAnswer(4);
                examQuestion.setFillAnswer(6);
            }
            examRecord =  examRecord.stream()
                    .sorted(Comparator.comparing(ExamRecord::getTotalScore,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());
            examRecord = examRecord.stream()
                    .sorted(Comparator.comparingLong(ExamRecord::getId).reversed())
                    .collect(Collectors.toList());

            return new ResultVO(1000, "查询成功", examRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0107 - 查看已完成的考试详情")
    @PostMapping("/exam/passedExam")
    public ResultVO passedExam(@RequestParam String examNumber) {
        try {
            //获取某个考试的详细信息
            ExamRecord examRecord = examService.passedExam(examNumber);
            return new ResultVO(1000, "查询成功", examRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0108 - 获取上岗证试题")
    @PostMapping("/exam/getPostExamQuestion")
    public ResultVO getPostExamQuestion(@RequestParam String postInfoId) {
        try {
            List<Long> strings = examService.getPostExam1(postInfoId);
            List<ExamQuestion> li = new ArrayList<>();

            PaperQuestionType question = examService.getExamInformation1("上岗证考核");
          if (1==1) {
                if(question.getSingleOptionNumber()==null||question.equals("")){
                    question.setSingleOptionNumber(0);
                }
                if(question.getMoreOptionNumber()==null||question.equals("")){
                    question.setMoreOptionNumber(0);
                }
                if(question.getJudgeNumbers()==null||question.equals("")){
                    question.setJudgeNumbers(0);
                }
                if(question.getFillNumber()==null||question.equals("")){
                    question.setFillNumber(0);
                }
                if(question.getShortNumber()==null||question.equals("")){
                    question.setShortNumber(0);
                }

                li.addAll(examService.examInformation3("单选题", strings, question.getSingleOptionNumber()));
                li.addAll(examService.examInformation3("多选题", strings, question.getMoreOptionNumber()));
                li.addAll(examService.examInformation3("判断题", strings, question.getJudgeNumbers()));
                li.addAll(examService.examInformation3("填空题", strings, question.getFillNumber()));
                li.addAll(examService.examInformation3("简答题", strings, question.getShortNumber()));
                for (int i = 0; i < li.size(); i++) {
                    //sql查询优化
                    int count=0;
                    li.get(i).setQuestionItemEntityList(examService.getPostQuestionContent(li.get(i).getQuestionId()));
                    switch (li.get(i).getQuestionType()){
                        case "填空题":
                            if(li.get(i).getQuestionContent().endsWith("_")){
                                count = li.get(i).getQuestionContent().split("_").length;
                            }else{
                                count = li.get(i).getQuestionContent().split("_").length - 1;
                            }
                            if (count==0){
                                count=li.get(i).getQuestionContent().split("\\(").length-1;
                            }
                            List<QuestionItemEntity> list=new ArrayList<>();
                            if (count==0){
                                count=1;
                            }else {
                                count=1;
                            }
                            for (int j = 0; j < count; j++) {
                                QuestionItemEntity questionItemEntity=new QuestionItemEntity();
                                list.add(questionItemEntity);
                            }
                            li.get(i).setQuestionItemEntityList(list);
                            break;
                    }
                }
            }
            examService.sortExamQuestions(li);
            return new ResultVO(1000, "查询成功", li);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("0103 - 上传答案附件记录")
    @PostMapping("/exam/addAttachmentId")
    public ResultVO addAttachmentId(@RequestBody CollegeTrainAttachment collegeTrainAttachment,@RequestParam String employeeNo,String examName ) {
        try {
            LocalDate currentDate = LocalDate.now();

            // 格式化日期为 "yyyy-MM-dd" 格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = currentDate.format(formatter);
            int sum=examService.getAttachmentIdSum(collegeTrainAttachment,employeeNo,formattedDate);
            Long id;
if (sum!=0){
     id=examService.getAttachmentId(collegeTrainAttachment,employeeNo,formattedDate);
    examService.updateAttachmentById(collegeTrainAttachment,id);
}else {
     id = examService.addAttachmentId(collegeTrainAttachment, employeeNo);
}
            CollegeTrainExamRecordAnswer recordAnswer=new CollegeTrainExamRecordAnswer();
             id=examService.getAttachmentIdByPath(collegeTrainAttachment.getAttachmentPath());

            recordAnswer.setAttachmentId(id);
            CollegeTrainExamRecord exam=new CollegeTrainExamRecord();
            Long access_id = satisfactionDegreeService.getAccessId(employeeNo);
            Date timeToday = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            String currentTime2 = simpleDateFormat.format(timeToday);

            exam.setAssessedId(access_id);
            exam.setExamName(examName+currentTime2);
            exam.setExamType(examService.getExamType(examName));
            exam.setExamCategoryId(examService.getExamCatyId(examName));
            Integer examSum=0;
            examSum=examService.getexamSum(access_id,examName+currentTime2);
            if(examSum==0){
                examService.addExamRecord1(exam,null);
            }
            long examId=examService.getExamId(access_id);
            recordAnswer.setExamId(examId);
            recordAnswer.setQuestionId(collegeTrainAttachment.getQuestionId());
            examService.insertDemo(recordAnswer);
            return new ResultVO(1000, "新增成功");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "新增失败，请联系后台管理人员处理");
        }
    }
    @ApiOperation("0109 - 开始考试")
    @PostMapping("/exam/addExamRecordStart")
    public ResultVO addExamRecordStart(@RequestBody BodyAnswer bodyAnswer) {
        Long access_id = satisfactionDegreeService.getAccessId(bodyAnswer.getEmployeeNo());
        Date timeToday = new Date();
        String postInfoId=bodyAnswer.getPostInfoId();
        String employeeNo=bodyAnswer.getEmployeeNo();
        String categoryItem=bodyAnswer.getCategoryItem();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String currentTime2 = simpleDateFormat.format(timeToday);
        int id=examService.getexamSum(access_id, bodyAnswer.getCategoryItem()+currentTime2);

        Long examId=examService.getExamIdByName(access_id,bodyAnswer.getCategoryItem()+currentTime2);
        PaperQuestionType record=new PaperQuestionType();
        if (categoryItem.equals("上岗证考核")){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = formatter.format(new Date());

        CollegeTrainExamRecord examRecord=new CollegeTrainExamRecord();examRecord.setExamStartTime(currentTime);
        examRecord.setAssessedId(access_id);
        examRecord.setExamName(bodyAnswer.getCategoryItem()+currentTime2);
        examRecord.setExamType(examService.getExamType(bodyAnswer.getCategoryItem()));
        examRecord.setExamCategoryId(examService.getExamCatyId(categoryItem));
        examService.addExamRecord1(examRecord,postInfoId);}
        else {
            CollegeTrainExamRecord exam=new CollegeTrainExamRecord();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = formatter.format(new Date());
             access_id = satisfactionDegreeService.getAccessId(bodyAnswer.getEmployeeNo());
             timeToday = new Date();
             simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
             currentTime2 = simpleDateFormat.format(timeToday);
            String examName=bodyAnswer.getCategoryItem();
            exam.setExamStartTime(currentTime);
            exam.setAssessedId(access_id);
            exam.setExamName(examName+currentTime2);
            exam.setExamType(examService.getExamType(examName));
            exam.setExamCategoryId(examService.getExamCatyId(examName));
            Integer examSum=0;
            examService.addExamRecord1(exam,null);
        }
        return new ResultVO(1000, "新增成功");
    }

    @ApiOperation("0109 - 计算分数，新增考试以及答案记录")
    @PostMapping("/exam/addExamRecord")
    public ResultVO addAnswerRecord(@RequestBody BodyAnswer bodyAnswer) {
        try {
            Long access_id = satisfactionDegreeService.getAccessId(bodyAnswer.getEmployeeNo());
            Date timeToday = new Date();
            String postInfoId=bodyAnswer.getPostInfoId();
            String employeeNo=bodyAnswer.getEmployeeNo();
            String categoryItem=bodyAnswer.getCategoryItem();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = formatter.format(new Date());

            String currentTime2 = simpleDateFormat.format(timeToday);
            int id=examService.getexamSum(access_id, bodyAnswer.getCategoryItem()+currentTime2);

            Long examId=examService.getExamIdByName(access_id,bodyAnswer.getCategoryItem()+currentTime2);
            List<Answer> list=bodyAnswer.getList();
            Double score = 0.0;
            PaperQuestionType record=new PaperQuestionType();
            if (categoryItem.equals("上岗证考核")){
                 record  = examService.getPostExam(bodyAnswer.getEmployeeNo(), bodyAnswer.getPostInfoId());
                 record.setExamStartTime(currentTime);
                CollegeTrainExamRecord examRecord=new CollegeTrainExamRecord();
                examRecord.setAssessedId(access_id);
                examRecord.setExamName(bodyAnswer.getCategoryItem()+currentTime2);
                examRecord.setExamType(examService.getExamType(bodyAnswer.getCategoryItem()));
                examRecord.setExamCategoryId(examService.getExamCatyId(categoryItem));
                //examService.addExamRecord1(examRecord,postInfoId);
            }else {
                 record = examService.getQuestionNumber(bodyAnswer.getEmployeeNo(), categoryItem);
            }
            int sum=0;
            int totalIq = 0;
            Map<String, List<Answer>> map = list.stream().collect(Collectors.groupingBy(Answer::getQuestionType));
            if ("IQ测试".equals(bodyAnswer.getCategoryItem())){
                for (Map.Entry<String, List<Answer>> m : map.entrySet()) {
                    List<Answer> entry = m.getValue();
                    for (int i = 0; i < entry.size(); i++) {
                        List<Answer> li1 = new ArrayList<>();
                        String answer = examService.getAnswerById(entry.get(i).getQuestionId());
                        switch (m.getKey()) {
                            case "单选题":
                                if (answer.equals(entry.get(i).getAnswerText())) {
                                    sum =sum +1;
                                    entry.get(i).setTotalScore(record.getSingleOptionAnswer());
                                } else {
                                    entry.get(i).setTotalScore(0.0);
                                }
                                entry.get(i).setExamId(examId);
                                li1.add(entry.get(i));
                                examService.addExamAnswer(li1);
                                break;
                            case "填空题":
                                if (categoryItem.equals("IQ测试")) {
                                    String[] temp = entry.get(i).getAnswerText().split(",");
                                    String[] temps = (answer.replace(" ", "").replace("，", ",")).split(",");

                                    if (temp.length == temps.length) {
                                        for (int j = 0; j < temp.length; j++) {
                                            if (temps[j].equals(temp[j])) {
                                                totalIq=totalIq+1;
                                            }
                                        }
                                    }
                                }

                                entry.get(i).setExamId(examId);
                                li1.add(entry.get(i));
                                examService.addExamAnswer(li1);
                                break;
                        }
                    }

                }

            }
            else {
                for (Map.Entry<String, List<Answer>> m : map.entrySet()) {
                    List<Answer> entry = m.getValue();
                    for (int i = 0; i < entry.size(); i++) {
                        List<Answer> li1 = new ArrayList<>();
                        String answer = examService.getAnswerById(entry.get(i).getQuestionId());
                        switch (m.getKey()) {
                            case "单选题":
                                if (answer.equals(entry.get(i).getAnswerText())) {
                                    score = score + record.getSingleOptionAnswer();
                                    entry.get(i).setTotalScore(record.getSingleOptionAnswer());
                                } else {
                                    entry.get(i).setTotalScore(0.0);
                                }
                                entry.get(i).setExamId(examId);
                                li1.add(entry.get(i));
                                examService.addExamAnswer(li1);
                                break;
                            case "多选题":
                                String studentAnswer =
                                        Stream.of(entry.get(i).getAnswerText().split(","))
                                                .sorted(Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0))))
                                                .collect(Collectors.joining());
                                answer = answer.replaceAll("\\s*,\\s*", ",");
                                String answerAfter = Stream.of(answer.split(","))
                                        .sorted(Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0))))
                                        .collect(Collectors.joining());

                                if (answerAfter.equals(studentAnswer)) {
                                    score = score + record.getMoreOptionAnswer();
                                    entry.get(i).setTotalScore(record.getMoreOptionAnswer());
                                } else {
                                    entry.get(i).setTotalScore(0.0);
                                }
                                entry.get(i).setExamId(examId);
                                li1.add(entry.get(i));
                                examService.addExamAnswer(li1);
                                break;
                            case "填空题":
                                if (categoryItem.equals("IQ测试")) {
                                    String[] temp = entry.get(i).getAnswerText().split(",");
                                    String[] temps = (answer.replace(" ", "").replace("，", ",")).split(",");
                                    int total = 0;
                                    if (temp.length == temps.length) {
                                        for (int j = 0; j < temp.length; j++) {
                                            if (temps[j].equals(temp[j])) {
                                                total = +1;
                                            }
                                        }
                                    }
                                } else {
                                    String[] temp = entry.get(i).getAnswerText().split(",");
                                    String[] temps = (answer.replace(" ", "").replace("，", ",")).split(",");
                                    int sumtotal = 0;
                                    if (temp.length == 0) {
                                        score = score + 0;
                                    } else if (temp.length < temps.length) {
                                        for (int j = 0; j < temps.length; j++) {
                                            for (int n = 0; n < temp.length; n++) {
                                                if (temps[j].equals(temp[n])) {
                                                    sumtotal++;
                                                }
                                            }
                                        }
                                        score = score + record.getFillAnswer() * sumtotal / temps.length;
                                    } else {
                                        for (int j = 0; j < temps.length; j++) {
                                            if (temps[j].equals(temp[j])) {
                                                sumtotal = 1;
                                                score = score + record.getFillAnswer();
                                            }
                                        }
                                    }
                                    entry.get(i).setTotalScore(record.getFillAnswer() * sumtotal);
                                }

                                entry.get(i).setExamId(examId);
                                li1.add(entry.get(i));
                                examService.addExamAnswer(li1);
                                break;
                            case "简答题":
                                entry.get(i).setExamId(examId);
                                li1.add(entry.get(i));
                                examService.addExamAnswer(li1);
                                break;
                            case "判断题":
                                if (answer.equals(entry.get(i).getAnswerText())) {
                                    score = score + record.getJudgeAnswer();
                                    entry.get(i).setTotalScore(record.getJudgeAnswer());
                                } else {
                                    entry.get(i).setTotalScore(0.0);
                                }
                                entry.get(i).setExamId(examId);
                                li1.add(entry.get(i));
                                examService.addExamAnswer(li1);
                                break;
                        }
                    }
                }
            }
            ExamRecord examRecord=new ExamRecord();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
             currentTime= sdf.format(timeToday);
             formatter =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //record.getExamStartTime()
            Date originalDate = formatter.parse(record.getExamStartTime());
            Calendar newTime = Calendar.getInstance();
            newTime.setTime(originalDate);
            newTime.add(Calendar.MINUTE,Integer.parseInt(record.getTestTime()));//日期加n分
            Random random = new Random();

            // 生成 7 位随机数
            int randomNumber = random.nextInt(9000000) + 1000000;
            Date newDate=newTime.getTime();
            String retval = formatter.format(newDate);
            if (categoryItem.equals("上岗证考核")){
                examRecord=examService.afterAddPostExamLog(employeeNo, bodyAnswer.getPostInfoId());
            }else if (categoryItem.contains("IQ")){
                examRecord= examService.afterAddExamLog(employeeNo,categoryItem);
            }
            else {
            examRecord= examService.afterAddExamLog(employeeNo,categoryItem);
            }
            examRecord.setExamCategoryId(examService.getExamCatyId(categoryItem));
            if (postInfoId==null||postInfoId.equals("")){
                if(bodyAnswer.getCategoryItem().equals("工艺公开课理论考试")||(bodyAnswer.getCategoryItem().equals("工程结业理论考试"))){
                    examRecord.setObjectiveScore(score);
                    examRecord.setSubjectiveScore("0");
                    examRecord.setTotalScore(score);
                }else if ("IQ测试".equals(bodyAnswer.getCategoryItem())){
                    switch (totalIq) {
                        case 20:
                            examRecord.setTotalScore(40.0+15*4);
                            break;
                        case 19:
                            examRecord.setTotalScore(20.0+sum*4);
                            break;
                        default:
                            examRecord.setTotalScore(0.0+sum*4);
                            break;
                    }
                }
                else {
                    examRecord.setObjectiveScore(score);
                    examRecord.setTotalScore(score);
                }
                switch (examRecord.getCategoryItem()) {
                    case"工艺公开课理论考试":
                        examRecord.setExamNumber("GY"+currentTime2+randomNumber);
                        break;
                    case"HR公开课理论考试①":
                        examRecord.setExamNumber("HR"+currentTime2+randomNumber);
                        break;
                    case"HR公开课理论考试②":
                        examRecord.setExamNumber("HR"+currentTime2+randomNumber);
                        break;
                    case"工艺公开课理论考试②":
                        examRecord.setExamNumber("GY"+currentTime2+randomNumber);
                        break;
                    case"BU公共课理论考试②":
                        examRecord.setExamNumber("BU"+currentTime2+randomNumber);
                        break;
                    case"BU公共课理论考试①":
                        examRecord.setExamNumber("BU"+currentTime2+randomNumber);
                        break;
                    case"工程结业理论考试":
                        examRecord.setExamNumber("GC"+currentTime2+randomNumber);
                        break;
                    case"IQ测试":
                        examRecord.setExamNumber("IQ"+currentTime2+randomNumber);
                        break;
                }
                examRecord.setExamType("正式考试");
                examRecord.setExamName(examRecord.getCategoryItem()+currentTime2);
                formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                currentTime = formatter.format(new Date());
                examRecord.setExamEndTime(currentTime);
            examService.changeExamRecord(examRecord,bodyAnswer.getEmployeeNo(),examId );
            }else {
                examId=examService.getPostExamId(access_id,postInfoId);
                examRecord= examService.afterAddPostExamLog(employeeNo,postInfoId);
                String paperName = examRecord.getPaperName();
                examRecord.setObjectiveScore(score);
                examRecord.setTotalScore(score);
                examRecord.setExamCategoryId(Long.valueOf("8"));
                long a=8;
                examRecord.setExamCategoryId(a);
                Integer postSum= examService.getPostCount(access_id,bodyAnswer.getPostInfoId());
                if(postSum==1){
                    examRecord.setExamName("上岗证考核" + paperName);
                    examRecord.setExamType("正式考试");
                }else {
                    examRecord.setExamName("上岗证考核" + paperName+"补考_"+postSum);
                    examRecord.setExamType("个人发起补考");
                }
                examRecord.setExamNumber("SGZ"+currentTime2+randomNumber);
                 formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                 currentTime = formatter.format(new Date());
                examRecord.setExamEndTime(currentTime);
                examService.changeExamRecord(examRecord,bodyAnswer.getEmployeeNo(),examId );
            }
            //计算选择题，判断题，填空题总分后增加考试记录
            return new ResultVO(1000, "成功");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }
}
