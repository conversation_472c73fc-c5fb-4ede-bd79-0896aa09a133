package com.htks.web.weChat;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.examiner.dto.Colleger;
import com.htks.domain.examiner.service.ExaminerService;
import com.htks.domain.student.dto.AppointMent;
import com.htks.domain.student.dto.AppointTime;
import com.htks.domain.student.service.SatisfactionDegreeService;
import com.htks.domain.student.dto.Reservation;
import com.htks.domain.student.dto.SatisfactionDegree;
import com.htks.domain.student.service.ReservationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Api(tags = {"大学生 - 实操答辩预约"})
@RestController
public class ReservationController {

    @Autowired
    private ReservationService reservationService;
    @Autowired
    private ExaminerService examinerService;
    @Autowired
    private SatisfactionDegreeService satisfactionDegreeService;

    @ApiOperation("0101 - 信息自动填入")
    @PostMapping("/appointment/approvalHomepage")
    public ResultVO approvalHomepage(@RequestParam String employeeNo) {
        try {
            Colleger colleger = examinerService.startApproval(employeeNo);
            return new ResultVO(1000, "查询成功", colleger);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "自动插入失败，请联系后台管理员处理");
        }
    }

    @ApiOperation("0102 - 预约界面展示信息")
    @PostMapping("/appointment/showHomepage")
    public ResultVO getMessage(@RequestParam Integer type,String employeeNo,String companyType) {
        try {
            String back = null;
            List<AppointTime> l1 = reservationService.allInformation(employeeNo);
            if(l1.isEmpty()){
back="当前学生没有信息";
            }else {

                int allNumber = reservationService.allNumber(employeeNo,companyType);
                int assessedNumber = reservationService.assessedNumber(type, employeeNo,companyType);
                AppointTime reservation1 = l1.get(0);

                //判断预约类型
                if (type==2) {
                    back = "本轮大学生培训总人数为" + allNumber + "人，已考核" + assessedNumber + "人，未考核" + (allNumber - assessedNumber) + "人，每周可预约名额为35人，异常答辩截止时间为" + reservation1.getEEndTime() + "，请及时提交预约申请。";
                } else {
                    back = "本轮大学生培训总人数为" + allNumber + "人，已考核" + assessedNumber + "人，未考核" + (allNumber - assessedNumber) + "人，每周可预约名额为35人，工程类实操考试截止时间为" + reservation1.getPEndTime() + "，请及时提交预约申请。";
                }
            }
            return new ResultVO(1000, "查询成功", back);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理员处理");
        }
    }

    @ApiOperation("0103 - 预约时间条件判断")
    @PostMapping("/appointment/ConditionalJudgment")
    public ResultVO ConditionalJudgment(@RequestParam Integer type,String employeeNo)  throws ParseException {
        try {
            SatisfactionDegree satisfactionDegree = new SatisfactionDegree();
            String message = null;
            Long access_id = satisfactionDegreeService.getAccessId(employeeNo);

           // reservation.setASSESSED_ID(access_id);
            List<AppointTime> list = reservationService.allInformation(employeeNo);
            AppointTime appnointTime = list.get(0);
            Date datetoday = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String currentTime = sdf.format(datetoday);

            Date timetoday = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime2 = simpleDateFormat.format(timetoday);
            Date start;
            Date end;
            //判断预约日期是否是实实验开始时间的前一周至工程类实操截止时间的前一周内
            //判断是异常答辩还是工程实操
            if (type==1) {
                start = sdf.parse(appnointTime.getPStartTime());
                end = sdf.parse(appnointTime.getPEndTime());
            } else {
                start = sdf.parse(appnointTime.getEStartTime());
                end = sdf.parse(appnointTime.getEEndTime());
            }
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(start);
            rightNow.add(Calendar.DATE, -7);
            Date dt1 = rightNow.getTime();
            String startDay = sdf.format(dt1);
            rightNow.setTime(end);
            rightNow.add(Calendar.DATE, -7);
            Date dt2 = rightNow.getTime();
            String endDay = sdf.format(dt2);
            Calendar cal = Calendar.getInstance();

            //判断当前时间是否是预约时间周四-周五
            cal.set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY);
            cal.set(Calendar.HOUR_OF_DAY, 8); //设置时
            cal.set(Calendar.MINUTE, 30); //设置分
            cal.set(Calendar.SECOND, 00); //设置秒
            cal.set(Calendar.MILLISECOND, 0);
            Date thursday = cal.getTime();
            String t = simpleDateFormat.format(thursday);
            cal.set(Calendar.DAY_OF_WEEK, Calendar.FRIDAY);
            cal.set(Calendar.HOUR_OF_DAY, 12); //设置时
            cal.set(Calendar.MINUTE, 30); //设置分
            cal.set(Calendar.SECOND, 00); //设置秒
            cal.set(Calendar.MILLISECOND, 00);
            Date friday = cal.getTime();
            String f = simpleDateFormat.format(friday);
            //todo
            if (currentTime.compareTo(startDay) >= 0 && currentTime.compareTo(endDay) <= 0 && currentTime2.compareTo(t) >= 0 && currentTime2.compareTo(f) <= 0) {
                int collegeIng = reservationService.isCollegeIng(employeeNo, type);
                if (collegeIng == 1 && type == 1) {
                    message = "您已经预约了工程类实操考试，不可进行重复预约。";
                }else if (collegeIng == 1 && type == 2){
                    message = "您已经预约了异常答辩考试，不可进行重复预约。";
                }
                else {
                    int ispassed = reservationService.judgePassed(employeeNo, type, true);
                    int ispassed2 = reservationService.judgePassed(employeeNo, type,null);
                    if (ispassed == 1 && type == 1) {
                        message = "您的工程类实操考试已通过，无需再进行预约，谢谢";
                    } else if (ispassed2 == 2 && type == 1) {
                        message = "您的工程实操预约次数已满两次，不可在进行预约，谢谢";
                    } else if (ispassed == 1 && type == 2) {
                        message = "您的异常答辩考试已通过，无需再进行预约，谢谢";
                    } else if (ispassed2 == 1 && type == 2) {
                        message = "您已经预约了异常答辩考试，不可进行二次预约，谢谢";
                    } else {
                        message = "0";
                    }
                }
            }else {
                message = "不在预约时间内，请在预约时间内进行操作，谢谢！";
            }
            return new ResultVO(1000, "查询成功", message);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理员处理");
        }
    }

    @ApiOperation("0104 - 预约名额判断")
    @PostMapping("/appointment/checkAppointment")
    public ResultVO checkYuyue(Integer type,String startDate, String endDate,String employeeNo) throws ParseException {
        try {
            int message = 0;
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            // 获取当前周的某一时刻
            cal.setTime(new Date());
            cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            cal.add(Calendar.DATE, 7);
            cal.set(Calendar.HOUR_OF_DAY, 00); //设置时
            cal.set(Calendar.MINUTE, 00); //设置分
            cal.set(Calendar.SECOND, 00); //设置秒
            cal.set(Calendar.MILLISECOND, 00);
            Date monday = cal.getTime();
            String monDay = simpleDateFormat.format(monday);
            cal.setTime(new Date());
            cal.set(Calendar.DAY_OF_WEEK, Calendar.FRIDAY);
            cal.add(Calendar.DATE, 7);
            cal.set(Calendar.HOUR_OF_DAY, 23); //设置时
            cal.set(Calendar.MINUTE, 59); //设置分
            cal.set(Calendar.SECOND, 59); //设置秒
            cal.set(Calendar.MILLISECOND, 99);
            Date friday = cal.getTime();
            String friDay = simpleDateFormat.format(friday);
            List<Integer> list = new ArrayList<Integer>();
            //获取本周已经预约的人数
            int all = reservationService.timeAppoitGet(monDay, friDay, type, "00:00:00", "23:59:59", employeeNo);
            //获取上午下午每个时间段预约人数
            int morning = reservationService.timeAppoitGet(startDate, endDate, type, "00:00:00", "23:59:59", employeeNo);
            int afternoon = reservationService.timeAppoitGet(startDate, endDate, type, "00:00:00", "23:59:59", employeeNo);
            list.add((35 - all));
            list.add((4 - morning));
            list.add((4 - afternoon));
            Map<String,Integer> map=new HashMap<>();
            if (all>35){
                all=35;
            }
            map.put("总数",(35-all));
            map.put("上午",(4 - morning));
            map.put("下午",(4 - afternoon));
            return new ResultVO(1000, "查询成功", map);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理员处理");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("0105 - 新增预约")
    @PostMapping("/appointment/addAppointment")
    public ResultVO addAppointment(@RequestBody AppointMent reservation) {
        try {
            Long assessed_ID = satisfactionDegreeService.getAccessId(reservation.getEmployeeNo());
                reservation.setAStartTime("00:00:00");
                reservation.setAEndTime("23:59:59");
            reservationService.addAppointment(reservation,assessed_ID);
            return new ResultVO(1000, "新增成功");
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001, "新增失败，请联系后台管理员处理");
        }
    }

    @ApiOperation("0106 - 我的预约")
    @PostMapping("/appointment/showMyAppointment")
    public ResultVO showAppointment(@RequestParam Integer type,String employeeNo) {
        try {
            List<Reservation> list = reservationService.showAppointment(employeeNo, type);
            return new ResultVO(1000, "查询成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理员处理");
        }
    }
}
