package com.htks.web.weChat;

import com.htks.common.config.WeChatConfig;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.WeChatUtils;
import com.htks.domain.ExamInformation.service.impl.SatisfactionCountServiceImpl;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.domain.examiner.dto.*;
import com.htks.domain.examiner.repository.hana.ExaminerRepository;
import com.htks.domain.examiner.service.ExaminerService;
import com.htks.domain.examiner.service.impl.ExaminerServiceImpl;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpMessageServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;


//@Slf4j
@Api(tags = {"阅卷老师 - 实操评价"})
@RestController
public class ExaminerController {


    /**
     * 阅卷师企业微信端
     *
     * <AUTHOR>
     * @date 2023/5/10.
     */


    @Autowired
    private ExaminerService examinerService;
    @Autowired
    private WeChatConfig weChatConfig;
    @Autowired
    private ExaminerServiceImpl examinerServiceimpl;
    @Autowired
    SatisfactionCountServiceImpl Saservice;
    @Resource
    private CollegeTrainAssessedService service;
    @Resource
    private ExaminerRepository examinerRepository;
    @ApiOperation("0101 - 信息自动填入")
    @PostMapping("/exam/approvalHomepage")
    public ResultVO approvalHomepage(@RequestParam String employeeNo) {
        try {
            Colleger colleger = examinerService.startApproval(employeeNo);
            return new ResultVO(1000, "查询成功", colleger);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "自动插入失败，请联系后台管理员处理");
        }
    }


    @ApiOperation("0102 - 录入成绩")
    @PostMapping("/exam/approvalHandsOn")
    @Transactional(rollbackFor = Exception.class)
    public ResultVO approvalComplete(Examiner examiner) {
        try {
            //id:学生工号   no：阅卷师工号
            //String name = examinerService.collegeName(examiner.getStudentID());
          //  String message = examinerServiceimpl.checkAll(examiner.getEmployeeNo(), examiner.getType(), examiner.getEvaluatorNo());
           String message="0";
            if (message.equals("0")) {

                Long AppointRecordId = (examinerService.sureAppointRecordId(examiner.getEmployeeNo(), examiner.getType()));

                String EVALUATOR_NAME = examinerService.collegeName(examiner.getEvaluatorNo());
                int b= examinerService.approvalComplete(examiner, AppointRecordId, EVALUATOR_NAME,examiner.getEVALUATOR_TYPE());
                int a = examinerService.checkApprovalThird(examiner.getEmployeeNo(), examiner.getType());

                double source = 0;
                AppointGoal appointGoal = new AppointGoal();
                if (a == 3) {
                    List<String> l1 = examinerService.checkApprovalFirst(examiner.getEvaluatorNo(),examiner.getEmployeeNo(), examiner.getType());
                    if (examiner.getType() == 1) {
                        for (int i = 0; i < l1.size(); i++) {
                            String department1 = examinerService.getCheckApprovalNo(l1.get(i),examiner.getEmployeeNo(), examiner.getType());
                            double decimal = examinerService.calculateEachGrades(l1.get(i), examiner.getType(),examiner.getEmployeeNo());
                            if (department1.equals("1") || department1.equals("2")) {
                                source += decimal * 0.4;
                            } else {
                                source += decimal * 0.2 * 2.5;
                            }
                        }
                    } else {
                        for (int i = 0; i < l1.size(); i++) {
                            double decimal = examinerService.calculateEachGrades(l1.get(i), examiner.getType(),examiner.getEmployeeNo());
                            source += decimal;
                        }
                        source = source / 3;
                    }
                    DecimalFormat df = new DecimalFormat("#0.00");

                    String formattedResult = df.format(source);

                    source = Double.parseDouble(formattedResult);
                    appointGoal.setStatus("已考核");
                    BigDecimal source1 = new BigDecimal(source);
                    double source2 = source1.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    examiner.setScore(source2);
                    appointGoal.setScore(source);
                    appointGoal.setEmployeeNo(examiner.getEmployeeNo());
                    if (examiner.getType()==1) {
                        if (source >= 80.00) {
                            appointGoal.setPassed("true");
                            Calendar currentDate = Calendar.getInstance();
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            String currentTime = sdf.format(currentDate.getTime());
                            appointGoal.setPassedDate(currentTime);
                            CollegeTrainAssessed collegeTrainAssessed = service.getByEmployeeNo(examiner.getEmployeeNo());
                            appointGoal.setAdditionalPoints(String.valueOf(getAdditionalPoints(currentTime, collegeTrainAssessed.getPracticalOperationEndTime())));

                        } else {
                            int sum = examinerService.getSum(examiner.getEmployeeNo(), examiner.getType());
                            if (sum == 1) {
                                final WxCpService wxCpService = weChatConfig.getWxCpService();
                                final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
                                // 开启微信消息服务
                                final WxCpMessage wxCpMessage = new WxCpMessage();
                                WeChatUtils weChatUtils = new WeChatUtils();
                                EmployeeEntity employeeEntity = Saservice.getWechatId(examiner.getEmployeeNo());
                                weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                                        "您好" + employeeEntity.getEmployeeName() + "您本次工程实操考试得分为" + source + "未通过，请尽快在企业微信端再次进行工程实操预约，请知悉！"
                                );
                            }
                            appointGoal.setPassed("false");
                        }
                    }
                    else {
                        appointGoal.setPassed("true");
                        Calendar currentDate = Calendar.getInstance();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        String currentTime = sdf.format(currentDate.getTime());
                        appointGoal.setPassedDate(currentTime);
                        CollegeTrainAssessed collegeTrainAssessed = service.getByEmployeeNo(examiner.getEmployeeNo());
                        appointGoal.setAdditionalPoints(String.valueOf(getAdditionalPoints(currentTime, collegeTrainAssessed.getExceptionDefenseEndTime())));
                    }
                } else {
                    appointGoal.setScore(null);
                    appointGoal.setStatus("未考核");
                }

                examinerService.updateAPPOINT(appointGoal,examiner.getType());
            message = "录入成功";
        }
            return new ResultVO(1000, "成功", message);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "");
        }
    }



    @ApiOperation("0103 - 开始评价检测")
    @PostMapping("/exam/checkApproval")
    public ResultVO checkApprovalFirst(@RequestParam String employeeNo, Integer type, String evaluatorNo,String evaluatorType) {
        //id:学员工号 no：阅卷师
        try {
            String message = examinerServiceimpl.checkAll(employeeNo, type, evaluatorNo,evaluatorType);
            return new ResultVO(1000, "成功", message);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "录入失败");
        }
    }

    @ApiOperation("0104 -  获取  1:异常答辩评分标准 2：满意度评分表")
    @PostMapping("/exam/getSatisfactionOrAbnormalDefenseCriteria")
    public ResultVO getSatisfactionOrAbnormalDefenseCriteria(@RequestParam Integer type) {
        //id:学员工号 no：阅卷师
        try {
            List<SatisfactionOrAbnormalDefenseCriteria> message = examinerService.getSatisfactionOrAbnormalDefenseCriteria(type);
            return new ResultVO(1000, "成功", message);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "录入失败");
        }
    }

    @ApiOperation("0105 -  获取  1:SOP评分标准 2：异常实操评分")
    @PostMapping("/exam/getSopOrHandsOnCriteria")
    public ResultVO getSopOrHandsOnCriteria(@RequestParam Integer type) {
        //id:学员工号 no：阅卷师
        try {
            List<SopOrHandsOnCriteria> message = examinerService.getSopOrHandsOnCriteria(type);
            return new ResultVO(1000, "成功", message);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "录入失败");
        }
    }
/*    @ApiOperation("0106 - 检验产品工程部")
    @PostMapping("/exam/checkNpi")
    @Transactional(rollbackFor = Exception.class)
    public ResultVO checkNpi(String evaluatorNo) {
        try {
            String department1 = examinerService.checkApprovalSecond(evaluatorNo);
            if ("产品工程部".equals(department1)) {
                return new ResultVO(1000, "成功", "1");
            } else {
                return new ResultVO(1000, "成功", "0");
            }
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001, "失败");
        }
    }*/


    @ApiOperation("getAdditionalPoints - getAdditionalPoints")
    @PostMapping("/exam/getAdditionalPoints")
    @Transactional(rollbackFor = Exception.class)
    public double getAdditionalPoints(String examDateStr, String deadlineDateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate examDate = LocalDate.parse(examDateStr, formatter);
        LocalDate deadlineDate = LocalDate.parse(deadlineDateStr, formatter);

        int examWeek = examDate.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear());
        int deadlineWeek = deadlineDate.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear());

        int weekDifference = deadlineWeek - examWeek;

        double score;

        if (weekDifference==0) {
            score = -1;
        } else if (weekDifference == 1) {
            score = 0;
        } else if (weekDifference == 2) {
            score = 0.5;
        } else if (weekDifference == 3) {
            score = 1;
        } else if (weekDifference >= 4) {
            score = 1.5;
        } else {
            score = 0;
        }
        return score;
    }
}