package com.htks.web.weChat;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.course.dto.Attachment;
import com.htks.domain.student.dto.CourseStudy;
import com.htks.domain.student.service.CourseStudyService;
import com.htks.domain.student.service.SatisfactionDegreeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = {"大学生 - 课程学习"})
@RestController
public class CourseStudyController {
    @Autowired
    CourseStudyService courseStudyService;
    @Autowired
    private SatisfactionDegreeService satisfactionDegreeService;


    @ApiOperation("0101 - 获取公开课课程")
    @PostMapping("/CourseStudy/getCraftCourse")
    public ResultVO getCraftCourse(@RequestParam String category,String employeeNo) {
        try {
            Long id = courseStudyService.getAccessId(employeeNo);
            String department=courseStudyService.getPracticeDepartment(employeeNo);
            List<CourseStudy> list = courseStudyService.getCourseDetail(id, category,department);
            List<Attachment> list1 = courseStudyService.getVideoUrlList();
            for (int i = 0; i < list.size(); i++) {
//                list.get(i).setMaxScore(courseStudyService.getExamScore(id,list.get(i).getTitle()));
                CourseStudy courseStudy = list.get(i);
                list.get(i).setCategory(category);
                list.get(i).setCreatedTime(courseStudy.getCreatedTime().substring(0,19));
                if (category.contains("HR")){
                    List<Attachment> attachments = list1.stream().filter(e->e.getId()==courseStudy.getCourseId()).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(attachments)){
                        list.get(i).setVideoUrl(attachments.get(0).getAttachmentPath());
                    }
                }
            }

/*          list.stream() .filter(CourseStudy -> CourseStudy.getMaxScore()==null)  .forEach(CourseStudy -> CourseStudy.setMaxScore(0));
            list.stream() .filter(CourseStudy -> CourseStudy.getMaxScore()<80)  .forEach(CourseStudy -> CourseStudy.setExamProgress("未通过"));
            list.stream() .filter(CourseStudy -> CourseStudy.getMaxScore()>80)  .forEach(CourseStudy -> CourseStudy.setExamProgress("通过"));*/
            return new ResultVO(1000,"查询成功",list);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001,"查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0102 - 更改学习状态")
    @PostMapping("/CourseStudy/changeStudyStatus")
    public void changeStudyStatus(@RequestParam String studyStatus,Long courseId,String employeeNo) {
        Date today = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String currentTime = sdf.format(today);
        long accessId= satisfactionDegreeService.getAccessId(employeeNo);
        courseStudyService.changeStudyStatus(studyStatus,currentTime,accessId,courseId);
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("0103 - 增加学习记录")
    @PostMapping("/CourseStudy/addStudyLog")
    public void addStudyLog(@RequestParam Long courseId,String employeeNo) {
        long accessId= satisfactionDegreeService.getAccessId(employeeNo);
        long id =courseStudyService.getCourseItemId(accessId,courseId);
        courseStudyService.addStudyLog(id);
        //changeStudyStatus("学习中",courseId,employeeNo);
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("0104 - ppt转图片")
    @PostMapping("/CourseStudy/pdfToPicture")
    public ResultVO pdfToPicture(@RequestParam String pptFilePath) {

            try {
              List<String> imageByteArrays= courseStudyService.pdfToPicture(pptFilePath);

                // 从字节数组输出流中获取字节数组
                return new ResultVO(1000,"查询成功",imageByteArrays);
            } catch (IOException e) {
                e.printStackTrace();
                return new ResultVO(1001,"查询失败");
            }

        }


/*
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("0104 - ppt转图片")
    @PostMapping("/CourseStudy/pdfToPicture1")
            public ResultVO base64ToImage(String base64Str, String outputFilePath) throws IOException {
        byte[] imageBytes = DatatypeConverter.parseBase64Binary(base64Str);
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
        File outputFile = new File(outputFilePath);
        ImageIO.write(image, "jpg", outputFile);
        return new ResultVO(1000,"查询成功",outputFile);
            }

*/


}
