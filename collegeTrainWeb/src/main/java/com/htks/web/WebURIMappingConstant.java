package com.htks.web;

/**
 * URI 配置管理
 *
 * <AUTHOR>
 * @date 2012/09/07
 */
public final class WebURIMappingConstant {

    private WebURIMappingConstant() {
    }

    //secured
    private static final String SECURITY_ROOT = "/secured/";

    private static final String PERFORMANCE = "performance/";

    private static final String CULTIVATE = "cultivate/";

    private static final String EVALUATE_SYSTEM = "evaluateSystem/";

    private static final String COURSE_INVENTORY = "courseInventory/";

    private static final String EXAM_PAPER = "examPaper/";

    private static final String APPRAISER = "appraiser/";

    private static final String USER = "user/";

    private static final String STUDENT = "student/";

    private static final String EXAM_SCHEDULE = "examSchedule/";

    //登录
    public static final String URI_LOGIN = "/login";

    //退出
    public static final String URI_LOGOUT = "/logout";

    //查询员工信息
    public static final String URI_SYS_GET_EMPLOYEE_INFO = SECURITY_ROOT + "getEmployeeInfo";

    /**
     * 公共
     */
    public static final String URI_COMMON_DOWNLOAD = "/file/download";
    public static final String URI_COMMON_FILE_UPLOAD = "/fileUpload";
    public static final String URI_COMMON_GET_PARAMETER_DEPT_DROPDOWN = "/getParameterDropDown";
    public static final String URI_COMMON_GET_EMPLOYEE_BASE_INFO = SECURITY_ROOT + "/getEmployeeBaseInfo";
    public static final String URI_COMMON_GET_COMMON_ROLE_INFO = SECURITY_ROOT + "/getCommonRoleInfo";

    /**
     * 企业微信
     */
    private static final String URI_QW = "/qw/";
    public static final String URI_QW_GET_WECHAT_USER_INFO = "/getWechatUserInfo";
    public static final String URI_QW_GET_APPRAISEE_INFO = URI_QW + "00/getAppraiseeInfo";
    public static final String URI_QW_GET_TUTOR_INFO = URI_QW + "00/getTutorInfo";
    public static final String URI_QW_GET_USER_JUDGE_INFO = URI_QW + "00/getUserJudgeInfo";
    public static final String URI_QW_SAVE_USER_ASSESSED_SCORE_DETAIL = URI_QW + "01/saveUserAssessedScoreDetail";
    public static final String URI_QW_SAVE_TUTOR_SCORE_DETAIL = URI_QW + "03/saveTutorScoreDetail";
    public static final String URI_QW_SAVE_LECTURER_SCORE_DETAIL = URI_QW + "04/saveLecturerScoreDetail";


    /**
     * 培训组--登录人管理
     */
    public static final String URI_USER_SEARCH_ADMIN = SECURITY_ROOT + USER + "searchAdmin";//列表查询
    public static final String URI_USER_DELETE_ADMIN = SECURITY_ROOT + USER + "deleteAdmin";//删除
    public static final String URI_USER_SAVE_ADMIN = SECURITY_ROOT + USER + "saveAdmin";//保存

    /**
     * 试题维护
     */
    private static final String URI_QUESTION = "/question/";
    public static final String URI_QUESTION_LIST = SECURITY_ROOT + URI_QUESTION + "list";//列表查询
    public static final String URI_QUESTION_SAVE = SECURITY_ROOT + URI_QUESTION + "save";//保存
    public static final String URI_QUESTION_IMPORT = SECURITY_ROOT + URI_QUESTION + "import";//解析试卷excel
    public static final String URI_QUESTION_DEPARTMENT_LIST = SECURITY_ROOT + URI_QUESTION + "departmentList";//下拉部门信息
    public static final String URI_QUESTION_CATEGORY = SECURITY_ROOT + URI_QUESTION + "categoryList";//下拉考试项目
    public static final String URI_QUESTION_DELETE = SECURITY_ROOT + URI_QUESTION + "deletePaper";//删除试卷
    public static final String URI_QUESTION_DETAIL = SECURITY_ROOT + URI_QUESTION + "getQuestionDetail";//试题详情
    public static final String URI_QUESTION_UPDATE = SECURITY_ROOT + URI_QUESTION + "updateQuestion";//试题详情

    /**
     * 课程维护
     */
    private static final String URI_COURSE = "/course/";
    public static final String URI_COURSE_LIST = SECURITY_ROOT + URI_COURSE + "list";//列表查询
    public static final String URI_COURSE_SAVE = SECURITY_ROOT + URI_COURSE + "save";//保存
    public static final String URI_COURSE_DELETE = SECURITY_ROOT + URI_COURSE + "deleteCourse";//删除

    /**
     * 抛送课程&考试
     */
    private static final String URI_PUSH_COURSE = "/pushCourse/";
    public static final String URI_PUSH_COURSE_LIST = SECURITY_ROOT + URI_PUSH_COURSE + "list";//列表查询
    public static final String URI_PUSH_COURSE_SEND = SECURITY_ROOT + URI_PUSH_COURSE + "sendCourse";//列表查询

    private static final String URI_PUSH_EXAM = "/pushExam/";
    public static final String URI_PUSH_EXAM_LIST = SECURITY_ROOT + URI_PUSH_EXAM + "list";//列表查询
    public static final String URI_PUSH_EXAM_SEND = SECURITY_ROOT + URI_PUSH_EXAM + "sendExam";//列表查询


}
