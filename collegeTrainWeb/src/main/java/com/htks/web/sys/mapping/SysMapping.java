package com.htks.web.sys.mapping;


import com.htks.domain.sys.dto.SysEmployeeEntity;
import com.htks.domain.sys.dto.SysTokenEntity;
import com.htks.web.sys.vo.EmployeeVO;
import com.htks.web.sys.vo.TokenVO;
import org.mapstruct.Mapper;

/**
 * MapStruct 转换 Mapper
 * <p>
 * 注：源类型与目标类型的成员变量相同类型相同变量名不用写映射
 *
 * <AUTHOR>
 * @date 2022/03/31.
 */

@Mapper(componentModel = "spring")
public interface SysMapping {

  /**
   * Token映射
   *
   * @param dto dto
   * @return vo
   */
  TokenVO tokenDtoToVo(SysTokenEntity dto);

  /**
   * 员工映射
   *
   * @param dto dto
   * @return vo
   */
  EmployeeVO employeeDtoToVo(SysEmployeeEntity dto);

}
