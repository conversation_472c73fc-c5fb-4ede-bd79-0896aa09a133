package com.htks.web.sys.vo;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.web.AbstractVO;
import lombok.Getter;
import lombok.Setter;

/**
 * 资源信息(菜单)
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Getter
@Setter
public final class ResourceVO extends AbstractVO {

  private static final long serialVersionUID = -8003803382736191052L;

  //资源ID
  private String resourceId;

  //资源名称
  private String resourceName;

  //资源编码
  private String resourceCode;

  //资源URL
  private String resourceUrl;

  //资源标签
  private String resourceFlag;

  //资源类型ID
  private Long resourceTypeId;

  //资源类型名称
  private String resourceTypeName;

  //父资源ID
  private Long parentResourceId;

  //资源描述
  private String resourceDescription;

  //是否冻结
  private Boolean freeze;

  //小图标样式
  private String iconClass;

  //大图标样式
  private String bigIconClass;

  //排序
  private Integer listOrder;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("resourceId", resourceId).
        add("resourceName", resourceName).
        add("resourceCode", resourceCode).
        add("resourceUrl", resourceUrl).
        add("resourceFlag", resourceFlag).
        add("resourceTypeId", resourceTypeId).
        add("resourceTypeName", resourceTypeName).
        add("parentResourceId", parentResourceId).
        add("resourceDescription", resourceDescription).
        add("freeze", freeze).
        add("iconClass", iconClass).
        add("bigIconClass", bigIconClass).
        add("listOrder", listOrder).
        toString();
  }
}
