package com.htks.web.sys.vo;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.web.AbstractVO;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色信息
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Getter
@Setter
public final class RoleVO extends AbstractVO {

  private static final long serialVersionUID = 3506234644342037492L;

  //角色ID
  private Long roleId;

  //角色名称
  private String roleName;

  //角色描述
  private String roleDescription;

  //资源(菜单)集合
  private List<ResourceVO> menuList;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("roleId", roleId).
        add("roleName", roleName).
        add("roleDescription", roleDescription).
        add("menuList", menuList).
        toString();
  }
}
