package com.htks.web.sys.vo;


import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.web.AbstractVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * TokenVO
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Getter
@Setter
@ApiModel("TokenVO")
public final class TokenVO extends AbstractVO {

  private static final long serialVersionUID = 4301759440214220016L;

  /**
   * 姓名
   */
  private String employeeName;

  /**
   * 性别
   */
  private String gender;

  /**
   * 员工工号
   */
  private String employeeNumber;

  /**
   * 角色
   */
  private String roleIds;

  /**
   * Token值
   */
  @ApiModelProperty(value = "Token值")
  private String token;

  /**
   * 过期时间
   */
  @ApiModelProperty(value = "过期时间")
  private String expire;

  @Override
  public String toString() {
    return toStringHelper(this)
        .add("employeeName", employeeName)
        .add("token", token)
        .add("expire", expire)
        .toString();
  }
}
