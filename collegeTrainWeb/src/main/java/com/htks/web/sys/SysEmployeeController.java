package com.htks.web.sys;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.sys.dto.SysEmployeeEntity;
import com.htks.domain.sys.service.SysEmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/9 15:46
 */
@Slf4j
@Api(tags = {"员工"})
@RestController
public class SysEmployeeController {

    @Resource
    private CommonEmployeeService commonEmployeeService;

    @ApiOperation(value = "通过工号获取员工信息", notes = "通过工号获取员工信息")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CommonEmployee.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("getEmployeeInfo/{employeeNumber}")
    public ResultVO getEmployeeInfo(@PathVariable("employeeNumber")String employeeNumber) {
        CommonEmployee commonEmployee = commonEmployeeService.getEmployeeInfo(employeeNumber);
        return ResultVO.success(commonEmployee);
    }

    @ApiOperation(value = "通过部门名称获取员工列表", notes = "通过部门名称获取员工列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CommonEmployee.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("getEmployeeListByDepartmentName")
    public ResultVO getEmployeeListByDepartmentName(@RequestParam("departmentName")String departmentName) {
        List<CommonEmployee> commonEmployeeList = commonEmployeeService.getEmployeeListByDepartmentName(departmentName);
        return ResultVO.success(commonEmployeeList);
    }



}
