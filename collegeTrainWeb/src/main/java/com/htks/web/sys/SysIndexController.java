package com.htks.web.sys;

import static com.htks.web.WebURIMappingConstant.URI_SYS_GET_EMPLOYEE_INFO;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainUser;
import com.htks.domain.school.enums.UserTypeEnum;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainUserService;
import com.htks.domain.sys.service.SysEmployeeService;
import com.htks.domain.school.vo.CollegeUserVo;
import com.htks.web.sys.mapping.SysMapping;
import com.htks.web.sys.vo.EmployeeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 首页Controller
 *
 * <AUTHOR>
 * @date 2022/03/31.
 */
@Slf4j
@Api(tags = {"首页"})
@RestController
public class SysIndexController {

    private final SysMapping sysMapping;

    private final SysEmployeeService sysEmployeeService;

    public SysIndexController(SysMapping sysMapping, SysEmployeeService sysEmployeeService) {
        this.sysMapping = sysMapping;
        this.sysEmployeeService = sysEmployeeService;
    }

    @Resource
    private CollegeTrainUserService collegeTrainUserService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;

    /**
     * 查询员工信息
     */
    @ApiOperation(value = "查询员工信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "employeeNumber", value = "工号", required = true, dataType = "string", example = "30203"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "1：学员登录 2：阅卷师登录 3：培训组长登录 4：管理员登录", required = true, dataType = "int")
    })

    @ApiResponses({@ApiResponse(code = 200, message = "操作成功", response = EmployeeVO.class),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping(value = URI_SYS_GET_EMPLOYEE_INFO)
    public ResultVO getEmployeeInfo(@RequestParam("employeeNumber") String employeeNumber, @RequestParam("type") int type) {
        if (log.isDebugEnabled()) {
            log.debug("查询员工{}信息", employeeNumber);
        }
        CollegeUserVo collegeUserVo = null;
        if (!UserTypeEnum.STUDENT.getCode().equals(type)) {
            CollegeTrainUser collegeTrainUser = collegeTrainUserService.getByEmployeeNo(employeeNumber);
            collegeUserVo = collegeTrainUser.convert(CollegeUserVo.class);
        } else {
            CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(employeeNumber);
            collegeUserVo = collegeTrainAssessed.convert(CollegeUserVo.class);
        }
        return new ResultVO(collegeUserVo);
    }

}
