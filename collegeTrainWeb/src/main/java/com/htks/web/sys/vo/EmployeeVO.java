package com.htks.web.sys.vo;

import static com.google.common.base.MoreObjects.toStringHelper;

import com.htks.web.AbstractVO;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 员工信息
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Getter
@Setter
public final class EmployeeVO extends AbstractVO {

  private static final long serialVersionUID = -7785629886438455228L;

  //姓名
  private String employeeName;

  //工号
  private String employeeNumber;

  //部门
  private String groupName;

  //部门ID
  private String groupId;

  //职务
  private String jobName;

  //职务ID
  private String jobId;

  //职位
  private String positionName;

  //职位ID
  private String positionId;

  //角色集合
  private List<RoleVO> roleList;

  @Override
  public String toString() {
    return toStringHelper(this).
        add("employeeName", employeeName).
        add("employeeNumber", employeeNumber).
        add("roleList", roleList).
        toString();
  }
}
