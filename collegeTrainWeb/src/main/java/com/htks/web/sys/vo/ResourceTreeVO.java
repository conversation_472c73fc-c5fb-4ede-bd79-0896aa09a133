package com.htks.web.sys.vo;

import com.htks.web.AbstractVO;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 资源菜单树VO
 *
 * <AUTHOR>
 * @date 2022/6/24.
 */
@Getter
@Setter
public final class ResourceTreeVO extends AbstractVO {

  private static final long serialVersionUID = 7567576371511690423L;

  //资源名称
  private Long id;


  //资源名称
  private String resourceName;

  private String resourceCode;

  //资源URI
  private String resourceUri;

  //图标名称
  private String iconClass;

  /**
   * 父资源ID
   */
  private Long parentId;

  //排序
  private Integer listOrder;

  @ApiModelProperty(value = "（有权限）按钮id集合")
  private List<String> buttonIdList;

  private List<ResourceTreeVO> children;
}
