package com.htks.web.sys;


import static com.alibaba.fastjson.JSON.toJSONString;
import static com.htks.common.SystemConfig.DATE_FORMAT_DEFAULT;
import static com.htks.common.SystemConfig.ResponseStatusEnum.DEFAULT;
import static com.htks.common.SystemConfig.ResponseStatusEnum.LOGIN_FAILURE;
import static com.htks.common.SystemConfig.SHIRO_SESSION_KEY;
import static com.htks.web.WebURIMappingConstant.URI_LOGIN;
import static com.htks.web.WebURIMappingConstant.URI_LOGOUT;
import static org.apache.shiro.SecurityUtils.getSubject;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.DomainUtil;
import com.htks.common.utils.HttpUtils;
import com.htks.common.utils.IPUtils;
import com.htks.common.utils.ShiroUtils;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.domain.common.service.CommonService;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainUser;
import com.htks.domain.school.enums.UserTypeEnum;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainUserService;
import com.htks.domain.student.repository.hana.CourseStudyRepository;
import com.htks.domain.sys.dto.SysRoleEntity;
import com.htks.domain.sys.dto.SysTokenEntity;
import com.htks.domain.sys.service.SysEmployeeService;
import com.htks.domain.sys.service.SysTokenService;
import com.htks.domain.sys.shiro.UserAuthenticationToken;
import com.htks.web.Rest;
import com.htks.web.RestBody;
import com.htks.web.sys.mapping.SysMapping;
import com.htks.web.sys.vo.TokenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import javax.naming.AuthenticationException;
import javax.naming.CommunicationException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .页面Controller
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Slf4j
@Api(tags = {"登录"}, hidden = true)
@RestController
public class SysLoginController {

    @Resource
    private CourseStudyRepository courseStudyRepository;
    //正则 五位数字
    private static Pattern NUMBER_PATTERN = Pattern.compile("^\\d{5}$");

    //是否启用踢人
    @Value("${defineProps.kick-out-other}")
    private Boolean kickOutOther;

    private final SysTokenService sysTokenService;

    private final SysEmployeeService sysEmployeeService;

    private final CommonService commonService;

    private final SysMapping sysMapping;

    private final HttpServletRequest request;

    private final String validateUrl = "http://10.160.1.196:7666/HRMApiService/external/ldap/validate";

    public SysLoginController(SysTokenService sysTokenService, SysMapping sysMapping, HttpServletRequest request, SysEmployeeService sysEmployeeService,
                              CommonService commonService) {
        this.sysTokenService = sysTokenService;
        this.sysMapping = sysMapping;
        this.request = request;
        this.sysEmployeeService = sysEmployeeService;
        this.commonService = commonService;
    }

    @Resource
    private CollegeTrainUserService collegeTrainUserService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;

    /**
     * 登录
     */
    @ApiOperation(value = "登录", notes = "登录", produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "loginName", value = "工号", required = true, dataType = "string", example = "30343"),
          //  @ApiImplicitParam(paramType = "query", name = "loginPassword", value = "密码", required = true, dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "1：学员登录 2：阅卷师登录 3：培训组长登录 4：管理员登录", required = true, dataType = "int")})
    @ApiResponses({@ApiResponse(code = 1000, message = "操作成功", response = TokenVO.class),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping(value = URI_LOGIN)
    public ResultVO<TokenVO> login(@RequestParam("loginName") String loginName,
                                   //@RequestParam("loginPassword") String loginPassword,
                                   @RequestParam("type") int type) {

        final String adName = generateAdName(loginName);

        Map<String, Object> map = new HashMap<>();
        map.put("userName",loginName);
        //map.put("password",loginPassword);
        //String result = HttpUtils.sendPostRequest(validateUrl, toJSONString(map));
        //JSONObject object = JSONObject.parseObject(result);
        //if(object.getString("code").equals("200")){
        //    log.info(result);
      /*  }else{
            return new ResultVO(ResultCode.ERROR.getCode(), object.getString("message"));
        }*/

        //获取五位工号(通过AD域校验的都是ks0开头的)
        String employeeNumber = loginName.replaceAll("300", "");
        employeeNumber = employeeNumber.replaceAll("330", "");
        employeeNumber = employeeNumber.replaceAll("660", "");
        employeeNumber = employeeNumber.replaceAll("js0", "");
        String employeeName =courseStudyRepository.getEmployeeName(employeeNumber) ;

        boolean exist = true;
        //如果是学员登录,需要校验是否在学员库里
        if (UserTypeEnum.STUDENT.getCode().equals(type)) {
            exist = collegeTrainAssessedService.checkExist(employeeNumber);
        } else {
            exist=false;
         String roleId =  courseStudyRepository.getRoleId(employeeNumber);
         if (roleId==null){
             return new ResultVO(ResultCode.FAILED, LOGIN_FAILURE.getStatusValue() + ":您的工号不属于所选登录类型");
         }
         if (type==2){
             if (roleId.contains("101")){
                 exist=true;
             }
         }
            if (type==3){
                if (roleId.contains("102")){
                    exist=true;
                }
            }
            if (type==4){
                if (roleId.contains("100")){
                    exist=true;
                }
            }
        }
        if (!exist) {
            return new ResultVO(ResultCode.FAILED, LOGIN_FAILURE.getStatusValue() + ":您的工号不属于所选登录类型");
        }

        //当前登录IP
        final String ip = IPUtils.getIpAddr(request);

        //生成token
        final SysTokenEntity sysTokenEntity = sysTokenService.createToken(employeeNumber, employeeNumber, ip);

        //组装返回前台的VO
        final TokenVO tokenVO = sysMapping.tokenDtoToVo(sysTokenEntity);
        tokenVO.setExpire(new DateTime(sysTokenEntity.getExpireTime()).toString(DATE_FORMAT_DEFAULT));
        tokenVO.setEmployeeName(employeeName);

        if (!UserTypeEnum.STUDENT.getCode().equals(type)) {
            CollegeTrainUser collegeTrainUser = collegeTrainUserService.getByEmployeeNo(employeeNumber);
            if (ObjectUtil.isNotEmpty(collegeTrainUser)) {
                tokenVO.setRoleIds(collegeTrainUser.getRoleId());
            }
        }

        //登录
        SecurityUtils.getSubject().login(new UserAuthenticationToken(sysTokenEntity.getToken()));
        log.info("员工【{}】从【{}】登入系统...", employeeName, ip);

        if (kickOutOther) {
            final Integer kickOutNo = sysTokenService.kickOutOtherToken(employeeNumber, ip);
            log.info("员工【{}】踢掉【{}】人...", employeeName, kickOutNo);
        }

        return new ResultVO(tokenVO);
    }

    /**
     * 退出
     */
    @IgnoreSysLog
    @ApiOperation(value = "退出", hidden = true)
    @PostMapping(value = URI_LOGOUT)
    public Rest logout() {
        log.info("员工<{}>退出系统...", ShiroUtils.getEmployeeNumber());
        ShiroUtils.logout();
        return RestBody.ok();
    }

    /**
     * 生成AD域的用户名
     * <p>
     * (1)如果入参为五位数字，则返回 ks0+五位数字
     * <p>
     * (2)其余不变
     *
     * @param str 入参
     * @return 出参
     */
    private String generateAdName(final String str) {
        if (NUMBER_PATTERN.matcher(str).matches()) {
            //五位数字
            return "ks" + Strings.padStart(str, 6, '0');
        } else {
            return str;
        }
    }


}
