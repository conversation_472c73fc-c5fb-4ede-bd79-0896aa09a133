package com.htks.web.admin.vo;

import com.htks.web.AbstractVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("课程清单列表查询结果前端类")
public class AdminListPageVO extends AbstractVO {
    @ApiModelProperty(value = "用户ID")
    private Long id;

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "角色信息")
    private String roleInfo;

}
