package com.htks.web.admin;


import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.admin.AdminCondition;
import com.htks.domain.admin.dto.AdminSearchEntity;
import com.htks.domain.admin.service.AdminService;
import com.htks.domain.common.dto.CommonRoleEntity;
import com.htks.domain.common.dto.UpgradeUserAdminEntity;
import com.htks.domain.common.service.CommonService;
import com.htks.web.JsonPagedVO;
import com.htks.web.admin.vo.AdminListPageVO;
import com.htks.web.admin.vo.AdminSaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.htks.web.WebURIMappingConstant.*;

@Slf4j
@Api(tags = {"培训组模块-管理员"})
@RestController
public class AdminController {


    private final AdminService adminService;
    private final CommonService commonService;

    public AdminController(AdminService adminService,CommonService commonService){
        this.adminService = adminService;
        this.commonService = commonService;
    }

    /**
     * 列表查询
     */
    @ApiOperation(value = "列表查询")
    @PostMapping(value = URI_USER_SEARCH_ADMIN)
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功",response = AdminListPageVO.class),
    })
    public JsonPagedVO searchAdmin(@RequestBody AdminCondition adminCondition) throws InvocationTargetException, IllegalAccessException {
        if (log.isDebugEnabled()) {
            log.debug("管理员-列表查询{}", adminCondition);
        }
        final int recordCount = adminService.queryAdminCount(adminCondition);
        List<CommonRoleEntity>  roleResult = commonService.getCommonRoleInfo("岗位资格");
        Map<Long,String> roleMap = roleResult.stream().collect(Collectors.toMap(CommonRoleEntity::getId,CommonRoleEntity::getRoleName));
        final List<AdminSearchEntity> result = adminService.queryAdminList(adminCondition);
        if(result !=null){
            List<AdminListPageVO> resultVoList = new ArrayList<>();
            for (AdminSearchEntity item: result) {
                AdminListPageVO temp = new AdminListPageVO();
                BeanUtils.copyProperties(temp, item);
                resultVoList.add(temp);
                if(item.getRoleId() != null && !item.getRoleId().isEmpty()){
                    if(item.getRoleId().startsWith(",")){
                        item.setRoleId(item.getRoleId().substring(1));
                    }
                    temp.setRoleInfo(Arrays.stream(item.getRoleId().split(",")).map(key-> roleMap.get( Long.parseLong(key))).collect(Collectors.joining(",")));
                }
            }
            return new JsonPagedVO(resultVoList,recordCount);
        }
        return new JsonPagedVO(new ArrayList<>(),recordCount);

    }

    /**
     * 删除后台用户
     */
    @ApiOperation(value = "删除后台用户")
    @PostMapping(value = URI_USER_DELETE_ADMIN)
    public ResultVO deleteCourseInventory(@RequestParam("id") Long id) {
        log.info("管理员-删除后台用户:{}",id);
        if(adminService.deleteAdmin(id)){
            return new ResultVO(ResultCode.SUCCESS);
        }
        return new ResultVO(ResultCode.FAILED,null);
    }

    /**
     * 保存用户
     */
    @ApiOperation(value = "保存用户")
    @PostMapping(value = URI_USER_SAVE_ADMIN)
    public ResultVO saveAdmin(@RequestBody AdminSaveVO adminSaveVO) throws InvocationTargetException, IllegalAccessException {
        log.info("管理员-保存用户:{}",adminSaveVO);
        UpgradeUserAdminEntity upgradeUserAdminEntity = new UpgradeUserAdminEntity();
        BeanUtils.copyProperties(upgradeUserAdminEntity, adminSaveVO);
        if(upgradeUserAdminEntity.getId() != null && upgradeUserAdminEntity.getId()>0){
            if(adminService.updateAdmin(upgradeUserAdminEntity)){
                return new ResultVO(ResultCode.SUCCESS);
            }
        }else {
            if(adminService.addAdmin(upgradeUserAdminEntity)){
                return new ResultVO(ResultCode.SUCCESS);
            }
        }
        return new ResultVO(ResultCode.FAILED,"");
    }


}
