package com.htks.web;

import com.htks.web.helper.session.WebSessionObject;
import org.apache.shiro.subject.Subject;

import static com.google.common.base.MoreObjects.firstNonNull;
import static com.htks.common.SystemConfig.SHIRO_SESSION_KEY;
import static org.apache.shiro.SecurityUtils.getSubject;


public abstract class AbstractActionController {

    private WebSessionObject getCurrentUser() {
        final Subject securitySubject = getSubject();
        return firstNonNull((WebSessionObject) securitySubject.getSession().getAttribute(SHIRO_SESSION_KEY), new WebSessionObject());
    }

    protected final String getCurrentEmployeeName() {
        return getCurrentUser().getEmployeeName();
    }

    protected final String getCurrentEmployeeNumber() {
        return getCurrentUser().getEmployeeNumber();
    }
}
