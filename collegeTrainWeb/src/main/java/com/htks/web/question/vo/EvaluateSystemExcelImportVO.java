package com.htks.web.question.vo;

import com.htks.domain.question.dto.QuestionEntity;
import com.htks.web.AbstractVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@ApiModel("评价系统-导入excel转换类")
public class EvaluateSystemExcelImportVO extends AbstractVO {

    @ApiModelProperty(value = "标题")
    private String sheetTitleName;

    @ApiModelProperty(value = "岗位名称")
    private String sheetPositionName;

    List<QuestionEntity> questionEntityList;

}
