package com.htks.web.question;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.CommonDecryptFileHelper;
import com.htks.common.utils.EasyExcelUtils;
import com.htks.domain.question.QuestionService;
import com.htks.domain.question.dto.*;
import com.htks.web.JsonPagedVO;
import com.htks.web.question.vo.EvaluateSystemExcelImportVO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.htks.web.WebURIMappingConstant.*;

/**
 * 登录页面Controller
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Slf4j
@Api(tags = {"试题维护"}, hidden = true)
@RestController
public class QuestionController {

    @Autowired
    private QuestionService questionService;

    @ApiOperation(value = "查询试题信息")
    @PostMapping(value = URI_QUESTION_LIST)
    public JsonPagedVO<PaperEntityDto> getEmployeeInfo(@RequestBody @Validated PaperCondition condition) {
        if (log.isDebugEnabled()) {
            log.debug("查询试题信息{}", JSONObject.toJSONString(condition));
        }
        return questionService.queryPaperList(condition);
    }

    @ApiOperation(value = "保存试卷信息")
    @PostMapping(value = URI_QUESTION_SAVE)
    public ResultVO savePaper(@RequestBody @Validated PaperSaveCondition condition) {
        if (log.isDebugEnabled()) {
            log.debug("保存试卷信息{}", JSONObject.toJSONString(condition));
        }
        return questionService.savePaper(condition);
    }
    @ApiOperation("已有文件添加水印")
    @PostMapping("/fileWatermark")
    @ResponseBody
    @ApiImplicitParam(name = "multipartFile",value = "加水印文件",required = true,dataType="java.io.File",allowMultiple = true,paramType = "formData")
    public ResultVO fileWatermark(@RequestParam("multipartFile") MultipartFile  multipartFile, @RequestParam("secretType") String secretType) {
        return null;
    }

    @ApiOperation("保存试卷")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/submitDraft")
    @ResponseBody
    @ApiImplicitParam(name="file",value = "附件",required = true,dataType="java.io.File",allowMultiple = true,paramType = "formData")
    public ResultVO submitDraft( @RequestParam ("file") MultipartFile file,
                                 @RequestParam("examCategoryId") Long examCategoryId,
                                 @RequestParam(value ="postInfoId", required= false) String postInfoId,
                                 @RequestParam(value ="department", required= false) String department
    ) throws IOException {
        final InputStream in = CommonDecryptFileHelper.commonDecryptFile(file);
        List<QuestionImportExcel> excelList = EasyExcelUtils.readExcel2(in, QuestionImportExcel.class);

        // 进行试卷数据处理和校验
        List<QuestionImportExcel> newList = new ArrayList<>();
        newList.addAll(excelList);
        newList.removeIf(p -> p.getTitle() == null || p.getTitle().isEmpty());
        newList.stream().map(e -> e.getAnswer().replaceAll("，", ",")).collect(Collectors.toList());
        // TODO: 添加试卷数据校验逻辑
        PaperSaveCondition condition=new PaperSaveCondition();
        condition.setQuestionImportExcelList(newList);
        condition.setDepartment(department);
        condition.setExamCategoryId(examCategoryId);
        String originalFilename = file.getOriginalFilename();

// 去除文件扩展名
        if (originalFilename != null && originalFilename.contains(".")) {
            originalFilename = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        }
        condition.setPaperName(originalFilename);
        condition.setPostInfoId(postInfoId);
        condition.setUpdater("31973");
        condition.setCreator("31973");
        // 保存试卷信息
        ResultVO saveResult = savePaper(condition);

        // 返回结果，包含导入的试题信息和保存试卷的结果
        return new ResultVO(saveResult.getData());
    }

    @ApiOperation(value = "保存试卷信息")
    @PostMapping(value = "test")
    @ApiImplicitParam(name = "multipartFile",value = "加水印文件",required = true,dataType="java.io.File",allowMultiple = true,paramType = "query")
    public ResultVO test(@RequestBody @Validated PaperSaveCondition condition) {
        if (log.isDebugEnabled()) {
            log.debug("保存试卷信息{}", JSONObject.toJSONString(condition));
        }
        return questionService.savePaper(condition);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入试卷考题文件")
    @PostMapping(value = URI_QUESTION_IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功",response = EvaluateSystemExcelImportVO.class),
    })
    public ResultVO importExamPaper(@RequestParam MultipartFile file) throws IOException {
        final InputStream in = CommonDecryptFileHelper.commonDecryptFile(file);
        List<QuestionImportExcel> excelList = EasyExcelUtils.readExcel2(in, QuestionImportExcel.class);

        List<QuestionImportExcel> newList = new ArrayList<>();
        newList.addAll(excelList);
        newList.removeIf(p->p.getTitle() ==null||p.getTitle().isEmpty());
        newList.stream().map(e->e.getAnswer().replaceAll("，",",")).collect(Collectors.toList());
        //TODO:校验试卷数据：空行，空列，表头没按模板，题型不规范，题干去除换行和空格，答案不规范（A,B,C,D)正确，错误；缺少选项
        return new ResultVO(newList);
    }

    @ApiOperation(value = "下拉部门信息")
    @GetMapping(value = URI_QUESTION_DEPARTMENT_LIST)
    public ResultVO<DepartmentDto> getDepartmentList() {
        if (log.isDebugEnabled()) {
            log.debug("下拉部门信息{}");
        }
        return questionService.getDepartmentList();
    }

    @ApiOperation(value = "下拉考试项目")
    @GetMapping(value = URI_QUESTION_CATEGORY)
    public ResultVO<DepartmentDto> categoryList() {
        if (log.isDebugEnabled()) {
            log.debug("下拉考试项目{}");
        }
        return questionService.categoryList();
    }

    @ApiOperation(value = "删除试题")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "questionId", name = "questionId", required = true, dataType = "long")}
    )
    @GetMapping(value = URI_QUESTION_DELETE)
    public ResultVO<DepartmentDto> deletePaper(@RequestParam Long questionId) {
        if (log.isDebugEnabled()) {
            log.debug("删除试题{}",questionId);
        }
        return questionService.deletePaperQuestion(questionId);
    }

    @ApiOperation(value = "查看试题详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "questionId", name = "questionId", required = true, dataType = "long")}
    )
    @GetMapping(value = URI_QUESTION_DETAIL)
    public ResultVO<QuestionEntity> getQuestionDetail(@RequestParam Long questionId) {
        if (log.isDebugEnabled()) {
            log.debug("查看试题详情{}",questionId);
        }
        return questionService.getQuestionDetail(questionId);
    }

    @ApiOperation(value = "编辑试题")
    @PostMapping(value = URI_QUESTION_UPDATE)
    public ResultVO<DepartmentDto> updateQuestion(@RequestBody QuestionEntity questionEntity) {
        if (log.isDebugEnabled()) {
            log.debug("编辑试题{}",JSONObject.toJSONString(questionEntity));
        }
        return questionService.updateQuestion(questionEntity);
    }



}
