package com.htks.web.question;


import com.alibaba.fastjson.JSONObject;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.course.CourseService;
import com.htks.domain.course.dto.CourseCondition;
import com.htks.domain.course.dto.CourseSaveCondition;
import com.htks.domain.pushCourse.PushCourseService;
import com.htks.domain.pushCourse.dto.PushCourseCondition;
import com.htks.domain.pushCourse.dto.PushCourseEntityDto;
import com.htks.domain.pushCourse.dto.PushExamCondition;
import com.htks.domain.pushCourse.dto.PushExamEntityDto;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.web.JsonPagedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.htks.web.WebURIMappingConstant.*;

/**
 * 登录页面Controller
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Slf4j
@Api(tags = {"抛送课程维护"}, hidden = true)
@RestController
public class PushCourseController {

    @Autowired
    private PushCourseService pushCourseService;

    @ApiOperation(value = "查询抛送课程信息")
    @PostMapping(value = URI_PUSH_COURSE_LIST)
    public JsonPagedVO<PushCourseEntityDto> queryPushCourseList(@RequestBody @Validated PushCourseCondition condition) {
        if (log.isDebugEnabled()) {
            log.debug("查询抛送课程信息{}", JSONObject.toJSONString(condition));
        }
        return pushCourseService.queryPushCourseList(condition);
    }

    @ApiOperation(value = "查询抛送考试信息")
    @PostMapping(value = URI_PUSH_EXAM_LIST)
    public JsonPagedVO<PushExamEntityDto> queryPushExamList(@RequestBody @Validated PushExamCondition condition) {
        if (log.isDebugEnabled()) {
            log.debug("查询抛送考试信息{}", JSONObject.toJSONString(condition));
        }
        return pushCourseService.queryPushExamList(condition);
    }

    @ApiOperation(value = "抛送课程")
    @PostMapping(value = URI_PUSH_COURSE_SEND)
    public ResultVO sendCourse(@RequestBody @Validated PushCourseEntityDto condition) {
        if (log.isDebugEnabled()) {
            log.debug("抛送课程{}", JSONObject.toJSONString(condition));
        }
        return pushCourseService.sendCourse(condition);
    }

    @ApiOperation(value = "抛送考试")
    @PostMapping(value = URI_PUSH_EXAM_SEND)
    public ResultVO sendExam(@RequestBody @Validated PushExamEntityDto condition) {
        if (log.isDebugEnabled()) {
            log.debug("抛送考试{}", JSONObject.toJSONString(condition));
        }
        return pushCourseService.sendExam(condition);
    }

    @ApiOperation(value = "跑抛人查询")
    @PostMapping("/secured/pushCourse/getAdminNo")
    public ResultVO getAdminNo(@RequestParam String name) {
        try {
            List<PushCourseEntityDto> list = pushCourseService.getAdminNo(name);
            return new ResultVO(1000, "上传成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation(value = "课程类别查询")
    @PostMapping("/secured/pushCourse/getCourseDetail")
    public ResultVO getCourseDetail() {
        try {
            List<String> list = pushCourseService.getCourse();
            return new ResultVO(1000, "上传成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "失败，请联系后台管理人员处理");
        }
    }
    @ApiOperation(value = "考试类别查询")
    @PostMapping("/secured//pushExam/getExam")
    public ResultVO getExam() {
        try {
            List<String> list = pushCourseService.getExam();
            return new ResultVO(1000, "上传成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation(value = "具体考试查询")
    @PostMapping("/secured/pushExam/getExamDetail")
    public ResultVO getExamDetail(@RequestParam String categry) {
        try {
            List<String> list = pushCourseService.getExamDetail(categry);
            return new ResultVO(1000, "上传成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "失败，请联系后台管理人员处理");
        }
    }
}
