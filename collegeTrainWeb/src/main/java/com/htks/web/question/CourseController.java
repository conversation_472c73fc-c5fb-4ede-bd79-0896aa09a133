package com.htks.web.question;


import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.EasyExcelUtils;
import com.htks.domain.course.CourseService;
import com.htks.domain.course.dto.CourseCondition;
import com.htks.domain.course.dto.CourseSaveCondition;
import com.htks.domain.question.QuestionService;
import com.htks.domain.question.dto.*;
import com.htks.web.JsonPagedVO;
import com.htks.web.question.vo.EvaluateSystemExcelImportVO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.htks.web.WebURIMappingConstant.*;

/**
 * 登录页面Controller
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Slf4j
@Api(tags = {"课程维护"}, hidden = true)
@RestController
public class CourseController {

    @Autowired
    private CourseService courseService;

    @ApiOperation(value = "查询课程信息")
    @PostMapping(value = URI_COURSE_LIST)
    public JsonPagedVO<PaperEntity> getCourseList(@RequestBody @Validated CourseCondition condition) {
        if (log.isDebugEnabled()) {
            log.debug("查询课程信息{}", JSONObject.toJSONString(condition));
        }
        return courseService.queryCourseList(condition);
    }

    @ApiOperation(value = "保存课程信息")
    @PostMapping(value = URI_COURSE_SAVE)
    public ResultVO saveCourse(@RequestBody @Validated CourseSaveCondition condition) {
        if (log.isDebugEnabled()) {
            log.debug("保存课程信息{}", JSONObject.toJSONString(condition));
        }
        return courseService.saveCourse(condition);
    }

    @ApiOperation(value = "删除课程信息")
    @PostMapping(value = URI_COURSE_DELETE)
    public ResultVO deleteCourse(@RequestParam Long id) {
        if (log.isDebugEnabled()) {
            log.debug("删除课程信息{}", id);
        }
        return courseService.deleteCourse(id);
    }



}
