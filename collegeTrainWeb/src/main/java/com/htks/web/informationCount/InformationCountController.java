package com.htks.web.informationCount;

import com.alibaba.excel.EasyExcel;
import com.htks.common.config.WeChatConfig;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.ExamInformation.dto.*;
import com.htks.domain.ExamInformation.service.SatisfactionCountService;
import com.htks.domain.ExamInformation.service.impl.*;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.domain.student.service.impl.SatisfactionDegreeServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpMessageServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import com.htks.common.utils.WeChatUtils;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

import com.htks.common.utils.WeChatUtils;

@Api(tags = {"管理员 - 信息查询"})
@RestController
public class InformationCountController {
    @Autowired
    private SatisfactionCountService satisfactionCountService;
    @Autowired
    SatisfactionCountServiceImpl service;
    @Autowired
    private WeChatConfig weChatConfig;
    @Autowired
    private UploadWorkLicense uploadWorkLicense;
    @ApiOperation("0101 - 大学生满意度详情信息")
    @PostMapping("/informationCount/getStatisticsCount")
    public ResultVO getStatisticsCount(@RequestBody StatisticsReasonBody reasonBody) {
        try {
            if (reasonBody.getStartTime()!=null&&!"".equals(reasonBody.getStartTime())) {
                reasonBody.setStartTime(reasonBody.getStartTime() + " 00:00:00");
                reasonBody.setEndTime(reasonBody.getEndTime() + " 23:59:59");
            }
            List<StatisticsInformationCount> list = satisfactionCountService.getSatisfactionInformation(reasonBody.getStartTime(),reasonBody.getEndTime(), reasonBody.getBatch(), reasonBody.getEmployeeName(), reasonBody.getEmployeeNo(), reasonBody.getDepartment(), reasonBody.getArea());

            return new ResultVO(1000, "查询成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }

    @ApiOperation("0102 - 大学生满意度详情导出")
    @PostMapping("/informationCount/getStatisticsCountExel")
    public void getExamInformationExel(@RequestBody StatisticsReasonBody reasonBody) {
        try {
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            if (reasonBody.getStartTime()!=null&&!"".equals(reasonBody.getStartTime())) {
                reasonBody.setStartTime(reasonBody.getStartTime() + " 00:00:00");
                reasonBody.setEndTime(reasonBody.getEndTime() + " 23:59:59");
            }
            List<StatisticsInformationCount> list = satisfactionCountService.getSatisfactionInformation(reasonBody.getStartTime(),reasonBody.getEndTime(), reasonBody.getBatch(), reasonBody.getEmployeeName(), reasonBody.getEmployeeNo(), reasonBody.getDepartment(), reasonBody.getArea());
            String fileName = "大学生满意度记录.xlsx";
            for (StatisticsInformationCount statisticsInformationCount:list) {
                statisticsInformationCount.setCreatedTime(statisticsInformationCount.getCreatedTime().substring(0,19));
            }
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8"));
            EasyExcel.write(response.getOutputStream(), StatisticsInformationCount.class)
                    .sheet("test")
                    .doWrite(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation("0103 - 大学生成绩详情信息")
    @PostMapping("/informationCount/ExamInformation")
    public ResultVO ExamInformation(@RequestBody ExamInformationReasonBody exam) {
        try {
            List<ExamInformation> information = service.getExamInformation(exam);


            Collator collator = Collator.getInstance(Locale.CHINA);

            Collections.sort(information, Comparator.comparing((ExamInformation info) -> convertToPinyin(info.getDepartment()), collator)
                    .thenComparing((ExamInformation info) -> convertToPinyin(info.getArea()), collator)
                    .thenComparing((ExamInformation info) -> convertToPinyin(info.getEmployeeName()), collator)
            );
            return new ResultVO(1000, "查询成功", information);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }

    private String convertToPinyin(String chinese) {
        StringBuilder pinyin = new StringBuilder();
        if (chinese == null) {
            return ""; // 返回空字符串
        }
        for (char c : chinese.toCharArray()) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
            if (pinyinArray != null) {
                pinyin.append(pinyinArray[0].charAt(0));
            } else {
                pinyin.append(c);
            }
        }
        return pinyin.toString().toUpperCase();
    }

    @ApiOperation("0104 - 大学生成绩详情导出")
    @PostMapping("/informationCount/getExamInformationExel")
    public void getStatisticsCountExel(@RequestBody ExamInformationReasonBody exam) {
        try {
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            List<ExamInformation> information = service.getExamInformation(exam);
            String fileName = "大学生成绩详情.xlsx";
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8"));
            EasyExcel.write(response.getOutputStream(), ExamInformation.class)
                    .sheet("test")
                    .doWrite(information);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation("0105 - 大学生成绩排名信息")
    @PostMapping("/informationCount/ExamInformationRanking")
    public ResultVO ExamInformationRanking(@RequestBody ExamInformationReasonBody exam) {
        try {
            if (exam.getDepartment()!=null) {
                List<String> lista = new ArrayList<>();
                for (int i = 0; i < exam.getDepartment().size(); i++) {
                    lista.add(uploadWorkLicense.getDepartmentName(exam.getDepartment().get(i)));
                }

                List<String> factory = new ArrayList<>();
                if (exam.getFactoryType() == null || exam.getFactoryType().size()==0) {
                    for (int i = 0; i < exam.getDepartment().size(); i++) {
                        factory.add(uploadWorkLicense.getFactory(exam.getDepartment().get(i)));
                    }
                    if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
                        exam.setFactoryType(factory);
                    } else if (factory.contains("华天昆山")) {
                        exam.setFactoryType(factory);
                    } else if (factory.contains("华天江苏")) {
                        exam.setFactoryType(factory);
                    }
                }
                exam.setDepartment(lista);
            }


            List<Long> Id = service.getAssessedId(exam.getBatch(), exam.getEmployeeName(), exam.getEmployeeNo(), exam.getDepartment(), exam.getArea(),exam.getFactoryType(),exam.getClassNo());
            List<ExamAllCollegeInformation> list = service.getExamInformationRanking(Id, exam.getExam());
            for (int i = 0; i < list.size(); i++) {
                double score = list.get(i).getScore();
                double roundedScore = Math.round(score * 100.00) / 100.00;
                list.get(i).setScore(roundedScore);
            }
            List<ExamAllCollegeInformation> collect = list.stream()
                    .sorted(Comparator.comparingDouble(ExamAllCollegeInformation::getScore).reversed())
                    .collect(Collectors.toList());
            // 排名
            Double tempScore = 0.0;
            int rankIndex = 1;
            for (int i = 0; i < collect.size(); i++) {
                if(tempScore.compareTo(collect.get(i).getScore()) == 0){
                    // 分数相等
                    collect.get(i).setRanking(rankIndex);
                    rankIndex = i+1;
                }else {
                    rankIndex = i+1;
                    collect.get(i).setRanking(i+1);
                    tempScore = collect.get(i).getScore();
                }
            }
            return new ResultVO(1000, "查询成功", collect);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理人员处理");
        }
    }
    @ApiOperation("0106 - 大学生成绩排名信息导出")
    @PostMapping("/informationCount/ExamInformationRankingDownload")
    public void ExamInformationRankingDownload(@RequestBody ExamInformationReasonBody exam) {
        try {
            if (exam.getDepartment()!=null) {
                List<String> lista = new ArrayList<>();
                for (int i = 0; i < exam.getDepartment().size(); i++) {
                    lista.add(uploadWorkLicense.getDepartmentName(exam.getDepartment().get(i)));
                }

                List<String> factory = new ArrayList<>();
                if (exam.getFactoryType() == null && exam.getFactoryType().size()==0) {
                    for (int i = 0; i < exam.getDepartment().size(); i++) {
                        factory.add(uploadWorkLicense.getFactory(exam.getDepartment().get(i)));
                    }
                    if (factory.contains("华天昆山") && factory.contains("华天江苏")) {
                        exam.setFactoryType(factory);
                    } else if (factory.contains("华天昆山")) {
                        exam.setFactoryType(factory);
                    } else if (factory.contains("华天江苏")) {
                        exam.setFactoryType(factory);
                    }
                }
                exam.setDepartment(lista);
            }
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            List<Long> Id = service.getAssessedId(exam.getBatch(), exam.getEmployeeName(), exam.getEmployeeNo(), exam.getDepartment(), exam.getArea(),exam.getFactoryType(),exam.getClassNo());
            List<ExamAllCollegeInformation> list = service.getExamInformationRanking(Id, exam.getExam());
            for (int i = 0; i < list.size(); i++) {
                double score = list.get(i).getScore();
                double roundedScore = Math.round(score * 100.0) / 100.0;
                list.get(i).setScore(roundedScore);
            }
            List<ExamAllCollegeInformation> collect = list.stream()
                    .sorted(Comparator.comparing(ExamAllCollegeInformation::getScore).reversed())
                    .collect(Collectors.toList());
            for (int i = 0; i < collect.size(); i++) {
                collect.get(i).setRanking(i+1);
            }
            List<String> exportFields = new ArrayList<>();

            // 添加通用字段
            exportFields.add("ranking");
            exportFields.add("batch");
            exportFields.add("employeeNo");
            exportFields.add("employeeName");
            exportFields.add("department");
            exportFields.add("area");
            for (String s:exam.getExam()) {
                switch (s) {
                    case "IQ测试":
                        exportFields.add("IQExam");
                        break;
                    case "新生见面会":
                        exportFields.add("meeting");
                        break;
                    case "HR公开课理论考试":
                        exportFields.add("HRExam");
                        break;
                    case "工艺公开课理论考试":
                        exportFields.add("craftExam");
                        break;
                    case "BU公共课理论考试":
                        exportFields.add("BUExam");
                        break;
                    case "上岗证考核":
                        exportFields.add("postExam");
                        break;
                    case "工程类实操考试":
                        exportFields.add("appointRecord");
                        break;
                    case "SOP制作":
                        exportFields.add("sopExam");
                        break;
                    case "异常答辩":
                        exportFields.add("exceptionRecord");
                        break;
                    case "工程结业理论考试":
                        exportFields.add("projectExam");
                        break;
                    case "每周周报":
                        exportFields.add("weeklyReport");
                        break;
                    case "培训组长主观评价":
                        exportFields.add("subjectiveJudge");
                        break;
                    case "《为什么是华天》读后感":
                        exportFields.add("afterRead");
                        break;
                }
            }
            exportFields.add("source");
            List<ExamAllCollegeInformation> modifiedList = new ArrayList<>();
            for (ExamAllCollegeInformation info : collect) {
                ExamAllCollegeInformation modifiedInfo = new ExamAllCollegeInformation();
                //BeanUtils.copyProperties(info, modifiedInfo);
                // 移除不需要的属性
                if (!exportFields.contains("IQExam")) {
                    info.setIQExam(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("meeting")) {
                    info.setMeeting(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("HRExam")) {
                    info.setHRExam(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("craftExam")) {
                    info.setCraftExam(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("BUExam")) {
                    info.setBUExam(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("postExam")) {
                    info.setPostExam(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("appointRecord")) {
                    info.setAppointRecord(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("sopExam")) {
                    info.setSopExam(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("exceptionRecord")) {
                    info.setExceptionRecord(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("projectExam")) {
                    info.setProjectExam(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("weeklyReport")) {
                    info.setWeeklyReport(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("subjectiveJudge")) {
                    info.setSubjectiveJudge(null); // 假设 IQExam 是对象的属性，将其设置为 null
                } if (!exportFields.contains("afterRead")) {
                    info.setAfterRead(null); // 假设 IQExam 是对象的属性，将其设置为 null
                }
                info.setScore(null);
                for (int i = 0; i < exam.getExam().size(); i++) {
                    if (info.getIQExam() == null) {
                        info.setIQExam(info.getMeeting());
                        info.setMeeting(info.getHRExam());
                        info.setHRExam(info.getCraftExam());
                        info.setCraftExam(info.getBUExam());
                        info.setBUExam(info.getPostExam());
                        info.setPostExam(info.getAppointRecord());
                        info.setAppointRecord(info.getSopExam());
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());
                    }
                    if (info.getMeeting() == null) {
                        info.setMeeting(info.getHRExam());
                        info.setHRExam(info.getCraftExam());
                        info.setCraftExam(info.getBUExam());
                        info.setBUExam(info.getPostExam());
                        info.setPostExam(info.getAppointRecord());
                        info.setAppointRecord(info.getSopExam());
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());
                    }
                    if (info.getHRExam() == null) {
                        info.setHRExam(info.getCraftExam());
                        info.setCraftExam(info.getBUExam());
                        info.setBUExam(info.getPostExam());
                        info.setPostExam(info.getAppointRecord());
                        info.setAppointRecord(info.getSopExam());
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());
                    }
                    if (info.getCraftExam() == null) {
                        info.setCraftExam(info.getBUExam());
                        info.setBUExam(info.getPostExam());
                        info.setPostExam(info.getAppointRecord());
                        info.setAppointRecord(info.getSopExam());
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());
                    }
                    if (info.getBUExam() == null) {
                        info.setBUExam(info.getPostExam());
                        info.setPostExam(info.getAppointRecord());
                        info.setAppointRecord(info.getSopExam());
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());
                    }   if (info.getPostExam() == null) {
                        info.setPostExam(info.getAppointRecord());
                        info.setAppointRecord(info.getSopExam());
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());

                    }   if (info.getAppointRecord() == null) {
                        info.setAppointRecord(info.getSopExam());
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());

                    }   if (info.getSopExam() == null) {
                        info.setSopExam(info.getExceptionRecord());
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());

                    }   if (info.getExceptionRecord() == null) {
                        info.setExceptionRecord(info.getProjectExam());
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());

                    }
                    if (info.getProjectExam() == null) {
                        info.setProjectExam(info.getWeeklyReport());
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());

                    }
                    if (info.getWeeklyReport() == null) {
                        info.setWeeklyReport(info.getSubjectiveJudge());
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());
                    }
                    if (info.getSubjectiveJudge() == null) {
                        info.setSubjectiveJudge(info.getAfterRead());
                        info.setAfterRead(info.getScore());
                    } if (info.getAfterRead() == null) {
                        info.setAfterRead(info.getScore());
                    }
                }
                info.setScore(getTotal(info));

                if (info.getIQExam() == null) {
                    info.setIQExam(info.getScore());info.setScore(null);
                }
                 if  (info.getMeeting() == null) {
                    info.setMeeting(info.getScore());info.setScore(null);
                }
                  if (info.getHRExam() == null) {
                    info.setHRExam(info.getScore());info.setScore(null);
                }
                  if (info.getCraftExam() == null) {
                    info.setCraftExam(info.getScore());
                    info.setScore(null);
                }
                 if (info.getBUExam() == null) {
                    info.setBUExam(info.getScore());
                    info.setScore(null);
                }   if (info.getPostExam() == null) {
                    info.setPostExam(info.getScore());
                    info.setScore(null);
                }  if (info.getAppointRecord() == null) {
                    info.setAppointRecord(info.getScore());
                    info.setScore(null);

                }   if (info.getSopExam() == null) {
                    info.setSopExam(info.getScore());info.setScore(null);


                }   if (info.getExceptionRecord() == null) {
                    info.setExceptionRecord(info.getScore());info.setScore(null);
                }
                 if (info.getProjectExam() == null) {
                    info.setProjectExam(info.getScore());
                    info.setScore(null);

                }
                 if (info.getWeeklyReport() == null) {
                    info.setWeeklyReport(info.getScore());
                     info.setScore(null);
                 }
                 if (info.getSubjectiveJudge() == null) {
                    info.setSubjectiveJudge(info.getScore());
                    info.setScore(null);
                }
               /* if (exam.getExam().size()!=13){
                    info.setScore(null);
                }*/
            }
                String fileName = "大学生成绩排名信息.xlsx";
                response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8"));

                // 导出数据到 Excel
            EasyExcel.write(response.getOutputStream(), ExamAllCollegeInformation.class)
                    .sheet("test")
                    .head(generateExcelHead(exportFields)) // 调用生成表头信息的方法
                    .doWrite(collect);
            } catch (Exception e) {
            e.printStackTrace();
        }
    }
    private List<List<String>> generateExcelHead(List<String> exportFields) {
        List<List<String>> head = new ArrayList<>();
        // 遍历每个字段
        for (String field : exportFields) {
            List<String> tmp = new ArrayList<>(); // 每次循环创建一个新的 tmp 列表
            // 根据字段名生成表头信息
            switch (field) {
                case "ranking":
                    tmp.add("排名");
                    break;
                case "batch":
                    tmp.add("批次");
                    break;
                case "employeeNo":
                    tmp.add("员工编号");
                    break;
                case "employeeName":
                    tmp.add("员工姓名");
                    break;
                case "department":
                    tmp.add("部门");
                    break;
                case "area":
                    tmp.add("区域");
                    break;
                case "IQExam":
                    tmp.add("IQ测试");
                    break;
                case "meeting":
                    tmp.add("新生见面会");
                    break;
                case "HRExam":
                    tmp.add("HR公开课理论考试");
                    break;
                case "craftExam":
                    tmp.add("工艺公开课理论考试");
                    break;
                case "BUExam":
                    tmp.add("BU公开课理论考试");
                    break;
                case "postExam":
                    tmp.add("上岗证考核");
                    break;
                case "appointRecord":
                    tmp.add("工程类实操考试");
                    break;
                case "sopExam":
                    tmp.add("SOP制作");
                    break;
                case "exceptionRecord":
                    tmp.add("异常答辩");
                    break;
                case "projectExam":
                    tmp.add("工程结业理论考试");
                    break;
                case "weeklyReport":
                    tmp.add("每周周报");
                    break;
                case "subjectiveJudge":
                    tmp.add("培训组长主观评价");
                    break;
                case "afterRead":
                    tmp.add("《为什么是华天》读后感");
                    break;
                case "source":
                    tmp.add("总分");
                    break;
                // 添加其他字段对应的表头信息
                // 其他字段的设置
                default:
                    tmp.add(field); // 默认直接将字段名作为表头
            }
            head.add(tmp); // 将每次生成的 tmp 列表作为一个表头添加到 head 列表中
        }
        return head;
    }
    private Double getTotal(ExamAllCollegeInformation information) {
        Double total = 0.0;

        total += getValueOrDefault(information.getBUExam());
        total += getValueOrDefault(information.getHRExam());
        total += getValueOrDefault(information.getAfterRead());
        total += getValueOrDefault(information.getIQExam());
        total += getValueOrDefault(information.getAppointRecord());
        total += getValueOrDefault(information.getCraftExam());
        total += getValueOrDefault(information.getExceptionRecord());
        total += getValueOrDefault(information.getMeeting());
        total += getValueOrDefault(information.getPostExam());
        total += getValueOrDefault(information.getProjectExam());
        total += getValueOrDefault(information.getWeeklyReport());
        total += getValueOrDefault(information.getSubjectiveJudge());
        total += getValueOrDefault(information.getSopExam());

        return total;
    }

    private Double getValueOrDefault(Double value) {
        if (value != null) {
            return value;
        } else {
            return 0.0;
        }
    }
    @ApiOperation("0107 - 一键推送")
    @PostMapping("/informationCount/wechatPush")
    public ResultVO wechatPush(@RequestBody List<ExamAllCollegeInformation> exam) {
        // 开启微信服务
        try {
            final WxCpService wxCpService = weChatConfig.getWxCpService();
            final WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(wxCpService);
            // 开启微信消息服务
            final WxCpMessage wxCpMessage = new WxCpMessage();
            List<String> employeeNos = new ArrayList<>();
            for (int i = 0; i < exam.size(); i++) {
                employeeNos.add(exam.get(i).getEmployeeNo());
                    EmployeeEntity employeeEntity = service.getWechatId(exam.get(i).getEmployeeNo());
                    if (employeeEntity != null) {
                        WeChatUtils weChatUtils = new WeChatUtils();
                        weChatUtils.sendMsg(wxCpMessage, wxCpMessageService, employeeEntity.getUserWeChatId(),
                                "您好" + exam.get(i).getEmployeeName() + "学员，您的大学生培训考试已经结束，以下是您所有的辛苦努力的成果，请查收！" +"\r\n"+
                                        "IQ测试：" + exam.get(i).getIQExam() +"分"+"\r\n"+
                                        "HR公开课理论考试：" + exam.get(i).getHRExam()+"分" +"\r\n"+
                                        "工艺公开课理论考试：" + exam.get(i).getCraftExam()+"分" +"\r\n"+
                                        "BU公开课理论考试：" + exam.get(i).getBUExam() +"分"+"\r\n"+
                                        "上岗证考核：" + exam.get(i).getPostExam() +"分"+"\r\n"+
                                        "工程类实操考试：" + exam.get(i).getAppointRecord()+"分" +"\r\n"+
                                        "SOP制作：" + exam.get(i).getSopExam() +"分"+"\r\n"+
                                        "异常答辩：" + exam.get(i).getExceptionRecord()+"分" +"\r\n"+
                                        "工程结业理论考试：" + exam.get(i).getProjectExam()+"分" +"\r\n"+
                                        "每周周报：" + exam.get(i).getWeeklyReport()+"分" +"\r\n"+
                                        "培训组长主观评价：" + exam.get(i).getSubjectiveJudge()+"分" +"\r\n"+
                                        "《为什么是华天》读后感：" + exam.get(i).getAfterRead()+"分" +"\r\n"+
                                        "新生见面会：" + exam.get(i).getMeeting()+"分" +"\r\n"+
                                        "总分:" + exam.get(i).getScore()+"分"+"\r\n"+
                                        "总排名：第"+exam.get(i).getRanking()+"名"
                        );
                    }
            }
            return new ResultVO(1000, "推送成功");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "推送失败");
        }
    }

}
