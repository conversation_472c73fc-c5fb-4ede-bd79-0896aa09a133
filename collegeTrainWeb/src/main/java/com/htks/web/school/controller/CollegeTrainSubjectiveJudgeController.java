package com.htks.web.school.controller;

import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.request.CollegeTrainSubjectiveJudgeRequest;
import com.htks.domain.school.service.CollegeTrainSubjectiveJudgeService;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.web.JsonPagedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 大学生培训系统 培训组长主观分评价 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainSubjectiveJudge")
@Slf4j
@Api(tags = {"培训组长主观分评价"})
public class CollegeTrainSubjectiveJudgeController {

    @Resource
    private CollegeTrainSubjectiveJudgeService collegeTrainSubjectiveJudgeService;

    @ApiOperation(value = "查询培训组长主观分评价列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPageList")
    public JsonPagedVO getPageList(@RequestBody CollegeTrainSubjectiveJudgeRequest request) {
        return collegeTrainSubjectiveJudgeService.getPageList(request);
    }

    @IgnoreSysLog
    @ApiOperation(value = "培训组长主观分评价模板", notes = "培训组长主观分评价模板")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("downTemplate")
    public void downTemplate(HttpServletResponse response) throws IOException {
        collegeTrainSubjectiveJudgeService.downTemplate(response);
    }

    @ApiOperation(value = "开始评价", notes = "开始评价")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("startEvaluation")
    public ResultVO startEvaluation(@RequestBody CollegeTrainSubjectiveJudgeRequest request) {
        collegeTrainSubjectiveJudgeService.startEvaluation(request);
        return ResultVO.success();
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入培训组长主观分评价模板", notes = "导入培训组长主观分评价模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("importTemplate")
    public ResultVO importTemplate(@RequestParam MultipartFile file) {
        return collegeTrainSubjectiveJudgeService.importTemplate(file);
    }

}
