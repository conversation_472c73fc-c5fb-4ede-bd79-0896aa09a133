package com.htks.web.school.controller;

import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.exception.CustomerException;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.request.StartReviewingRequest;
import com.htks.domain.school.service.CollegeTrainExamRecordService;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.web.JsonPagedVO;
import com.htks.web.Rest;
import com.htks.web.RestBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 大学生 考试记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainExamRecord")
@Slf4j
@Api(tags = {"大学生考试记录"})
public class CollegeTrainExamRecordController {
    @Resource
    private CollegeTrainExamRecordService collegeTrainExamRecordService;


    @ApiOperation(value = "查询考试记录列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPageList")
    public JsonPagedVO<List<CollegeTrainExamRecordVo>> getPageList(@RequestBody CollegeTrainExamRecordRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询大学生考试记录{}", JSONObject.toJSONString(request));
        }
        if (request.getExamStartTime()!=null&&!"".equals(request.getExamStartTime())){
            request.setExamStartTime(request.getExamStartTime() + " 00:00:00");
            request.setExamEndTime(request.getExamEndTime() + " 23:59:59");
        }
        return collegeTrainExamRecordService.queryPaperList(request);
    }

    @ApiOperation(value = "查询待批阅考试列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getForApprovalPageList")
    public JsonPagedVO<CollegeTrainExamRecordVo> getForApprovalPageList(@RequestBody CollegeTrainExamRecordRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询待批阅考试记录{}", JSONObject.toJSONString(request));
        }
        if (request.getExamStartTime()!=null&&!"".equals(request.getExamStartTime())){
            request.setExamStartTime(request.getExamStartTime() + " 00:00:00");
            request.setExamEndTime(request.getExamEndTime() + " 23:59:59");
        }
        return collegeTrainExamRecordService.getForApprovalPageList(request);
    }

    @ApiOperation(value = "开始批阅")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("startReviewing")
    public ResultVO startReviewing(@RequestBody StartReviewingRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("开始批阅{}", JSONObject.toJSONString(request));
        }
        return collegeTrainExamRecordService.startReviewing(request);
    }

    @ApiOperation(value = "查询已批阅考试列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getApprovedPageList")
    public JsonPagedVO<CollegeTrainExamRecordVo> getApprovedPageList(@RequestBody CollegeTrainExamRecordRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询已批阅考试记录{}", JSONObject.toJSONString(request));
        }
        if (request.getExamStartTime()!=null&&!"".equals(request.getExamStartTime())){
            request.setExamStartTime(request.getExamStartTime() + " 00:00:00");
            request.setExamEndTime(request.getExamEndTime() + " 23:59:59");
        }
        return collegeTrainExamRecordService.getApprovedPageList(request);
    }

    @ApiOperation(value = "工程结业理论考试列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getEngineeringPageList")
    public JsonPagedVO<List<CollegeTrainExamRecordVo>> getEngineeringPageList(@RequestBody CollegeTrainExamRecordRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询工程结业理论考试记录{}", JSONObject.toJSONString(request));
        }
        return collegeTrainExamRecordService.getEngineeringPageList(request);
    }

    @ApiOperation(value = "主观题评分管理数据导入")
    @PostMapping("importEvaluateDetail")
    public Rest importEvaluateDetail(@RequestParam("file") MultipartFile file) throws IOException {
        String s = collegeTrainExamRecordService.processExcelFile(file);
        if("文件上传成功".equals(s)){
            return RestBody.ok(s);
        }else {
            return RestBody.failure(s);
        }
    }

    @ApiOperation(value = "主观题评分管理模板下载")
    @PostMapping("downloadSubjectiveExcel")
    public void downloadEvaluateDetail(HttpServletResponse response) throws IOException {
        collegeTrainExamRecordService.downloadSubjectiveExcel(response);
    }

    @ApiOperation(value = "主观题评分管理数据导出")
    @PostMapping("exportSubjectiveExcel")
    public void exportSopEvaluateDetail(HttpServletResponse response) throws IOException {
        collegeTrainExamRecordService.exportSubjectiveExcel(response);
    }

    @IgnoreSysLog
    @ApiOperation(value = "工程结业理论考试模板", notes = "工程结业理论考试模板")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("downloadEngineeringTemplate")
    public void downloadEngineeringTemplate(HttpServletResponse response) throws IOException {
        collegeTrainExamRecordService.downloadEngineeringTemplate(response);
    }

    @IgnoreSysLog
    @ApiOperation(value = "批量分配阅卷师模板", notes = "批量分配阅卷师模板")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        collegeTrainExamRecordService.downloadTemplate(response);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导出", notes = "导出")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("export")
    public void export(@RequestBody CollegeTrainExamRecordRequest request,HttpServletResponse response) {
        collegeTrainExamRecordService.export(request,response);
    }

    @ApiOperation(value = "分配阅卷师", notes = "分配阅卷师")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "数据id", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "judgeNo", value = "阅卷师工号", required = true, dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "judgeName", value = "阅卷师名称", required = true, dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "examName", value = "考试名称", required = true, dataType = "string")
    })
    @PostMapping("assignExaminer")
    public ResultVO assignExaminer(@RequestBody CollegeTrainExamRecordRequest request) {
        collegeTrainExamRecordService.assignExaminer(request);
        return ResultVO.success();
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入分配阅卷师模板", notes = "导入分配阅卷师模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("importTemplate")
    public ResultVO importTemplate(@RequestParam MultipartFile file) throws IOException {
        return collegeTrainExamRecordService.importTemplate(file);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入工程结业理论考试模板", notes = "导入工程结业理论考试模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("importEngineeringTemplate")
    public ResultVO importEngineeringTemplate(@RequestParam MultipartFile file) throws IOException {
        return collegeTrainExamRecordService.importEngineeringTemplate(file);
    }

    @ApiOperation(value = "获取考试编号、考试名称 ,批阅编号下拉")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "column", value = "考试编号:EXAM_NUMBER ,考试名称:EXAM_NAME, 批阅编号:JUDGE_NUMBER", required = true)
    })
    @GetMapping("getSpecifiedColumn")
    public ResultVO getSpecifiedColumn(@RequestParam("column") String column) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(column, "column必传字段未传");
        List<String> strings = Arrays.asList("EXAM_NUMBER", "EXAM_NAME", "JUDGE_NUMBER");
        if (!strings.contains(column)) {
            throw new CustomerException("column 不合法", ResultCode.FAILED.getCode());
        }
        return collegeTrainExamRecordService.getSpecifiedColumn(column);
    }

}
