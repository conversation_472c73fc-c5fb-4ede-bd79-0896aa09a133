package com.htks.web.school.controller;

import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.exception.CustomerException;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.school.dto.CollegeSOPCondition;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainSopUpload;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.excel.CollegeEvaluateExcel;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.request.CollegeTrainSopUploadRequest;
import com.htks.domain.school.request.SopEvaluateRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainSopUploadService;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.school.vo.CollegeTrainSopUploadVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.domain.student.service.ExamService;
import com.htks.domain.student.service.SatisfactionDegreeService;
import com.htks.domain.student.service.UploadPostCardService;
import com.htks.web.JsonPagedVO;
import com.htks.web.Rest;
import com.htks.web.RestBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 大学生培训系统 SOP上传/读后感上传 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainSopUpload")
@Slf4j
@Api(tags = {"sop-读后感评价管理"})
public class CollegeTrainSopUploadController {
    @Resource
    SatisfactionDegreeService satisfactionDegreeService;
    @Resource
    private CollegeTrainSopUploadService collegeTrainSopUploadService;
    @Resource
    private UploadPostCardService uploadPostCardService;
    @Resource
    private ExamService examService;
    @Resource
    private CommonEmployeeService commonEmployeeService;
    @Resource
    private UploadWorkLicense uploadWorkLicense;

    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;

    @ApiOperation(value = "查询(SOP)-(读后感)评价管理列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainSopUploadVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPageList")
    public JsonPagedVO<CollegeTrainSopUploadVo> getPageList(@RequestBody CollegeTrainSopUploadRequest request) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(request);
        CommonExceptionEnum.NOT_NULL.assertNotNull(request.getType(), "type必传");
        if (log.isDebugEnabled()) {
            log.debug("查询sop列表{}", JSONObject.toJSONString(request));
        }
        return collegeTrainSopUploadService.queryPaperList(request);
    }

    @IgnoreSysLog
    @ApiOperation(value = "sop批量分配阅卷师模板", notes = "sop批量分配阅卷师模板")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("downloadSopTemplate")
    public void downloadSopTemplate(HttpServletResponse response) throws IOException {
        collegeTrainSopUploadService.downloadSopTemplate(response);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导出", notes = "导出")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("export")
    public void export(@RequestBody CollegeTrainSopUploadRequest request, HttpServletResponse response) {
        collegeTrainSopUploadService.export(request, response);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入SOP分配阅卷师模板", notes = "导入SOP分配阅卷师模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("importTemplate")
    public ResultVO importTemplate(@RequestParam MultipartFile file) throws IOException {
        return collegeTrainSopUploadService.importTemplate(file);
    }

    @ApiOperation(value = "分配阅卷师", notes = "分配阅卷师")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "数据id", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "judgeNo", value = "阅卷师工号", required = true, dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "judgeName", value = "阅卷师名称", required = true, dataType = "string"),
            @ApiImplicitParam(paramType = "query", name = "examName", value = "考试名称", required = true, dataType = "string")
    })
    @PostMapping("assignExaminer")
    public ResultVO assignExaminer(@RequestBody CollegeTrainSopUploadRequest request) {
        collegeTrainSopUploadService.assignExaminer(request);
        return ResultVO.success();
    }

    @ApiOperation(value = "SOP评价", notes = "SOP评价")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("sopEvaluate")
    public ResultVO sopEvaluate(@RequestBody SopEvaluateRequest request) {
        collegeTrainSopUploadService.sopEvaluate(request);
        return ResultVO.success();
    }

    @IgnoreSysLog
    @ApiOperation(value = "读后感评价模板", notes = "读后感评价模板")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("downloadAfterReadingTemplate")
    public void downloadAfterReadingTemplate(HttpServletResponse response) throws IOException {
        collegeTrainSopUploadService.downloadAfterReadingTemplate(response);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入读后感评价模板", notes = "导入读后感评价模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("importAfterReadingTemplate")
    public ResultVO importAfterReadingTemplate(@RequestParam MultipartFile file, String employeeNo) throws IOException {
        return collegeTrainSopUploadService.importAfterReadingTemplate(file, employeeNo);
    }


    @IgnoreSysLog
    @ApiOperation(value = "读后感评价", notes = "读后感评价")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("updateReadAfterByHand")
    public ResultVO updateReadAfterByHand(@RequestParam String employeeNo, String graderNo, Integer score) throws IOException {
        CommonEmployee employeeInfo = commonEmployeeService.getEmployeeInfo( graderNo.substring(graderNo.length() - 5));

        CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(employeeNo);
        CollegeTrainSopUpload collegeTrainSopUpload = new CollegeTrainSopUpload();
        collegeTrainSopUpload.setGraderNo(graderNo);
        collegeTrainSopUpload.setGraderName(employeeInfo.getEmployeeName());
        collegeTrainSopUpload.setScore(score);
        collegeTrainSopUpload.setAssessedId(collegeTrainAssessed.getId());
        uploadWorkLicense.updateReadAfterByHand(collegeTrainSopUpload);
        return new ResultVO(1000, "查询成功");
    }


    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "SOP-读后感上传", notes = "SOP-读后感上传")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("insertSopUpload")
    public ResultVO insertSopUpload(@RequestBody CollegeTrainSopUpload collegeTrainSopUpload, @RequestParam String path) throws IOException {
        // 获取文件名
        try {
            Long access_id = satisfactionDegreeService.getAccessId(collegeTrainSopUpload.getAssessedId().toString());
            String fileName = path;
            //文件类型
            collegeTrainSopUpload.setAssessedId(access_id);
            String fileTypeA = "";
            int dotIndex = collegeTrainSopUpload.getFileName().lastIndexOf(".");
            if (dotIndex > 0 && dotIndex < collegeTrainSopUpload.getFileName().length() - 1) {
                fileTypeA = collegeTrainSopUpload.getFileName().substring(dotIndex + 1).toLowerCase();
            }

            AttachmentEntity attachmentEntity = new AttachmentEntity();
            attachmentEntity.setAttachmentName(collegeTrainSopUpload.getFileName());
            attachmentEntity.setAttachmentType(fileTypeA);
            attachmentEntity.setAttachmentPath(path);
            uploadPostCardService.addPostCardPicture(attachmentEntity, null,access_id);
            long id = examService.getAttachmentIdByPath(path);
            collegeTrainSopUpload.setAttachmentId(id);
            CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSopUpload);
            if (collegeTrainSopUpload.getType() == 1) {
                CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSopUpload.getSopName(), "sop名称必传");
            }
            CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSopUpload.getAssessedId(), "员工信息主键必传");
            CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSopUpload.getAttachmentId(), "附件必传");
            CommonExceptionEnum.NOT_NULL.assertNotNull(collegeTrainSopUpload.getType(), "类型必传");
            int sum = uploadWorkLicense.getSopSum(collegeTrainSopUpload.getAssessedId(), collegeTrainSopUpload.getType());
            String back = null;
            if (sum == 0) {
                uploadWorkLicense.insertSop(collegeTrainSopUpload);
                back = "上传成功";
            } else {
                uploadWorkLicense.deleteSop(id);
                back = "只能上传一次";
            }
            return new ResultVO(1000, "查询成功", back);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "自动插入失败，请联系后台管理员处理");
        }
    }

    @ApiOperation(value = "获取SOP名称下拉")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "column", value = "SOP名称:SOP_NAME", required = true)
    })
    @GetMapping("getSpecifiedColumn")
    public ResultVO getSpecifiedColumn(@RequestParam("column") String column) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(column, "column必传字段未传");
        List<String> strings = Arrays.asList("SOP_NAME");
        if (!strings.contains(column)) {
            throw new CustomerException("column 不合法", ResultCode.FAILED.getCode());
        }
        return collegeTrainSopUploadService.getSpecifiedColumn(column);
    }

    @ApiOperation(value = "获取SOP阅卷师")
    @GetMapping("getSopGrader")
    public ResultVO getSopGrader() {
        return collegeTrainSopUploadService.getSopGrader();
    }

    @ApiOperation(value = "分配SOP阅卷师")
    @PostMapping("assignmentSopGrader")
    public ResultVO assignmentSopGrader(@RequestParam String JudgeNo, String JudgeName, String employeeNo) {
        try {
            Date dateToday = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmSS");
            String currentTime = sdf.format(dateToday);
            CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(employeeNo);
            String number = "SOP" + currentTime;
            uploadWorkLicense.updateSop(JudgeNo, JudgeName, collegeTrainAssessed.getId(), number);
            return new ResultVO(1000, "成功");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "失败，请联系后台管理员处理");
        }
    }



    @ApiOperation(value = "获取SOP截止上传时间")
    @PostMapping("getSopEndTime")
    public ResultVO getSopEndTime(@RequestParam  String employeeNo) {
        try {
            String  employeeNumber = employeeNo.replaceAll("300", "");
            employeeNumber = employeeNumber.replaceAll("330", "");
            employeeNumber = employeeNumber.replaceAll("660", "");
           String time= uploadWorkLicense.getSopEndTime(employeeNumber);
            return new ResultVO(1000, "成功",time);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "失败，请联系后台管理员处理");
        }
    }
    @ApiOperation(value = "获取读后感截止上传时间")
    @PostMapping("getReadAfterEndTime")
    public ResultVO getReadAfterEndTime(@RequestParam  String employeeNo) {
        try {
           String  employeeNumber = employeeNo.replaceAll("300", "");
            employeeNumber = employeeNumber.replaceAll("330", "");
            employeeNumber = employeeNumber.replaceAll("660", "");
            String time= uploadWorkLicense.getReadAfterEndTime(employeeNumber);
            return new ResultVO(1000, "成功",time);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "失败，请联系后台管理员处理");
        }
    }

    @ApiOperation(value = "SOP评价明细数据管理")
    @PostMapping("queryEvaluateDetail")
    public JsonPagedVO queryEvaluateDetail(@RequestBody CollegeSOPCondition condition){
        return collegeTrainSopUploadService.queryEvaluateDetail(condition);
    }


    @ApiOperation(value = "SOP评价明细数据导入")
    @PostMapping("importEvaluateDetail")
    public Rest importEvaluateDetail(@RequestParam("file") MultipartFile file) throws IOException {
        String s = collegeTrainSopUploadService.processExcelFile(file);
        if("文件上传成功".equals(s)){
            return RestBody.ok(s);
        }else {
            return RestBody.failure(s);
        }
    }

    @ApiOperation(value = "SOP评价明细数据模板下载")
    @PostMapping("downloadEvaluateDetail")
    public void downloadEvaluateDetail(HttpServletResponse response) throws IOException {
//        collegeTrainSopUploadService.downloadEvaluateDetail(response);
        collegeTrainSopUploadService.downloadEvaluateDetailXlsx(response);
    }

    @ApiOperation(value = "SOP评价明细数据导出")
    @PostMapping("exportEvaluateDetail")
    public void exportSopEvaluateDetail(HttpServletResponse response) throws IOException {
        collegeTrainSopUploadService.exportSopEvaluateDetail(response);
    }

    @ApiOperation(value = "SOP名称查询")
    @PostMapping("querySopNames")
    public Rest querySopNames()  {
        List<String> strings = collegeTrainSopUploadService.querySopNames();
        return RestBody.okData(strings);
    }
}
