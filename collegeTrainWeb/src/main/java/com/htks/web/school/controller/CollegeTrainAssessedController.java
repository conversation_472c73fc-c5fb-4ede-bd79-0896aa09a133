package com.htks.web.school.controller;

import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.exception.CustomerException;
import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.question.dto.PaperCondition;
import com.htks.domain.question.dto.PaperEntity;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.request.CollegeTrainAssessedRequest;
import com.htks.domain.school.request.CollegeTrainEnabledRequest;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainExamRecordService;
import com.htks.domain.school.vo.CollegeTrainAssessedVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.htks.common.utils.HttpServletUtils.downloadTemplate;

/**
 * <p>
 * 大学生培训系统 大学生信息配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RequestMapping("/collegeTrainAssessed")
@Slf4j
@Api(tags = {"大学生信息配置"})
@RestController
public class CollegeTrainAssessedController {
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @Resource
    private CollegeTrainExamRecordService collegeTrainExamRecordService;
@Resource
private UploadWorkLicense uploadWorkLicense;

    @IgnoreSysLog
    @ApiOperation(value = "下载大学生信息模板", notes = "下载大学生信息模板")
    @GetMapping (value = "downloadTemplate")
    public void downloadTemplate2(HttpServletResponse response) throws Exception {
        log.info("大学生信息配置模板");
        downloadTemplate(response, "大学生信息配置模板", "import/大学生信息配置模板.xlsx");
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入大学生信息模板", notes = "导入大学生信息模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("importTemplate")
    public ResultVO importTemplate(@RequestParam MultipartFile file) throws IOException {
        return collegeTrainAssessedService.importTemplate(file);
    }

    @ApiOperation(value = "新增大学生信息", notes = "新增大学生信息")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("addCollegeTrainAssessed")
    public ResultVO addCollegeTrainAssessed(@RequestBody CollegeTrainAssessed collegeTrainAssessed) {
        return collegeTrainAssessedService.addCollegeTrainAssessed(collegeTrainAssessed);
    }

    @ApiOperation(value = "编辑大学生信息", notes = "编辑大学生信息")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("updateCollegeTrainAssessed")
    public ResultVO updateCollegeTrainAssessed(@RequestBody CollegeTrainAssessed collegeTrainAssessed) {
        return collegeTrainAssessedService.updateCollegeTrainAssessed(collegeTrainAssessed);
    }

    @ApiOperation(value = "查询大学生信息列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainAssessedVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPageList")
    public JsonPagedVO<CollegeTrainAssessedVo> getPageList(@RequestBody CollegeTrainAssessedRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询大学生信息{}", JSONObject.toJSONString(request));
        }
        return collegeTrainAssessedService.queryPaperList(request);
    }

    @ApiOperation(value = "通过员工号查询大学生信息")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainAssessedVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getByEmployeeNo/{employeeNo}")
    public ResultVO getByEmployeeNo(@PathVariable("employeeNo") String employeeNo) {
        CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(employeeNo);

       // collegeTrainAssessed.setPracticeDepartmentAreaId(uploadWorkLicense.getArea(employeeNo));
        return ResultVO.success(collegeTrainAssessed);
    }

    @ApiOperation(value = "获取批次,学员姓名,学员工号,师傅姓名,师傅工号下拉")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "column", value = "学员工号:EMPLOYEE_NO 批次:BATCH 学员姓名:EMPLOYEE_NAME 师傅姓名:MASTER_NAME 师傅工号:MASTER_NO ", required = true)
    })
    @GetMapping("getSpecifiedColumn")
    public ResultVO getSpecifiedColumn(@RequestParam("column") String column) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(column, "column必传字段未传");
        List<String> strings = Arrays.asList("EMPLOYEE_NO", "BATCH", "EMPLOYEE_NAME", "MASTER_NAME", "MASTER_NO", "CLASS_NO");
        if (!strings.contains(column)) {
            throw new CustomerException("column 不合法", ResultCode.FAILED.getCode());
        }
        return collegeTrainAssessedService.getSpecifiedColumn(column);
    }

    @ApiOperation(value = "删除大学生信息")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainAssessedVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("remove/{collegeTrainAssessedId}")
    public ResultVO remove(@PathVariable("collegeTrainAssessedId") String collegeTrainAssessedId) {
        return collegeTrainExamRecordService.delete(collegeTrainAssessedId);
    }
    @GetMapping("getEmployeeInfoName")
    public ResultVO getEmployeeInfo( String employeeNo) {
        return collegeTrainExamRecordService.getEmployeeInfo(employeeNo);
    }

    @ApiOperation(value = "大学生开启、关闭接口")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("updateEnabled")
    public ResultVO<Boolean> updateEnabled(@RequestBody CollegeTrainEnabledRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询大学生信息{}", JSONObject.toJSONString(request));
        }
        return collegeTrainAssessedService.updateEnabled(request);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导出", notes = "导出")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("export")
    public void export(@RequestBody CollegeTrainAssessedRequest request, HttpServletResponse response) {
//        CollegeTrainAssessedRequest request = new CollegeTrainAssessedRequest();
        collegeTrainAssessedService.export(request,response);
    }

}
