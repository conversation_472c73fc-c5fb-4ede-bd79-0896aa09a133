package com.htks.web.school.controller;

import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainAppointRecordDetail;
import com.htks.domain.school.request.CollegeTrainAppointRecordRequest;
import com.htks.domain.school.request.CollegeTrainMeetRequest;
import com.htks.domain.school.service.CollegeTrainMeetService;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.domain.school.vo.CollegeTrainMeetVo;
import com.htks.web.JsonPagedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 大学生培训系统 见面会 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainMeet")
@Slf4j
@Api(tags = {"见面会评价管理"})
public class CollegeTrainMeetController {
    @Resource
    private CollegeTrainMeetService collegeTrainMeetService;

    @IgnoreSysLog
    @ApiOperation(value = "见面会评价模板", notes = "见面会评价模板")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("downTemplate")
    public void downTemplate(HttpServletResponse response) throws IOException {
        collegeTrainMeetService.downTemplate(response);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导入见面会评价模板", notes = "导入见面会评价模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", dataType = "MultipartFile", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("importTemplate")
    public ResultVO importTemplate(@RequestParam MultipartFile file) throws IOException {
        return collegeTrainMeetService.importTemplate(file);
    }

    @ApiOperation(value = "见面会评价详情", notes = "见面会评价详情")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainAppointRecordDetail.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getMeetDetailDetail")
    public ResultVO getMeetDetail(@RequestParam("meetId") Long meetId) {
        return collegeTrainMeetService.getMeetDetail(meetId);
    }

    @ApiOperation(value = "查询见面会评价列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPageList")
    public JsonPagedVO<CollegeTrainMeetVo> getPageList(@RequestBody CollegeTrainMeetRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询见面会评价{}", JSONObject.toJSONString(request));
        }
        return collegeTrainMeetService.queryPaperList(request);
    }

}
