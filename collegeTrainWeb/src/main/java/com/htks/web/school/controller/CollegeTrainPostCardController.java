package com.htks.web.school.controller;

import com.alibaba.fastjson.JSONObject;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.PostCardEntity;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.request.CollegeTrainPostCardRequest;
import com.htks.domain.school.service.CollegeTrainPostCardService;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.domain.school.vo.CollegeTrainPostCardVo;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.JsonPagedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.optim.nonlinear.scalar.LineSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 大学生培训系统 上岗证 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainPostCard")
@Slf4j
@Api(tags = {"上岗证管理"})
public class CollegeTrainPostCardController {
    @Resource
    private CollegeTrainPostCardService collegeTrainPostCardService;

    @Resource
    private UploadWorkLicense uploadWorkLicense;
    @ApiOperation(value = "上岗证管理列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPageList")
    public JsonPagedVO<CollegeTrainPostCardVo> getPageList(@RequestBody CollegeTrainPostCardRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询上岗证管理列表{}", JSONObject.toJSONString(request));
        }
        return collegeTrainPostCardService.queryPaperList(request);
    }

    @ApiOperation(value = "上岗证明细")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPostDetail")
    public ResultVO getPageList(@RequestParam String employeeNo) {
        try {
     //     List<PostCardEntity> list=uploadWorkLicense.findAllPostCard(employeeNo);
            List<PostCardEntity> list=uploadWorkLicense.findAllPostCard1(employeeNo);
            for (PostCardEntity postCardEntity:list) {
                postCardEntity.setDepartment(uploadWorkLicense.findAllPostCard2(employeeNo));
                postCardEntity.setPostId(uploadWorkLicense.getpostCardId(postCardEntity.getPostArea(),postCardEntity.getPostName(),employeeNo));
                postCardEntity.setPath(uploadWorkLicense.findAllPostCard4(employeeNo,postCardEntity.getPostId()));

            }
            return new ResultVO(1000, "查询成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败，请联系后台管理员处理");
        }
        }

}
