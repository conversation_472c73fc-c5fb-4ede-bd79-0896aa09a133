package com.htks.web.school.controller;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainUser;
import com.htks.domain.school.enums.UserRoleTypeEnum;
import com.htks.domain.school.service.CollegeTrainUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 岗位认证系统 管理员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainUser")
@Slf4j
@Api(tags = {"管理用户"})
public class CollegeTrainUserController {
    @Resource
    private CollegeTrainUserService collegeTrainUserService;

    @GetMapping("getAllJudge")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainUser.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @ApiOperation(value = "获取所有阅卷师")
    public ResultVO getAllJudge(){
        List<CollegeTrainUser> list = collegeTrainUserService.getAllJudge(UserRoleTypeEnum.GRADER.getCode());
        return ResultVO.success(list);
    }

    @GetMapping("handleFile")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainUser.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @ApiOperation(value = "处理文件转预览文件地址")
    public ResultVO handleFile(String filePath){
        String path = collegeTrainUserService.handleFile(filePath);
        return new  ResultVO(1000,"操作成功",path);
    }

}
