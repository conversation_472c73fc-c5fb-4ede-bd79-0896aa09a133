package com.htks.web.school.controller;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.service.CollegeTrainExamRecordAnswerService;
import com.htks.domain.school.vo.CollegeTrainAssessedVo;
import com.htks.domain.school.vo.CollegeTrainExamRecordAnswerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 岗位认证系统 考试记录答案表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainExamRecordAnswer")
@Slf4j
@Api(tags = {"学员考试记录答案"})
public class CollegeTrainExamRecordAnswerController {
    @Resource
    private CollegeTrainExamRecordAnswerService collegeTrainExamRecordAnswerService;

    @ApiOperation(value = "获取试卷详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "examId", required = true),
            @ApiImplicitParam(paramType = "query", name = "questionType", value = "(获取所有试题,此值不传)试题类型 1 单选题	2 多选题	3 判断题	4 填空题	5 主观题",required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainExamRecordAnswerVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("getExamRecordAnswer")
    public ResultVO getExamRecordAnswer(@RequestParam("examId") Long examId, @RequestParam("questionType") String questionType) {
        return ResultVO.success(collegeTrainExamRecordAnswerService.getExamRecordAnswer(examId,questionType));
    }



}
