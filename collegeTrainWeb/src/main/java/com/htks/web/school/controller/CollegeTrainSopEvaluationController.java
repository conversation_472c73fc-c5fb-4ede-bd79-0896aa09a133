package com.htks.web.school.controller;

import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.SopDetailEntity;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.dto.CollegeTrainSopEvaluation;
import com.htks.domain.school.service.CollegeTrainAssessedService;
import com.htks.domain.school.service.CollegeTrainSopEvaluationService;
import com.htks.domain.school.service.CommonEmployeeService;
import com.htks.domain.student.repository.hana.UploadWorkLicense;
import com.htks.web.sys.vo.TokenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 大学生 编写SOP等体系主观题评价表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainSopEvaluation")
@Slf4j
@Api(tags = {"主观题评价标准"})
public class CollegeTrainSopEvaluationController {
    @Resource
     private UploadWorkLicense uploadWorkLicense;
    @Resource
    private CollegeTrainSopEvaluationService collegeTrainSopEvaluationService;
    @Resource
    private CommonEmployeeService commonEmployeeService;
    @Resource
    private CollegeTrainAssessedService collegeTrainAssessedService;
    @ApiOperation(value = "获取评分标准", notes = "获取评分标准")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "1:SOP评分标准 2：异常实操评分标准", required = true, dataType = "int")})
    @ApiResponses({@ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainSopEvaluation.class),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getList")
    public ResultVO getList(@Param("type") Integer type) {
        CommonExceptionEnum.NOT_NULL.assertNotNull(type,"type必传字段未传");
        return collegeTrainSopEvaluationService.getList(type);
    }

    @ApiOperation(value = "获取分数详情")
    @PostMapping("getSopDetail")
    public ResultVO getSopDetail(@RequestParam String employeeNo) {
       try {
           CollegeTrainAssessed collegeTrainAssessed = collegeTrainAssessedService.getByEmployeeNo(employeeNo);
           SopDetailEntity sopDetailEntity=uploadWorkLicense.getSopDetail(collegeTrainAssessed.getId());
           return new ResultVO(1000, "查询成功", sopDetailEntity);
       } catch (Exception e) {
           e.printStackTrace();
           return new ResultVO(1001, "查询失败，请联系后台管理员处理");
       }
    }


}
