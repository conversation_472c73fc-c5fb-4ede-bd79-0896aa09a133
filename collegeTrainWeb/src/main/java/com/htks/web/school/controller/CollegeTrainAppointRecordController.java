package com.htks.web.school.controller;

import com.alibaba.fastjson.JSONObject;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainAppointRecordDetail;
import com.htks.domain.school.repository.hana.CollegeTrainAppointRecordMapper;
import com.htks.domain.school.request.CollegeTrainAppointRecordRequest;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.service.CollegeTrainAppointRecordService;
import com.htks.domain.school.vo.CollegeTrainAppointRecordVo;
import com.htks.domain.school.vo.CollegeTrainExamRecordVo;
import com.htks.web.JsonPagedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 大学生培训系统 预约记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/collegeTrainAppointRecord")
@Slf4j
@Api(tags = {"预约记录管理"})
public class CollegeTrainAppointRecordController {

    @Autowired
    private CollegeTrainAppointRecordService collegeTrainAppointRecordService;


    @ApiOperation(value = "查询预约记录列表")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainExamRecordVo.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getPageList")
    public JsonPagedVO<CollegeTrainAppointRecordVo> getPageList(@RequestBody CollegeTrainAppointRecordRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("查询预约记录{}", JSONObject.toJSONString(request));
        }
        if (request.getAppointEndTime() != null && request.getAppointEndTime() != "") {

            request.setAppointStartDate(request.getAppointStartTime().substring(0, 10));
            request.setAppointStartTime(request.getAppointStartTime().substring(11, 19));

            request.setAppointEndDate(request.getAppointEndTime().substring(0, 10));
            request.setAppointEndTime(request.getAppointEndTime().substring(11, 19));
        }
        return collegeTrainAppointRecordService.queryPaperList(request);
    }

    @IgnoreSysLog
    @ApiOperation(value = "导出", notes = "导出")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功"),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("export")
    public void export(@RequestBody CollegeTrainAppointRecordRequest request, HttpServletResponse response) {
        if (request.getAppointEndTime()!=null) {

            request.setAppointStartDate(request.getAppointStartTime().substring(0, 10));
            request.setAppointStartTime("00:00:00");

            request.setAppointEndDate(request.getAppointEndTime().substring(0, 10));
            request.setAppointEndTime("23:59:59");
        }
        collegeTrainAppointRecordService.export(request,response);
    }

    @ApiOperation(value = "预约记录详情", notes = "预约记录详情")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功",response = CollegeTrainAppointRecordDetail.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getAppointRecordDetail")
    public ResultVO getAppointRecordDetail(@RequestParam("appointRecordId") Long appointRecordId) {
        return collegeTrainAppointRecordService.getAppointRecordDetail(appointRecordId);
    }


    @ApiOperation(value = "预约记录退回接口")
    @PostMapping("backAppointRecode")
    public ResultVO backAppointRecode(@RequestParam("appointRecordId") Long appointRecordId){
        // 根据ID获取记录，判断考核状态
        return collegeTrainAppointRecordService.backAppointRecode(appointRecordId);
    }


}
