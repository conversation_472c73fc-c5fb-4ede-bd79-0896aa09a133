package com.htks.web.school.controller;

import com.htks.common.external.wx.ResultVO;
import com.htks.domain.school.dto.CollegeTrainDepartment;
import com.htks.domain.school.dto.CollegeTrainPostArea;
import com.htks.domain.school.dto.CommonEmployee;
import com.htks.domain.school.service.CollegeTrainPostAreaService;
import com.htks.web.common.vo.ByDepartmentListVO;
import com.htks.web.sys.vo.TokenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门区域对应表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@Api(tags = {"选岗部门-区域"})
@RequestMapping("/collegeTrainPostArea")
public class CollegeTrainPostAreaController {

    @Resource
    private CollegeTrainPostAreaService collegeTrainPostAreaService;


    @ApiOperation(value = "获取所有部门", notes = "获取所有部门")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = String.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("getAllDepartment")
    public ResultVO getAllDepartment(String type, String factoryFlag) {
        List<String> practiceDepartmentList = collegeTrainPostAreaService.getAllDepartment(factoryFlag);
        type="2";
        if (type.equals("1")){
            practiceDepartmentList.add("资讯技术部");
        }
        return ResultVO.success(practiceDepartmentList);
    }
    @GetMapping("getAllDepartmentId")
    public ResultVO getAllDepartmentId() {
        List<CollegeTrainDepartment> practiceDepartmentList = collegeTrainPostAreaService.getCollegeTrainDepartment();

        return ResultVO.success(practiceDepartmentList);
    }
    @ApiOperation(value = "通过部门获取区域", notes = "通过部门获取区域")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainPostArea.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @GetMapping("getAreaByDepartment")
    public ResultVO getAreaByDepartment(@RequestParam(value="department") String department,String employeeNo) {
        List<CollegeTrainPostArea> collegeTrainPostAreaList = collegeTrainPostAreaService.getAreaByDepartment(department,employeeNo);
        return ResultVO.success(collegeTrainPostAreaList);
    }

    @ApiOperation(value = "通过多部门获取多区域", notes = "通过多部门获取多区域")
    @ApiResponses({
            @ApiResponse(code = 1000, message = "操作成功", response = CollegeTrainPostArea.class),
            @ApiResponse(code = 1001, message = "操作失败"),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @PostMapping("getByDepartmentList")
    public ResultVO getAreaByDepartment(@RequestBody ByDepartmentListVO byDepartmentListVO) {
        List<CollegeTrainPostArea> collegeTrainPostAreaList = collegeTrainPostAreaService.getByDepartmentListNew(byDepartmentListVO.getDepartmentList(),byDepartmentListVO.getFactoryFlagList());
        return ResultVO.success(collegeTrainPostAreaList);
    }

}
