package com.htks.web.common.vo;

import com.google.common.base.MoreObjects;
import com.htks.web.AbstractVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 附件vo
 *
 * <AUTHOR>
 * @date 2022/9/28.
 */
@Getter
@Setter
@ApiModel("AttachmentVO")
public class AttachmentVO extends AbstractVO {
    private static final long serialVersionUID = 7897631874544260434L;

    @ApiModelProperty(value = "附件ID")
    private Long id;

    @ApiModelProperty(value = "附件名称", example = "test.png")
    private String attachmentName;

    @ApiModelProperty(value = "附件类型")
    private String attachmentType;

    @ApiModelProperty(value = "附件路径", example = "/home/<USER>")
    private String attachmentPath;


    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("id", id)
                .add("attachmentName", attachmentName)
                .add("attachmentType", attachmentType)
                .add("attachmentURI", attachmentPath)
                .toString();
    }
}
