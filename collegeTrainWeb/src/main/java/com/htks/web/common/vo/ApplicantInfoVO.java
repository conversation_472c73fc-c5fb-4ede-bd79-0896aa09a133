package com.htks.web.common.vo;

import com.google.common.base.MoreObjects;
import com.htks.web.AbstractVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 公共 - 员工信息VO
 *
 * <AUTHOR>
 * @date 2022/10/10.
 */
@Getter
@Setter
@ApiModel("ApplicantInfoVO")
public class ApplicantInfoVO extends AbstractVO {

    private static final long serialVersionUID = -3108929215171502038L;

    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "工号")
    private String employeeNumber;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    @ApiModelProperty(value = "当前等级Code")
    private String currentGrade;

    @ApiModelProperty(value = "当前等级名称")
    private String currentGradeName;

    @ApiModelProperty(value = "下一等级Code")
    private String nextGrade;

    @ApiModelProperty(value = "下一等级C名称")
    private String nextGradeName;

    @ApiModelProperty(value = "职务")
    private String positionName;

    @ApiModelProperty(value = "事业部名称")
    private String businessUnitName;

    @ApiModelProperty(value = "事业部ID")
    private String businessUnitId;

    @ApiModelProperty(value = "区/组名称")
    private String classBanName;

    @ApiModelProperty(value = "区/组ID")
    private String classBanId;

    @ApiModelProperty(value = "职能名称")
    private String znName;

    @ApiModelProperty(value = "职能ID")
    private String znId;

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("employeeName", employeeName)
                .add("employeeNumber", employeeNumber)
                .add("departmentName", departmentName)
                .add("departmentId", departmentId)
                .add("departmentId", currentGrade)
                .add("departmentId", currentGradeName)
                .add("departmentId", nextGrade)
                .add("departmentId", nextGradeName)
                .add("departmentId", positionName)
                .add("positionName", businessUnitName)
                .add("positionName", businessUnitId)
                .add("positionName", classBanName)
                .add("positionName", classBanId)
                .add("positionName", znName)
                .add("positionName", znId)
                .toString();
    }
}
