package com.htks.web.common;

import com.github.pagehelper.util.StringUtil;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.FileService;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.common.dto.CommonRoleEntity;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.domain.common.dto.UpgradeDictionaryEntity;
import com.htks.domain.common.service.CommonService;
import com.htks.domain.student.dto.FileEntry;
import com.htks.web.common.mapping.CommonMapping;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.htks.web.WebURIMappingConstant.*;


/**
 * 公共
 *
 * <AUTHOR>
 * @date 2022/9/21.
 */
@Slf4j
@Api(tags = {"文件上传 "})
@RestController
public class UploadController {

    @Value(value = "${defineProps.file.dir.temp}")
    private String tempPath;

    @Value(value = "${defineProps.file.nginx-link}")
    private String nginxLink;

    @Resource
    private CommonMapping mapping;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FileService fileService;

    @IgnoreSysLog
    @ApiOperation("0101 - 上传文件")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/file/fileUpload")
    public ResultVO getEmployeeNo(@RequestParam("file") MultipartFile file)  {
        try {
            FileEntry fileEntry=commonService.fileUpload(file);
            return new ResultVO(1000, "查询成功",fileEntry);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001, "查询失败");
        }
    }
    @ApiOperation("0101 - 上传上岗证文件")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/file/filePostUpload")
    public ResultVO filePostUpload(@RequestParam("file") MultipartFile file,String employeeNo)  {
        try {
            FileEntry fileEntry=commonService.filePostUpload(file,employeeNo);
            return new ResultVO(1000, "查询成功",fileEntry);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001, "查询失败");
        }
    }
    @IgnoreSysLog
    @ApiOperation("0101 - 下载上岗证文件")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/file/downloadFile")
    public ResultVO filePostDown(@RequestParam("file") String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return new ResultVO(1001, "文件不存在");
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", file.getName());
            headers.setContentLength(file.length());

            ResponseEntity<byte[]> responseEntity;
            try (FileInputStream fis = new FileInputStream(file)) {
                byte[] data = new byte[(int) file.length()];
                fis.read(data);
                responseEntity = new ResponseEntity<>(data, headers, HttpStatus.OK);
            } catch (IOException e) {
                e.printStackTrace();
                return new ResultVO(1001, "文件下载失败");
            }

            return  new ResultVO(1000, "下载成功",responseEntity);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultVO(1001, "查询失败");
        }
    }
}
