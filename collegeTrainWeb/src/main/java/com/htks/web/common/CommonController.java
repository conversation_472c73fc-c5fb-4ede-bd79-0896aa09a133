package com.htks.web.common;

import com.github.pagehelper.util.StringUtil;
import com.htks.common.annotation.IgnoreSysLog;
import com.htks.common.external.FileService;
import com.htks.common.external.wx.ResultCode;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.common.dto.*;
import com.htks.domain.common.service.CommonService;
import com.htks.web.Rest;
import com.htks.web.RestBody;
import com.htks.web.common.mapping.CommonMapping;
import com.htks.web.common.vo.ApplicantInfoVO;
import com.htks.web.common.vo.AttachmentVO;
import com.htks.web.common.vo.RoleInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.google.common.collect.Lists.newArrayList;
import static com.htks.web.WebURIMappingConstant.*;


/**
 * 公共
 *
 * <AUTHOR>
 * @date 2022/9/21.
 */
@Slf4j
@Api(tags = {"Common - 公共"})
@RestController
public class CommonController {

    @Value(value = "${defineProps.file.dir.temp}")
    private String tempPath;

    @Value(value = "${defineProps.file.nginx-link}")
    private String nginxLink;

    @Resource
    private CommonMapping mapping;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FileService fileService;



    @IgnoreSysLog
    @ApiOperation("0101 - 附件下载")
    @PostMapping(URI_COMMON_DOWNLOAD)
    public void fileDownload(@RequestParam("fileFullPath")String fileFullPath, HttpServletResponse response) {
        log.info("附件下载路径：{}",fileFullPath);
        try {
            final String fileName = fileFullPath.substring(fileFullPath.lastIndexOf("/") + 1);
            response.setHeader("content-type", "application/octet-stream;");
            response.setContentType("application/octet-stream; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "iso8859-1"));

            byte[] bytes= fileService.readFile(fileFullPath);
            response.getOutputStream().write(bytes);
        } catch (IOException e) {
            log.error("附件下载，IOException:{}", e.getMessage());
        }
    }

    @IgnoreSysLog
    @ApiOperation("0102 - 上传")
    @PostMapping(URI_COMMON_FILE_UPLOAD)
    public ResultVO fileUpload(@RequestParam("file") MultipartFile file) {

        final AttachmentVO attachmentResult = new AttachmentVO();
        try {
            //原始文件名称
            final String originalFilename = file.getOriginalFilename();
            //文件类型
            String fileType;
            if (originalFilename != null) {
                fileType = originalFilename.substring(originalFilename.indexOf(".") + 1).toLowerCase();
                final String attachmentURI = tempPath + originalFilename;
                //写文件 - 存到临时文件路径
                fileService.write(file.getBytes(), tempPath, originalFilename);
                attachmentResult.setAttachmentName(originalFilename);
                attachmentResult.setAttachmentType(fileType.toUpperCase());
                attachmentResult.setAttachmentPath(attachmentURI);
                AttachmentEntity attachmentEntity = new AttachmentEntity();
                BeanUtils.copyProperties(attachmentEntity, attachmentResult);
                if(commonService.addAttachmentInfoToDB(attachmentEntity)){
                    attachmentResult.setId(attachmentEntity.getId());
                }
                else {
                    return new ResultVO(ResultCode.FILE_UPLOAD_ERROR,null);
                }
            }
        } catch (IOException e) {
            log.error("附件上传，IOException:{}", e.getMessage());
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        return new ResultVO(attachmentResult);
    }

    @ApiOperation("0103 - 查询(下拉框)参数")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "paramType", value = "级别:position_grade;", dataType = "string", example = "position_grade"),
    })
    @PostMapping(URI_COMMON_GET_PARAMETER_DEPT_DROPDOWN)
    public ResultVO getParameterDropDown(@RequestParam(value = "paramType", required = false) String paramType) {
        //获取下拉框选项数据信息
        final List<UpgradeDictionaryEntity> dicList = commonService.getDictionary(paramType);

        return new ResultVO(dicList);
    }

    @ApiOperation("0104 - 查询员工基本信息")
    @ApiResponses({@ApiResponse(code = 200, message = "操作成功", response = ApplicantInfoVO.class),
            @ApiResponse(code = 500, message = "未知异常,请联系管理员")}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "empNumber", value = "5位工号", dataType = "string", example = "30344"),
    })
    @PostMapping(URI_COMMON_GET_EMPLOYEE_BASE_INFO)
    public ResultVO getEmployeeBaseInfo(@RequestParam(value = "empNumber") String empNumber) {

        //获取员工基本信息
        final EmployeeEntity employeeBaseInfo = commonService.getEmployeeBaseInfo(empNumber);
        if(employeeBaseInfo==null ){
            return new ResultVO(ResultCode.NO_RESULT,"输入工号错误");
        }
        //dto转vo
        final ApplicantInfoVO applicantInfoVO = mapping.applicantInfoDtoToVo(employeeBaseInfo);
        if(StringUtil.isEmpty(employeeBaseInfo.getPositionGrade())){
            applicantInfoVO.setCurrentGrade(null);
            applicantInfoVO.setCurrentGradeName(null);
            applicantInfoVO.setNextGrade("E4-5");
            applicantInfoVO.setNextGradeName("初A");
            return new ResultVO(applicantInfoVO);
        }

        List<UpgradeDictionaryEntity> dictionaryEntities = commonService.getDictionary("position_grade");
        List<UpgradeDictionaryEntity> upgradeDictionaryEntities = new ArrayList<>();
        for(UpgradeDictionaryEntity entity : dictionaryEntities){
            for(String grade : Arrays.asList(entity.getName2().split(",")) ){
                if(grade.equals(employeeBaseInfo.getPositionGrade())){
                    upgradeDictionaryEntities.add(entity);
                }
            }
        }
        UpgradeDictionaryEntity positionDicResult = null;
        if(!CollectionUtils.isEmpty(upgradeDictionaryEntities)){
            positionDicResult = upgradeDictionaryEntities.get(0);
        }
//        UpgradeDictionaryEntity positionDicResult =  dictionaryEntities.stream().filter(p->p.getName2().contains(employeeBaseInfo.getPositionGrade())).findFirst().get();
        if(positionDicResult != null){
            applicantInfoVO.setCurrentGrade(positionDicResult.getCode());
            applicantInfoVO.setCurrentGradeName(positionDicResult.getName());
            Long nextId = positionDicResult.getId()+1;
            UpgradeDictionaryEntity nextPositionDicResult =  dictionaryEntities.stream().filter(p->p.getId()==nextId).findFirst().get();
            if(nextPositionDicResult != null){
                applicantInfoVO.setNextGrade(nextPositionDicResult.getCode());
                applicantInfoVO.setNextGradeName(nextPositionDicResult.getName());
            }
        }else{
            applicantInfoVO.setNextGrade("E4-5");
            applicantInfoVO.setNextGradeName("初级");
        }
        return new ResultVO(applicantInfoVO);
    }

    @ApiOperation(value = "0106 - 角色列表")
    @GetMapping(value = URI_COMMON_GET_COMMON_ROLE_INFO)
    public ResultVO getCommonRoleInfo() throws InvocationTargetException, IllegalAccessException {
        List<CommonRoleEntity> roleEntityList =  commonService.getCommonRoleInfo("岗位资格");
        if(roleEntityList !=null && !roleEntityList.isEmpty()){
            List<RoleInfoVO> resultListVO = new ArrayList<>();
            for (CommonRoleEntity item: roleEntityList) {
                RoleInfoVO temp = new RoleInfoVO();
                BeanUtils.copyProperties(temp, item);
                resultListVO.add(temp);
            }
            return new ResultVO(resultListVO);
        }
        return new ResultVO(ResultCode.FAILED,"");
    }



    @ApiOperation("0107 - 根据企业微信号获取工号")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/evaluate/getEmployeeNo")
    public ResultVO getEmployeeNo(@RequestParam String userId)  {
        try {
            if (userId.equals("sunsong-chanpingongchengbu-32560")){
                return new ResultVO(1000, "查询成功","32560");
            }
            String employeeNo=commonService.getEmployeeNoByUserId(userId);
            return new ResultVO(1000, "查询成功",employeeNo);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001, "查询失败");
        }
    }
    @ApiOperation("0108 - 根据工号获取角色")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/evaluate/geyRoleByEmployeeNo")
    public ResultVO geyRoleByEmployeeNo(@RequestParam String employeeNo)  {
        try {
            List<String> roleId=commonService.geyRoleByEmployeeNo(employeeNo);
            String returnBack="0";
            if(roleId==null||roleId.size()==0){
                List<String> role=new ArrayList<>();
                Integer sum=commonService.getSumByCollegeNo(employeeNo);
                if(sum==1){
                    role.add("大学生");
                }else {
                    role.add("未授权人员");
                }
                return new ResultVO(1000, "查询成功",role);
            }
            else {
                String role=roleId.get(0);
                String[]id=role.split(",");
                roleId.clear();
                for (int i = 0; i < id.length; i++) {
                    switch (id[i]) {
                        case "100":
                            roleId.add("管理员");
                            break;
                        case "101":
                            roleId.add("阅卷师");
                            break;
                        case "102":
                            roleId.add("培训组组长");
                            break;
                }
                }
            }
            return new ResultVO(1000, "查询成功",roleId);
        }catch (Exception e){
            e.printStackTrace();
            return new ResultVO(1001, "查询失败");
        }
    }


}
