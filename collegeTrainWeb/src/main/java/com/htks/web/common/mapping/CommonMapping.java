package com.htks.web.common.mapping;

import com.htks.domain.common.dto.AttachmentEntity;
import com.htks.domain.common.dto.EmployeeEntity;
import com.htks.web.common.vo.ApplicantInfoVO;
import com.htks.web.common.vo.AttachmentVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * MapStruct 转换 Mapper
 * <p>
 * 注：源类型与目标类型的成员变量相同类型相同变量名不用写映射
 *
 * <AUTHOR>
 * @date 2022/10/10.
 */

@Mapper(componentModel = "spring")
public interface CommonMapping {

    /**
     * y员工信息 - Employee映射
     *
     * @param dto dto
     * @return vo
     */
    ApplicantInfoVO applicantInfoDtoToVo(EmployeeEntity dto);

    /**
     * 附件 - Attachment映射
     *
     * @param vo vo
     * @return dto
     */
    List<AttachmentEntity> attachmentListVoToDto(List<AttachmentVO> vo);
}
