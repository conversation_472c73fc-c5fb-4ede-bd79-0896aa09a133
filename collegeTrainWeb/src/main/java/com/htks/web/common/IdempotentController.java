package com.htks.web.common;

import com.htks.common.external.wx.ResultVO;
import com.htks.common.utils.IdempotentUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 幂等性相关接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Api(tags = {"幂等性管理"})
@Slf4j
@RestController
@RequestMapping("/idempotent")
public class IdempotentController {
    
    private final IdempotentUtils idempotentUtils;
    
    @Autowired
    public IdempotentController(IdempotentUtils idempotentUtils) {
        this.idempotentUtils = idempotentUtils;
    }
    
    @ApiOperation("获取幂等性token")
    @PostMapping("/token")
    public ResultVO getIdempotentToken(@RequestParam String businessKey) {
        try {
            String token = idempotentUtils.generateToken(businessKey);
            return new ResultVO(1000, "获取token成功", token);
        } catch (Exception e) {
            log.error("获取幂等性token失败", e);
            return new ResultVO(1001, "获取token失败，请联系管理员");
        }
    }
}
