package com.htks;

import static com.htks.common.SystemConfig.DATE_FORMAT_DEFAULT;
import static org.springframework.boot.WebApplicationType.SERVLET;

import javax.annotation.Resource;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

/**
 * spring boot启动类
 *
 * <AUTHOR>
 * @date 2020/09/18
 */
@Slf4j
@SpringBootApplication
@EnableTransactionManagement
@EnableScheduling
public class App extends SpringBootServletInitializer implements TransactionManagementConfigurer {

  private static final Marker MARKER = MarkerFactory.getMarker(log.getName());

  @Resource(name = "txManager")
  private PlatformTransactionManager txManager;

  /**
   * 创建事务管理器
   */
  @Bean(name = "txManager")
  public PlatformTransactionManager txManager(DataSource dataSource) {
    return new DataSourceTransactionManager(dataSource);
  }

  @NonNull
  @Override
  public PlatformTransactionManager annotationDrivenTransactionManager() {
    return txManager;
  }

  public static void main(String[] args) {
    new SpringApplicationBuilder(App.class).web(SERVLET).run();

    final String now = DateTime.now().toString(DATE_FORMAT_DEFAULT);
    log.info(MARKER, "(大学生培训系统-Mis后端)于{}启动成功...............", now);
  }

  @Override
  protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
    return application.sources(App.class);
  }
}
