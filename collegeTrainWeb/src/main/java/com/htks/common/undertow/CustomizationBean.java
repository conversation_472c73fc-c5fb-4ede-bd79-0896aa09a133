package com.htks.common.undertow;

import io.undertow.UndertowOptions;
import io.undertow.server.DefaultByteBufferPool;
import io.undertow.websockets.jsr.WebSocketDeploymentInfo;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 设置 Undertow(嵌入式 Web 服务器)的 Buffer Pool
 *
 * <AUTHOR>
 * @date 2021/08/20.
 */
@Component
public class CustomizationBean implements
    WebServerFactoryCustomizer<UndertowServletWebServerFactory> {

  private static final int BUFFER_SIZE = 1024;

  private static final String UNDERTOW_NAME = "io.undertow.websockets.jsr.WebSocketDeploymentInfo";

  @Override
  public void customize(UndertowServletWebServerFactory factory) {
    factory.addDeploymentInfoCustomizers(deploymentInfo -> {
      final WebSocketDeploymentInfo webSocketDeploymentInfo = new WebSocketDeploymentInfo();
      webSocketDeploymentInfo.setBuffers(new DefaultByteBufferPool(false, BUFFER_SIZE));
      deploymentInfo.addServletContextAttribute(UNDERTOW_NAME, webSocketDeploymentInfo);
    });
  }

  @Bean
  public UndertowServletWebServerFactory undertowServletWebServerFactory() {
    final UndertowServletWebServerFactory factory = new UndertowServletWebServerFactory();
    //开启undertow计时
    factory.addBuilderCustomizers(builder -> builder.setServerOption(UndertowOptions.RECORD_REQUEST_START_TIME, true));
    return factory;
  }
}
