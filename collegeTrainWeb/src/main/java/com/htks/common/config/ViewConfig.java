package com.htks.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;




/**
 * View配置
 *
 * <AUTHOR>
 * @date 2019/05/31.
 */
@Configuration
public class ViewConfig implements WebMvcConfigurer {
  
  @Override
  public void addViewControllers(ViewControllerRegistry registry) {
    registry.addViewController("/").setViewName("forward:/index.html");
    registry.setOrder(Ordered.HIGHEST_PRECEDENCE);
  }
  
  /**
   * 跨域支持
   * <p>
   * Access-Control-Allow-Origin：指定哪些域可以访问域资源
   * Access-Control-Allow-Credentials：指定浏览器是否将使用请求发送cookie。仅当allow-credentials标头设置为true时，才会发送Cookie
   * Access-Control-Allow-Methods：指定可以使用哪些HTTP请求方法（GET，PUT，DELETE等）来访问资源
   *
   * @param registry 跨域配置实体
   */
  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/**")
            // 是否允许证书（cookies）
            .allowCredentials(true)
            // 设置允许跨域请求的域名
            .allowedOriginPatterns("*")
            // 设置允许的方法
            .allowedMethods("GET", "POST", "DELETE", "PUT")
            .allowedHeaders("*")
            .exposedHeaders(HttpHeaders.CONTENT_DISPOSITION)
            // 跨域允许时间
            .maxAge(3600 * 24L);
  }
}
