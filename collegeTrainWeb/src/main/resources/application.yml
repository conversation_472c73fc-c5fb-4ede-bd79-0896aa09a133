spring:
  jmx.default-domain: collegeTrainBack
  # 环境 dev|qa|uat|prod/outer
  profiles:
    active: prod
  web:
    # 指定静态资源的路径
    resources:
      static-locations: classpath:/static/,classpath:/views/
  messages:
    encoding: UTF-8
    fallback-to-system-locale: false
    use-code-as-default-message: true
    basename: i18n/messages
  main:
    lazy-initialization: false #默认false 关闭
  jackson:
    time-zone: Asia/Shanghai
  servlet:
    multipart:
      max-file-size: 128MB
      max-request-size: 128MB

  # Mybatis配置  （注：多数据源时，以下配置不可用）
  #mybatis:
  #mapperLocations: classpath:mapper/**/*.xml # 配置mapper扫描路径
  #configLocation: classpath*:mybatis.xml # 加载全局配置文件
  #type-aliases-package: com.htks.domain.**.dto # 扫描指定包的别名

# 日志
logging:
  file:
    path: /home/<USER>/collegeTrainSystem/
    name: ${logging.file.path}collegeTrainBack

# enable swagger2
swagger2:
  enable: true

# pom的版本号（需要配置pom中<filtering>true</filtering>）
version: @project.version@
systemIdentifier: @project.parent.artifactId@

# Redis配置
redis:
  database: 0
  host: ***********
  port: 6379
  password: root      # 密码（默认为空）
  timeout: 6000         # 连接超时时长（毫秒）
  jedis:
    pool:
      max-active: 16  # 连接池最大连接数（使用负值表示没有限制）
      max-idle: 8      # 连接池中的最大空闲连接
      min-idle: 2       # 连接池中的最小空闲连接
      max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）

#邮件配置
email:
  username: it.dev01
  password: Ks@123456
  host: ksmail.ht-tech.com
  port: 587
  protocol: smtp
  defaultEncoding: UTF-8
  senderName: <EMAIL>


#自定义参数
defineProps:
  kick-out-other: false #根据工号踢掉当前IP之外的其他登录者
  file:
    nginx-link: http://************:8666
    dir:   #附件上传路径
      root: /home/<USER>/collegeTrainSystem/
      temp: ${defineProps.file.dir.root}tempFile/
      final:  ${defineProps.file.dir.root}finalFile/
FileUrl:
  Preview: http://**************:8012/AesEncrypted?value=

FileUpload: http://**************:9020/htfs/htfsServerFiles/upload
