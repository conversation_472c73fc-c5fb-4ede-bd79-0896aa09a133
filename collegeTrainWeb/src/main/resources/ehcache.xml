<ehcache name="shiroCache">
    <diskStore path="java.io.tmpdir"/>

    <!-- 配置项说明 -->
    <!--name：缓存名称-->
    <!--maxEntriesLocalHeap:堆内存中最大缓存对象数，0没有限制-->
    <!--eternal：缓存中对象是否永久有效，如果为true，timeouts将被忽略，对象将永不过期-->
    <!--timeToIdleSeconds：如果超过设置时间未访问，那么此缓存失效，当eternal为false时，这个属性才有效，0为不限制-->
    <!--timeToLiveSeconds：最多可以存活时间，当eternal为false时，这个属性才有效，0为不限制-->
    <!--overflowToDisk：内存不足时，是否启用磁盘缓存。-->
    <!--statistics：是否收集统计信息-->
    <!--diskPersistent 是否在VM重启时存储硬盘的缓存数据-->
    <!--diskExpiryThreadIntervalSeconds 磁盘失效线程运行时间间隔-->

    <defaultCache
            maxElementsInMemory="10000"
            eternal="false"
            overflowToDisk="false"
            timeToIdleSeconds="600"
            timeToLiveSeconds="600"
            diskPersistent="false"
            diskExpiryThreadIntervalSeconds="120"
    />

    <!-- 授权缓存,缓存超过30分钟未访问 那么此缓存失效！ -->
    <cache name="authorizationCache"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="0"
           overflowToDisk="false"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="120"
           statistics="false">
    </cache>

    <!-- 认证缓存 ,此缓存超过30分钟未访问 那么此缓存失效！-->
    <cache name="authenticationCache"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="0"
           overflowToDisk="false"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="120"
           statistics="false">
    </cache>

    <cache name="shiro-activeSessionCache"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="0"
           overflowToDisk="false"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="120"
           statistics="false">
    </cache>

</ehcache>