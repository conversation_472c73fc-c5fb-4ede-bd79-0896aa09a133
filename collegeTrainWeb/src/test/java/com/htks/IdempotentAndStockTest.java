package com.htks;

import com.htks.common.utils.DistributedLockUtils;
import com.htks.common.utils.IdempotentUtils;
import com.htks.common.utils.StockUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 幂等性和库存管理测试
 *
 * <AUTHOR> Generated
 * @date 2025/07/09
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = App.class)
@DisplayName("幂等性和库存管理测试")
public class IdempotentAndStockTest {

    @Autowired
    private IdempotentUtils idempotentUtils;

    @Autowired
    private StockUtils stockUtils;

    @Autowired
    private DistributedLockUtils distributedLockUtils;

    @Test
    @DisplayName("测试幂等性token生成和验证")
    public void testIdempotent() {
        String businessKey = "test_business";
        
        // 生成token
        String token = idempotentUtils.generateToken(businessKey);
        log.info("生成的token: {}", token);
        
        // 第一次验证应该成功
        boolean result1 = idempotentUtils.validateAndConsumeToken(businessKey, token);
        log.info("第一次验证结果: {}", result1);
        assert result1;
        
        // 第二次验证应该失败（token已被消费）
        boolean result2 = idempotentUtils.validateAndConsumeToken(businessKey, token);
        log.info("第二次验证结果: {}", result2);
        assert !result2;
    }

    @Test
    @DisplayName("测试分布式锁")
    public void testDistributedLock() throws InterruptedException {
        String lockKey = "test_lock";
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    String lockValue = distributedLockUtils.tryLock(lockKey, 10, 1000);
                    if (lockValue != null) {
                        log.info("线程 {} 获取锁成功", threadId);
                        successCount.incrementAndGet();
                        
                        // 模拟业务处理
                        Thread.sleep(100);
                        
                        // 释放锁
                        distributedLockUtils.releaseLock(lockKey, lockValue);
                        log.info("线程 {} 释放锁成功", threadId);
                    } else {
                        log.info("线程 {} 获取锁失败", threadId);
                    }
                } catch (Exception e) {
                    log.error("线程 {} 执行异常", threadId, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        log.info("成功获取锁的线程数: {}", successCount.get());
        // 在短时间内，应该只有少数线程能获取到锁
        assert successCount.get() < threadCount;
    }

    @Test
    @DisplayName("测试库存管理")
    public void testStock() {
        String stockKey = "test_stock";
        int initialStock = 100;
        
        // 初始化库存
        boolean initResult = stockUtils.initStock(stockKey, initialStock);
        log.info("初始化库存结果: {}", initResult);
        assert initResult;
        
        // 获取库存
        int currentStock = stockUtils.getStock(stockKey);
        log.info("当前库存: {}", currentStock);
        assert currentStock == initialStock;
        
        // 扣减库存
        int remainingStock = stockUtils.deductStock(stockKey, 10);
        log.info("扣减后库存: {}", remainingStock);
        assert remainingStock == initialStock - 10;
        
        // 增加库存
        int newStock = stockUtils.addStock(stockKey, 5);
        log.info("增加后库存: {}", newStock);
        assert newStock == initialStock - 10 + 5;
    }

    @Test
    @DisplayName("测试库存超卖防护")
    public void testStockOverselling() throws InterruptedException {
        String stockKey = "test_overselling";
        int initialStock = 10;
        int threadCount = 20;
        
        // 初始化库存
        stockUtils.initStock(stockKey, initialStock);
        
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    int result = stockUtils.deductStock(stockKey, 1);
                    if (result >= 0) {
                        successCount.incrementAndGet();
                        log.info("线程 {} 扣减库存成功，剩余: {}", threadId, result);
                    } else {
                        failCount.incrementAndGet();
                        log.info("线程 {} 扣减库存失败，原因: {}", threadId, 
                                result == -1 ? "库存不存在" : "库存不足");
                    }
                } catch (Exception e) {
                    log.error("线程 {} 执行异常", threadId, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        log.info("成功扣减次数: {}, 失败次数: {}", successCount.get(), failCount.get());
        
        // 验证最终库存
        int finalStock = stockUtils.getStock(stockKey);
        log.info("最终库存: {}", finalStock);
        
        // 成功扣减的次数应该等于初始库存
        assert successCount.get() == initialStock;
        // 最终库存应该为0
        assert finalStock == 0;
        // 失败次数应该等于总线程数减去成功次数
        assert failCount.get() == threadCount - initialStock;
    }

    @Test
    @DisplayName("测试预扣库存")
    public void testPreDeductStock() {
        String stockKey = "test_pre_deduct";
        int initialStock = 5;
        
        // 初始化库存
        stockUtils.initStock(stockKey, initialStock);
        
        // 预扣库存
        StockUtils.PreDeductResult result = stockUtils.preDeductStock(stockKey, 2, 10);
        log.info("预扣结果: success={}, remainingStock={}", 
                result.isSuccess(), result.getRemainingStock());
        
        assert result.isSuccess();
        assert result.getRemainingStock() == initialStock - 2;
        
        // 确认预扣
        stockUtils.confirmPreDeduct(stockKey, result.getLockValue());
        
        // 验证库存
        int finalStock = stockUtils.getStock(stockKey);
        log.info("确认后库存: {}", finalStock);
        assert finalStock == initialStock - 2;
    }

    @Test
    @DisplayName("测试预扣库存回滚")
    public void testPreDeductStockRollback() {
        String stockKey = "test_pre_deduct_rollback";
        int initialStock = 5;
        
        // 初始化库存
        stockUtils.initStock(stockKey, initialStock);
        
        // 预扣库存
        StockUtils.PreDeductResult result = stockUtils.preDeductStock(stockKey, 2, 10);
        log.info("预扣结果: success={}, remainingStock={}", 
                result.isSuccess(), result.getRemainingStock());
        
        assert result.isSuccess();
        
        // 回滚预扣
        stockUtils.rollbackPreDeduct(stockKey, 2, result.getLockValue());
        
        // 验证库存恢复
        int finalStock = stockUtils.getStock(stockKey);
        log.info("回滚后库存: {}", finalStock);
        assert finalStock == initialStock;
    }
}
