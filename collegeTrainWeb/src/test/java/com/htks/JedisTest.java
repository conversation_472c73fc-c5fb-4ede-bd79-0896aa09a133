package com.htks;

import static com.htks.common.SystemConfig.APP_AGENT_PREFIX;

import com.htks.common.utils.JedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @date 2022/07/21.
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = App.class)
@DisplayName("JedisTest")
public class JedisTest {

  @Autowired
  private JedisUtils jedisUtils;

  @Test
  public void tes00() {
    log.info("测试00:{}", jedisUtils.get(APP_AGENT_PREFIX + "1000044"));
  }

  @Test
  public void tes01() {
    for (int i = 0; i < 10; i++) {
      //jedisUtils.del("test:bourne");
      jedisUtils.set("test:bourne", "猫德" + i);
      //log.info("Jedis:{}", jedisUtils.getJedis());
      log.info("查询结果:{}", jedisUtils.get("test:bourne"));
    }
  }
}
