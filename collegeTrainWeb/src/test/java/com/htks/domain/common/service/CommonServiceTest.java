package com.htks.domain.common.service;

import com.htks.domain.common.dto.AttachmentEntity;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CommonServiceTest {
    @Resource
    private CommonService service;
    //pass
    @Test
    void getParameterDropDown() {
    }
    //pass
    @Test
    void getEmployeeBaseInfo() {
        service.getEmployeeBaseInfo("31973");
    }
    //pass
    @Test
    void getEmployeeBaseInfoByWx() {
        service.getEmployeeBaseInfoByWx("31973");

    }
    //pass
    @Test
    void getDictionary() {

        service.getDictionary("position_grade");

    }
    //pass
    @Test
    void getSkillDictionary() {
    }
    //pass
    @Test
    void getSkillGroupDictionary() {
    }
    //pass
    @Test
    void getBusinessUnit() {
    }
    //pass
    @Test
    void getDepartment() {
        long id = 70;
        service.getDepartment(id);

    }
    //pass
    @Test
    void getClassBan() {
    }
    //pass
    @Test
    void getZn() {
    }
    //pass
    @Test
    void addAttachmentInfoToDB() {
        AttachmentEntity attachmentEntity = new AttachmentEntity();
        service.addAttachmentInfoToDB(attachmentEntity);

    }
    //pass
    @Test
    void getCommonRoleInfo() {

        service.getCommonRoleInfo("1");

    }
    //pass
    @Test
    void getAttachmentByAnswerId() {
    }
    //pass
    @Test
    void getEmployeeNoByUserId() {

        service.getEmployeeNoByUserId("");
    }
    //pass
    @Test
    void geyRoleByEmployeeNo() {
        service.geyRoleByEmployeeNo("31973");
    }
    //pass
    @Test
    void getSumByCollegeNo() {
        service.getSumByCollegeNo("31973");

    }
    //pass
    @Test
    void fileUpload() throws IOException {
        String filePath = "/Users/<USER>/Desktop/123.txt"; // 请替换成你电脑上123.txt文件的实际路径
        File file = new File(filePath);
        String originalFilename = file.getName();
        String contentType = "text/plain";

        try {
            byte[] fileContent = Files.readAllBytes(file.toPath());
            MockMultipartFile multipartFile = new MockMultipartFile("file", originalFilename, contentType, fileContent);
            service.fileUpload(multipartFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}