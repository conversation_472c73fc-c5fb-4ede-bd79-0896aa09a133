package com.htks.domain.ExamInformation.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class SatisfactionCountServiceTest {
@Resource
private  SatisfactionCountService service;
    //pass
    @Test
    void getSatisfactionInformation() {
        List<String>department=new ArrayList<>();
        List<String>area=new ArrayList<>();
        service.getSatisfactionInformation("2024-01-23","2024-01-25","202401","胡泽昊","31973",department,area);
    }
    //pass
    @Test
    void getAssessedId() {
        List<String>department=new ArrayList<>();
        List<String>area=new ArrayList<>();
        List<String>factoryType=new ArrayList<>();
        String classNo = "1";
        service.getAssessedId("202401","胡泽昊","31973",department,area,factoryType,classNo);


    }
    //pass
    @Test
    void getHR() {
        long id=36;
        System.out.println(service.getHR(id));

    }
//pass
    @Test
    void getWechatId() {
        System.out.println(service.getWechatId("31973"));
    }

}