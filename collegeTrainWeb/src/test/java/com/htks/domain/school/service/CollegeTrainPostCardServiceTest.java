package com.htks.domain.school.service;

import com.htks.domain.school.request.CollegeTrainPostCardRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainPostCardServiceTest {
@Resource
private CollegeTrainPostCardService service;
    //pass
    @Test
    void queryPaperList() {
        CollegeTrainPostCardRequest request =new CollegeTrainPostCardRequest();
        request.setBatch("202401");
        request.setEmployeeNo("31973");
        request.setEmployeeName("胡泽昊");
        request.setPracticeDepartmentAreaId("54");
        List<String>list=new ArrayList<>();
        list.add("WLP工程部");
        request.setPracticeDepartmentNameList(list);
        service.queryPaperList(request);
    }
}