package com.htks.domain.school.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainAppointRecordDetailServiceTest {
@Resource
private CollegeTrainAppointRecordDetailService service;
//pass
    @Test
    void getAppointRecordDetail() {
        long id=35;
        service.getAppointRecordDetail(id);
    }
}