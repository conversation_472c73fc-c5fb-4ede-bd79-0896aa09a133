package com.htks.domain.school.service;

import cn.hutool.json.JSONUtil;
import com.htks.domain.school.dto.CollegeTrainAssessed;
import com.htks.domain.school.request.CollegeTrainAssessedRequest;
import com.htks.domain.school.request.CollegeTrainEnabledRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
@Slf4j
class CollegeTrainAssessedServiceTest {
@Resource
private CollegeTrainAssessedService service;
    //pass
    @Test
    void checkExist() {
        service.checkExist("ks031973");
    }
    //pass
    @Test
    void getByEmployeeNo() {
        service.getByEmployeeNo("31973");

    }
    @Test
    void addCollegeTrainAssessed() {
        CollegeTrainAssessed assessed=new CollegeTrainAssessed();
        assessed.setBatch("202401");
        assessed.setEmployeeName("张凯");
        assessed.setEmployeeNo("30876");
        assessed.setEntryDate("2024-01-30");
        service.addCollegeTrainAssessed(assessed);
    }

    @Test
    void updateCollegeTrainAssessed() {
        CollegeTrainAssessed assessed=new CollegeTrainAssessed();
        assessed.setBatch("202401");
        assessed.setEmployeeName("张凯");
        assessed.setEmployeeNo("30876");
        assessed.setEntryDate("2024-01-30");
        service.updateCollegeTrainAssessed(assessed);
    }

    //pass
    @Test
    void queryPaperList() {
        CollegeTrainAssessedRequest assessed=new CollegeTrainAssessedRequest();
        assessed.setBatch("202401");
        assessed.setEmployeeName("张凯");
        assessed.setEmployeeNo("30876");
        service.queryPaperList(assessed);
    }
    //pass
    @Test
    void getSpecifiedColumn() {
        service.getSpecifiedColumn("EMPLOYEE_NO");
    }
    //pass
    @Test
    void getByAssessedIdList() {
        List<Long>list=new ArrayList<>();
        list.add(36L);
        list.add(38L);
        service.getByAssessedIdList(list);

    }
    //pass
    @Test
    void updateEnabledTest() {
        CollegeTrainEnabledRequest request = new CollegeTrainEnabledRequest();
        request.setEmployeeNo("31187");
        request.setEnabled("关闭");
        log.info(JSONUtil.toJsonStr(service.updateEnabled(request)));

    }
}