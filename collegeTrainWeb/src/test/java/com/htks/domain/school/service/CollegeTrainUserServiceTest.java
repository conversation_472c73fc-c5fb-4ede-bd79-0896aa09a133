package com.htks.domain.school.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainUserServiceTest {
@Resource
private CollegeTrainUserService service;
    //pass
    @Test
    void checkExist() {
        service.checkExist("31973");
    }
    //pass
    @Test
    void getByEmployeeNo() {
        service.getByEmployeeNo("31973");

    }
    //pass
    @Test
    void getAllJudge() {
        service.getAllJudge("100");

    }


}