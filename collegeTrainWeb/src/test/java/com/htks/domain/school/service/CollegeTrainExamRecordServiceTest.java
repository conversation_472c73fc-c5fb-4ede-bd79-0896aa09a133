package com.htks.domain.school.service;

import com.htks.common.exception.base.enums.CommonExceptionEnum;
import com.htks.domain.school.request.CollegeTrainExamRecordAnswerRequest;
import com.htks.domain.school.request.CollegeTrainExamRecordRequest;
import com.htks.domain.school.request.StartReviewingRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainExamRecordServiceTest {
@Resource
private CollegeTrainExamRecordService service;
    //pass
    @Test
    void queryPaperList() {
        CollegeTrainExamRecordRequest request =new CollegeTrainExamRecordRequest();
        request.setBatch("202401");
        service.queryPaperList(request);
    }

    //pass
    @Test
    void assignExaminer() {
        CollegeTrainExamRecordRequest request =new CollegeTrainExamRecordRequest();
        request.setBatch("202401");
        request.setExamName("工程考试");
        request.setJudgeNo("31461");
        request.setJudgeName("测试");
        long id=78;
        request.setId(id);
        service.assignExaminer(request);
    }

//pass
    @Test
    void getForApprovalPageList() {
        CollegeTrainExamRecordRequest request =new CollegeTrainExamRecordRequest();
        request.setBatch("202401");
        service.getForApprovalPageList(request);

    }
    //pass
    @Test
    void startReviewing() {
        StartReviewingRequest request=new StartReviewingRequest();
        request.setExamId(20l);
        request.setEvaluateReason("测试");
        request.setSubjectiveScore(new BigDecimal("25"));
        List<CollegeTrainExamRecordAnswerRequest> list=new ArrayList<>();
        CollegeTrainExamRecordAnswerRequest request1=new CollegeTrainExamRecordAnswerRequest();
        request1.setId(11l);
        request1.setScore(BigDecimal.valueOf(25));
        list.add(request1);
        request.setExamRecordAnswerList(list);
        service.startReviewing(request);

    }
    //pass
    @Test
    void getApprovedPageList() {
        CollegeTrainExamRecordRequest request =new CollegeTrainExamRecordRequest();
        request.setBatch("202401");

        service.getApprovedPageList(request);

    }
    //pass
    @Test
    void getEngineeringPageList() {
        CollegeTrainExamRecordRequest request =new CollegeTrainExamRecordRequest();
        request.setBatch("202401");
        service.getEngineeringPageList(request);

    }

    //pass
    @Test
    void getSpecifiedColumn() {
        service.getSpecifiedColumn("正式考试");

    }
    //pass
    @Test
    void getCountByAssessedId() {
        service.getCountByAssessedId("39");

    }

    @Test
    void delete() {
        service.delete("41");
    }
    //pass
    @Test
    void getEmployeeInfo() {
        service.getEmployeeInfo("31973");

    }
}