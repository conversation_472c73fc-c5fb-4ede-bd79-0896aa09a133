package com.htks.domain.school.service;

import com.htks.domain.school.dto.CollegeTrainSopUpload;
import com.htks.domain.school.dto.CollegeTrainSopUploadDetail;
import com.htks.domain.school.request.CollegeTrainSopUploadRequest;
import com.htks.domain.school.request.SopEvaluateRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainSopUploadServiceTest {
@Resource
private CollegeTrainSopUploadService service;
    //PASS
    @Test
    void queryPaperList() {
        CollegeTrainSopUploadRequest request=new CollegeTrainSopUploadRequest();
        request.setBatch("202401");
        request.setEmployeeNo("31973");
        request.setType(1);
        service.queryPaperList(request);
    }

    //PASS
    @Test
    void assignExaminer() {
        CollegeTrainSopUploadRequest request=new CollegeTrainSopUploadRequest();
        request.setId(12l);
        request.setGraderNo("31973");
        request.setGraderName("测试");
        service.assignExaminer(request);
    }
    //PASS
    @Test
    void sopEvaluate() {
        SopEvaluateRequest request=new SopEvaluateRequest();
        request.setScore(85);
        request.setEvaluateReason("测试");
        request.setSopUploadId(25l);
        CollegeTrainSopUploadDetail collegeTrainSopUploadDetail=new CollegeTrainSopUploadDetail();
        collegeTrainSopUploadDetail.setEasyUnderstand("85");
        request.setSopUploadDetail(collegeTrainSopUploadDetail);
        service.sopEvaluate(request);
    }


    @Test
    void insertSopUpload() {
        CollegeTrainSopUpload sopUpload=new CollegeTrainSopUpload();
        sopUpload.setAssessedId(39l);
        sopUpload.setGraderName("测试");
        sopUpload.setType(1);
        sopUpload.setScore(85);
        sopUpload.setAttachmentId(2l);
        sopUpload.setGraderNo("31463");
        sopUpload.setSopName("sss");
        sopUpload.setEvaluateReason("");
        sopUpload.setGradingNumber("31461");
        service.insertSopUpload(sopUpload);
    }
    //PASS
    @Test
    void getSpecifiedColumn() {
        service.getSpecifiedColumn("未评价");

    }
    //PASS
    @Test
    void getSopGrader() {
        service.getSopGrader();

    }
}