package com.htks.domain.school.service;

import com.htks.domain.school.request.CollegeTrainWeeklyReportRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainWeeklyReportServiceTest {
@Resource
private CollegeTrainWeeklyReportService service;

//PASS
    @Test
    void getPageList() {
        CollegeTrainWeeklyReportRequest request=new CollegeTrainWeeklyReportRequest();
        request.setBatch("202401");
        request.setEmployeeNo("31973");
        service.getPageList(request);
    }
}