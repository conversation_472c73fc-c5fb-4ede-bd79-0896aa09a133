package com.htks.domain.school.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
@Slf4j
class CollegeTrainPostAreaServiceTest {
@Resource
private CollegeTrainPostAreaService service;
    //pass
    @Test
    void getAllDepartment() {
        log.info(service.getAllDepartment("华天昆山").toString());
    }
    //pass
    @Test
    void getAreaByDepartment() {
        service.getAreaByDepartment("Bumping工程部","");
    }
    //pass
    @Test
    void getByDepartmentAndArea() {
        service.getByDepartmentAndArea("Bumping工程部","1","华天昆山");
    }
    //pass
    @Test
    void getByDepartmentList() {
        List<String> list=new ArrayList<>();
        list.add("Bumping工程部");
        list.add("WLP工程部");
        service.getByDepartmentList(list);
    }
    //pass
    @Test
    void getByAreaList() {
        List<String> list=new ArrayList<>();
        list.add("1");
        list.add("54");
        service.getByAreaList(list);

    }
    //pass
    @Test
    void getByDepartmentAndAreaList() {
        List<String> list1=new ArrayList<>();
        list1.add("Bumping工程部");
        list1.add("WLP工程部");
        List<String> list2=new ArrayList<>();
        list2.add("1");
        list2.add("54");
        service.getByDepartmentAndAreaList(list1,list2);

    }
}