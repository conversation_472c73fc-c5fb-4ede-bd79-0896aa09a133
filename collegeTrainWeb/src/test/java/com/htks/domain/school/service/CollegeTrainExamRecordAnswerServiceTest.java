package com.htks.domain.school.service;

import com.htks.domain.school.request.CollegeTrainExamRecordAnswerRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainExamRecordAnswerServiceTest {
@Resource
private CollegeTrainExamRecordAnswerService service;
    //pass
    @Test
    void getExamRecordAnswer() {
        long id =12;
    service.getExamRecordAnswer(id,"填空题");
    }
    //pass
    @Test
    void updateExamRecordAnswer() {
        List<CollegeTrainExamRecordAnswerRequest> examRecordAnswerList =new ArrayList<>();
        CollegeTrainExamRecordAnswerRequest request=new CollegeTrainExamRecordAnswerRequest();
        request.setScore(BigDecimal.valueOf(85));
        long id =12;
        request.setId(id);
        examRecordAnswerList.add(request);
        service.updateExamRecordAnswer(examRecordAnswerList);

    }
}