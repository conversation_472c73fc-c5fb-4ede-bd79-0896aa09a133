package com.htks.domain.school.service;

import com.htks.domain.school.request.CollegeTrainAppointRecordRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainAppointRecordServiceTest {
@Resource
private CollegeTrainAppointRecordService service;
//pass
    @Test
    void queryPaperList() {
        CollegeTrainAppointRecordRequest request=new CollegeTrainAppointRecordRequest();
        request.setBatch("202401");
        service.queryPaperList(request);
    }
    //pass
    @Test
    void getAppointRecordDetail() {
        long id=36;
        service.getAppointRecordDetail(id);

    }
}