package com.htks.domain.school.service;

import com.htks.domain.school.dto.CollegeTrainMeetDetail;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainMeetDetailServiceTest {
@Resource
 private    CollegeTrainMeetDetailService service;
    //pass
    @Test
    void saveOrUpdateMeetDetail() {
        List<CollegeTrainMeetDetail> collect=new ArrayList<>();
        CollegeTrainMeetDetail detail=new CollegeTrainMeetDetail();
        detail.setMeetId(25l);
        detail.setJudgeName("测试");
        detail.setScore(BigDecimal.valueOf(85));
        detail.setJudgeNo("5555");
        collect.add(detail);
        Long meetId=11l;
        String type="add";

        service.saveOrUpdateMeetDetail(collect,meetId,type);
    }
}