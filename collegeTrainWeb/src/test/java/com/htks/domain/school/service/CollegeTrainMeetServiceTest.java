package com.htks.domain.school.service;

import com.htks.domain.school.request.CollegeTrainMeetRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainMeetServiceTest {
@Resource
private  CollegeTrainMeetService service;
    //pass
    @Test
    void getMeetDetail() {
        service.getMeetDetail(25l);
    }
    //pass
    @Test
    void queryPaperList() {
        CollegeTrainMeetRequest request=new CollegeTrainMeetRequest();
        request.setBatch("202401");
        service.queryPaperList(request);

    }
}