package com.htks.domain.school.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CommonEmployeeServiceTest {
@Resource
private  CommonEmployeeService service;
//PASS
    @Test
    void getEmployeeInfo() {
        service.getEmployeeInfo("31973");
    }
    //PASS
    @Test
    void getEmployeeListByDepartmentName() {
        service.getEmployeeListByDepartmentName("WLP工程部");

    }
    //PASS
    @Test
    void getEmployeeInfoTwo() {
        service.getEmployeeInfo("31973","在职");

    }
}