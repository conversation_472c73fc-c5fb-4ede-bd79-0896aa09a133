package com.htks.domain.school.service;

import com.htks.domain.school.request.CollegeTrainSubjectiveJudgeRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CollegeTrainSubjectiveJudgeServiceTest {
@Resource
private CollegeTrainSubjectiveJudgeService service;
    //pass
    @Test
    void getPageList() {
        CollegeTrainSubjectiveJudgeRequest request=new CollegeTrainSubjectiveJudgeRequest();
        request.setBatch("202401");
        service.getPageList(request);
    }

    @Test
    void downTemplate() {

    }
    //pass
    @Test
    void startEvaluation() {
        CollegeTrainSubjectiveJudgeRequest request=new CollegeTrainSubjectiveJudgeRequest();
        request.setBatch("202401");
        service.startEvaluation(request);

    }

}