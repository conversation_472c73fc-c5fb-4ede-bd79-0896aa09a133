package com.htks.domain.admin.service;

import com.htks.domain.admin.AdminCondition;
import com.htks.domain.common.dto.UpgradeUserAdminEntity;
import org.checkerframework.checker.units.qual.A;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class AdminServiceTest {

@Resource
private AdminService adminService;

    //pass
    @Test
    void queryAdminCount() {
        AdminCondition adminCondition=new AdminCondition();
        adminCondition.setDepartmentName("资讯技术部");
        adminCondition.setEmployeeName("胡泽昊");
        adminCondition.setEmployeeNo("31973");
        System.out.println(adminService.queryAdminCount(adminCondition));
    }
    //pass
    @Test
    void queryAdminList() {
        AdminCondition adminCondition=new AdminCondition();
        adminCondition.setDepartmentName("资讯技术部");
        adminCondition.setEmployeeName("胡泽昊");
        adminCondition.setEmployeeNo("31973");
        System.out.println(adminService.queryAdminList(adminCondition));
    }
    //pass
    @Test
    void deleteAdmin() {
        long id=55;
        System.out.println(adminService.deleteAdmin(id));
    }
    //pass
    @Test
    void updateAdmin() {
        UpgradeUserAdminEntity upgradeUserAdminEntity=new UpgradeUserAdminEntity();
        upgradeUserAdminEntity.setEmployeeName("胡泽昊");
        upgradeUserAdminEntity.setEmployeeNo("31973");
        upgradeUserAdminEntity.setRoleId("5");
        System.out.println(adminService.updateAdmin(upgradeUserAdminEntity));


    }
    //pass
    @Test
    void addAdmin() {
        UpgradeUserAdminEntity upgradeUserAdminEntity=new UpgradeUserAdminEntity();
        upgradeUserAdminEntity.setEmployeeName("胡泽昊");
        upgradeUserAdminEntity.setEmployeeNo("31973");
        upgradeUserAdminEntity.setRoleId("5");
        upgradeUserAdminEntity.setCreatedUser("admin");
        System.out.println(adminService.addAdmin(upgradeUserAdminEntity));
    }
}