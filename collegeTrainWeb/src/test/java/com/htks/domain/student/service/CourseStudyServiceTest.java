package com.htks.domain.student.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CourseStudyServiceTest {
@Resource
private CourseStudyService service;
    //pass
    @Test
    void getAccessId() {
        service.getAccessId("31973");
    }
    //pass
    @Test
    void getCourseId() {
        service.getCourseId(service.getAccessId("31973"));

    }

    @Test
    void getVideoUrl() {
        service.getVideoUrl(55l);

    }

    //pass
    @Test
    void getDeadTime() {
        service.getDeadTime(41l);
    }
    //pass
    @Test
    void getCourseDetail() {
        service.getCourseDetail(39l,"HR公开课","");

    }
    //pass
    @Test
    void changeStudyStatus() {
        service.changeStudyStatus("已完成","2024-01-30",39l,41l);

    }
    //pass
    @Test
    void addStudyLog() {
        service.addStudyLog(633l);

    }
    //pass
    @Test
    void getCourseItemId() {
        service.getCourseItemId(39l,41l);

    }
    //pass
    @Test
    void getExamScore() {
        service.getExamScore(39l,"HR公开课理论考试②");

    }
//pass
    @Test
    void getDepartmentName() {
        service.getDepartmentName("31973");

    }

    @Test
    void pdfToPicture() throws IOException {
        String filePath = "/Users/<USER>/Desktop/123.pdf"; // 请替换成你电脑上123.txt文件的实际路径
        service.pdfToPicture(filePath);
    }
}