package com.htks.domain.student.service;

import com.htks.domain.common.dto.AttachmentEntity;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class UploadPostCardServiceTest {
@Resource
public UploadPostCardService service;
    //pass
    @Test
    void getWorkLicense() {
        service.getWorkLicense("研磨工艺","31973");
    }
    //pass
    @Test
    void findDepartmentCard() {
        service.findDepartmentCard("研磨工艺","31973");

    }
    //pass
    @Test
    void findAreaCard() {
        service.findAreaCard("研磨工艺","31973");

    }

    @Test
    void getWorkLicenseArea() {
        service.getWorkLicenseArea("研磨工艺","31973");

    }
    //pass
    @Test
    void postUploadStartTime() {
        service.postUploadStartTime("31973");
    }
    //pass
    @Test
    void postUploadEndTime() {
        service.postUploadEndTime("31973");

    }
    //pass
    @Test
    void uploadWorkLicense() {
        service.uploadWorkLicense("研磨工艺","研磨工艺A",40l,"","");

    }

    @Test
    void getAreaList() {
        service.getAreaList("34665");

    }

    //pass
    @Test
    void getPostCardId() {
        service.getPostCardId("","研磨工艺A","");
    }
    //pass
    @Test
    void getWorkLicense21() {
        service.getWorkLicense21("31973");

    }
    //pass
    @Test
    void getWorkLicense22() {
        service.getWorkLicense22(service.getWorkLicense21("31973"));
    }
    //pass
    @Test
    void getWorkLicense23() {
        service.getWorkLicense23("研磨工艺",service.getWorkLicense22(service.getWorkLicense21("31973")));
    }

    @Test
    void addPostCardPicture() {
        AttachmentEntity attachmentEntity=new AttachmentEntity();
        Long postCardId=1l;
        service.addPostCardPicture(attachmentEntity,postCardId,null);

    }


}