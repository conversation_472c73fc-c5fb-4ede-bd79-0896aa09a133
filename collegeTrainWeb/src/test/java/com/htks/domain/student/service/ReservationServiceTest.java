package com.htks.domain.student.service;

import com.htks.domain.student.dto.AppointMent;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class ReservationServiceTest {
@Resource
private ReservationService service;
    //pass
    @Test
    void timeAppoitGet() {
        service.timeAppoitGet("2024-01-25","2024-02-25",1,"12:00:00","23:59:00","");
    }
    //pass
    @Test
    void allInformation() {
        service.allInformation("31973");

    }
    //pass
    @Test
    void allNumber() {
        service.allNumber("31973","");

    }
    //pass
    @Test
    void assessedNumber() {
        service.assessedNumber(1,"31973","华天昆山");

    }
    //pass
    @Test
    void judgePassed() {
        service.assessedNumber(1,"31973","");

    }

    @Test
    void addAppointment() {
        AppointMent appointMent=new AppointMent();
        appointMent.setType(1);
        appointMent.setEmployeeNo("30876");

        service.addAppointment(appointMent,40l);

    }
    //pass
    @Test
    void showAppointment() {
        service.showAppointment("31973",1);

    }
    //pass
    @Test
    void isCollegeIng() {
        service.isCollegeIng("31973",1);

    }
}