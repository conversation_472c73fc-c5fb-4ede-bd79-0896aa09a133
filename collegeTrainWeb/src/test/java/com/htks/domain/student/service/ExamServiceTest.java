package com.htks.domain.student.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.htks.common.external.wx.ResultVO;
import com.htks.domain.question.dto.QuestionEntity;
import com.htks.domain.question.dto.QuestionItemEntity;
import com.htks.domain.school.dto.CollegeTrainAttachment;
import com.htks.domain.school.excel.ExamQuestionExport;
import com.htks.domain.student.dto.ExamQuestion;
import com.htks.domain.student.repository.hana.ExamRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
@Slf4j
class ExamServiceTest {
@Resource
private ExamService service;
@Resource
private ExamRepository examRepository;
    //pass
    @Test
    void getAllExam() {
        service.getAllExam("31973","2024-01-25");
    }
    //pass
    @Test
    void getExamByArea() {
        service.getExamByArea("31973","2024-01-25");

    }
    //pass
    @Test
    void getPostPaperName() {
        service.getPostPaperName(8.0);
    }
    //pass
    @Test
    void passedExam() {
        service.passedExam("GC202401267502374");
    }
    //pass
    @Test
    void getPassAllExam() {
        service.getPassAllExam("GC202401267502374");
    }

    //pass
    @Test
    void getPaperTypeByid() {
        service.getPaperTypeByid(8l);

    }
    //pass
    @Test
    void addExamAnswer() {

    }
    //pass
    @Test
    void getAreaByDepartment() {
        service.getAreaByDepartment("31973");

    }
    //pass
    @Test
    void getExamInformation1() {
        service.getExamInformation1("BU公共课理论考试②");

    }
    //pass
    @Test
    void examInformation2() {
        service.examInformation2(8l);

    }
    //pass
    @Test
    void examInformation3() {
        List<Long>list=new ArrayList<>();
        list.add(25l);
        service.examInformation3("填空题",list,1);

    }
    //pass
    @Test
    void getPostQuestionContent() {
        service.getPostQuestionContent(25l);

    }
    //pass
    @Test
    void getPostExam1() {
        service.getPostExam1("31973");
    }
    //pass
    @Test
    void getPostExam2() {
        service.getPostExam2(6l);

    }

    @Test
    void getAnswerById() {
        service.getAnswerById(39l);

    }
    //pass
    @Test
    void getQuestionNumber() {
        service.getQuestionNumber("31973","BU公共课理论考试②");

    }
    //pass
    @Test
    void getPostExam() {

        service.getPostExam("31973","1");

    }
    //pass
    @Test
    void getPassExamByArea() {
        service.getPassExamByArea("31973","");

    }

//pass
    @Test
    void getAttachmentIdByPath() {
        service.getAttachmentIdByPath("http://10.160.240.150:9090/collegestudent/24012911420748feb3bac6b94bfb76bf《为什么是华天读后感 》.docx");

    }
    //pass
    @Test
    void getExamId() {
        service.getExamId(40l);

    }
    //pass
    @Test
    void getExamIdByName() {
        service.getExamIdByName(40l,"BU公共课理论考试②");
    }
    //pass
    @Test
    void getPostExamId() {
        service.getPostExamId(40l,"1");

    }

//pass
    @Test
    void afterAddExamLog() {
        service.afterAddExamLog("31973","BU公共课理论考试②");

    }
    //pass
    @Test
    void afterAddPostExamLog() {
        service.afterAddPostExamLog("31973","1");

    }
    //pass
    @Test
    void addAttachmentId() {
        CollegeTrainAttachment collegeTrainAttachment=new CollegeTrainAttachment();
        collegeTrainAttachment.setAttachmentPath("22222");
        collegeTrainAttachment.setQuestionId(55l);
        service.addAttachmentId(collegeTrainAttachment,"31973");
    }
    //pass
    @Test
    void getAttachmentIdSum() {
        CollegeTrainAttachment collegeTrainAttachment=new CollegeTrainAttachment();
        collegeTrainAttachment.setAttachmentPath("22222");
        collegeTrainAttachment.setQuestionId(55l);
        service.getAttachmentIdSum(collegeTrainAttachment,"34665","2024-06-11");
    }
    //pass
    @Test
    void updateAttachmentById() {
        CollegeTrainAttachment collegeTrainAttachment=new CollegeTrainAttachment();
        collegeTrainAttachment.setAttachmentPath("22222");
        collegeTrainAttachment.setQuestionId(55l);
        service.updateAttachmentById(collegeTrainAttachment,55l);
    }


    //pass
    @Test
    void getAttachmentId() {
        CollegeTrainAttachment collegeTrainAttachment=new CollegeTrainAttachment();
        collegeTrainAttachment.setAttachmentPath("22222");
        collegeTrainAttachment.setQuestionId(55l);
        service.getAttachmentId(collegeTrainAttachment,"34665","2024-06-11");
    }
    //pass
    @Test
    void getCollegeBatch() {
        service.getCollegeBatch("31973");

    }
    //pass
    @Test
    void getFirstBatch() {
        service.getFirstBatch("2024");

    }
    //pass
    @Test
    void getIQExam() {
        service.getIQExam("BU公共课理论考试①");
    }

//pass
    @Test
    void getexamSum() {
        service.getexamSum(40l,"BU公共课理论考试①");

    }

    //pass
    @Test
    void getExamCatyId() {
        service.getExamCatyId("工艺公开课理论考试①");

    }
    //pass
    @Test
    void getExamType() {
        service.getExamType("工艺公开课理论考试①");

    }
    //pass
    @Test
    void getPostCount() {
        service.getPostCount(39l,"1");
    }

    /**
     * 导出试题
     * IQ测试
     * 新生见面会
     * HR公开课理论考试①
     * HR公开课理论考试②
     * 工程结业理论考试
     * 培训组长主观评价
     * BU公共课理论考试①
     * BU公共课理论考试②
     * 上岗证理论考试
     * 上岗证考核
     * 工程类实操考试
     * SOP制作
     * 异常答辩
     * 工艺公开课理论考
     */
    @Test
    void getOtherExamQuestionTest(){
        String emno = "40489";
        ResultVO resultVO = service.getOtherExamQuestion("工艺公开课理论考试①",emno);
        log.info(JSONObject.toJSONString(resultVO.getData()));
        List<ExamQuestion> questionEntities = (List<ExamQuestion>)resultVO.getData();
        List<ExamQuestionExport> export = new ArrayList<>();
        for(int i=0;i<questionEntities.size();i++){
            List<QuestionItemEntity> questionItemEntityList = questionEntities.get(i).getQuestionItemEntityList();
            ExamQuestionExport examQuestionExport = new ExamQuestionExport();
            examQuestionExport.setNumber(i+1);
            examQuestionExport.setQuestionContent(questionEntities.get(i).getQuestionContent());
            examQuestionExport.setQuestionType(questionEntities.get(i).getQuestionType());
            try{
                examQuestionExport.setOptionA(questionItemEntityList.get(0).getItemContent());
                examQuestionExport.setOptionB(questionItemEntityList.get(1).getItemContent());
                examQuestionExport.setOptionC(questionItemEntityList.get(2).getItemContent());
                examQuestionExport.setOptionD(questionItemEntityList.get(3).getItemContent());
                examQuestionExport.setOptionE(questionItemEntityList.get(4).getItemContent());
                examQuestionExport.setOptionF(questionItemEntityList.get(5).getItemContent());
            }catch (Exception e){
                log.info("没有选项");
            }
            examQuestionExport.setStandardAnswer(questionEntities.get(i).getStandardAnswer());
            export.add(examQuestionExport);
        }
        String dep=examRepository.getDep(emno);
        String comp=examRepository.getCompany(emno);
        if(dep.equals("CP/COF工程部")){
            dep="CP COF工程部";
        }
        if(dep.equals("RW/COG工程部")){
            dep="RW COG工程部";
        }
        File file = new File("D:\\\\Download\\\\"+dep+comp+"试题导出.xlsx");
        EasyExcel.write("D:\\\\Download\\\\"+dep+comp+"试题导出.xlsx", ExamQuestionExport.class).sheet("sheet1").doWrite(export);
    }
}
