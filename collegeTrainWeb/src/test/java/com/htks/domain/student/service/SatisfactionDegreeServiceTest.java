package com.htks.domain.student.service;

import com.htks.domain.student.dto.SatisfactionDegree;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class SatisfactionDegreeServiceTest {
@Resource
public SatisfactionDegreeService satisfactionDegreeService;
    @Test
    void inserDegree() {
        SatisfactionDegree satisfactionDegree=new SatisfactionDegree();
        satisfactionDegree.setEmployeeNo("31973");
        satisfactionDegree.setSCORE(100);
        satisfactionDegreeService.inserDegree(satisfactionDegree,39l);
    }
    //pass
    @Test
    void onboardingTime() {
        satisfactionDegreeService.onboardingTime("31973");

    }
    //pass
    @Test
    void appraiseIsNull() {
        satisfactionDegreeService.appraiseIsNull("31973");

    }
    //pass
    @Test
    void getThisWeekMonday() {
        Date today = new Date();
        satisfactionDegreeService.getThisWeekMonday(today);

    }
    //pass
    @Test
    void getAccessId() {
        satisfactionDegreeService.getAccessId("31973");
    }
    //pass
    @Test
    void judgeIsfirst() {
        satisfactionDegreeService.judgeIsfirst(39l);

    }
    //pass
    @Test
    void newDate() {
        satisfactionDegreeService.newDate("31973");

    }
    //pass
    @Test
    void getNewScore() {
        satisfactionDegreeService.getNewScore("31973");

    }
}