package com.htks.domain.pushCourse;

import cn.hutool.json.JSONUtil;
import com.htks.domain.pushCourse.dto.PushCourseCondition;
import com.htks.domain.pushCourse.dto.PushCourseEntityDto;
import com.htks.domain.pushCourse.dto.PushExamCondition;
import com.htks.domain.pushCourse.dto.PushExamEntityDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
@Slf4j
class PushCourseServiceTest {
@Resource
private PushCourseService service;
    //pass
    @Test
    void queryPushCourseList() {
        PushCourseCondition condition=new PushCourseCondition();
        condition.setBatch("202403");
        condition.setCategory("HR公开课");
        condition.setPushObject("批次内全员");
        condition.setCreator("32560");
        log.info(JSONUtil.toJsonStr(service.queryPushCourseList(condition)));
    }
    //pass
    @Test
    void queryPushExamList() {
        PushExamCondition condition=new PushExamCondition();
        condition.setBatch("202401");
        condition.setCategory("HR公开课");
        condition.setPushObject("批次内全员");
        condition.setCreator("31461");
        List<String>list=new ArrayList<>();
        list.add("工艺公开课理论考试①");
        condition.setCategoryItem(list);
        condition.setFactoryFlagList(Arrays.asList("华天昆山"));
        service.queryPushExamList(condition);

    }

    @Test
    void sendCourse() {
        PushCourseEntityDto dto=new PushCourseEntityDto();
        dto.setBatch("202407");
        dto.setCategory("HR公开课");
        dto.setFactoryFlag("华天昆山");
        dto.setPushObject("批次内全员");
        dto.setDeadTime("");
        service.sendCourse(dto);
    }

    @Test
    void sendExam() {
        PushExamEntityDto dto=new PushExamEntityDto();
        dto.setBatch("202401");
        dto.setCategory("HR公开课");
        dto.setCategoryItem("英语测试");
        dto.setFactoryFlag("华天昆山");
        dto.setExamStartTime("2024-06-08 10:00:00");
        service.sendExam(dto);
    }
    //pass
    @Test
    void getAdminNo() {
        service.getAdminNo("张作永");
    }
    //pass
    @Test
    void getExamDetail() {
        service.getExamDetail("HR公开课");

    }
    //pass
    @Test
    void getExam() {
        service.getExam();
    }
    //pass
    @Test
    void getCourse() {
        service.getCourse();
    }
}