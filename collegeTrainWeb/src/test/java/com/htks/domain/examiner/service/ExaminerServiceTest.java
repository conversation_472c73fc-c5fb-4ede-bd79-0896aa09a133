package com.htks.domain.examiner.service;

import com.htks.domain.examiner.dto.AppointGoal;
import com.htks.domain.examiner.dto.Examiner;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class ExaminerServiceTest {
@Resource
private ExaminerService service;
    //pass
    @Test
    void startApproval() {
        service.startApproval("31973");

    }
    @Test
    void checkApprovalNew() {

        service.checkApprovalNew("31636","31973",1);
    }    @Test
    void getCheckApprovalNo() {

        service.getCheckApprovalNo("31636","31973",1);
    }

    @Test
    void approvalComplete() {
        Examiner examiner=new Examiner();
        examiner.setEmployeeNo("31973");
        examiner.setScore(23);
        examiner.setType(1);
        long id=40;
        service.approvalComplete(examiner,id,"","1");
    }
    //pass
    @Test
    void checkApprovalFirst() {
        service.checkApprovalFirst("","31973",2);
    }
    //pass
    @Test
    void checkApprovalSecond() {
        service.checkApprovalSecond("31747");
    }
    //pass
    @Test
    void updateAPPOINT() {
        AppointGoal appointGoal=new AppointGoal();
        appointGoal.setScore(80.0);
        appointGoal.setStatus("已批阅");
        appointGoal.setPassed("通过");
        appointGoal.setPassedDate("2077-04-01");

        appointGoal.setAdditionalPoints("通过");
        service.updateAPPOINT(appointGoal,1);

    }
    //pass
    @Test
    void checkApprovalThird() {
        service.checkApprovalThird("31973",1);

    }
    //pass
    @Test
    void calculateEachGrades() {
        service.calculateEachGrades("31747",1,"31973");

    }
    //pass
    @Test
    void sureAppointRecordId() {
        System.out.println( service.sureAppointRecordId("31747",1));

    }
    //pass
    @Test
    void collegeName() {
        System.out.println( service.collegeName("31747"));

    }
    //pass
    @Test
    void getSatisfactionOrAbnormalDefenseCriteria() {
        System.out.println( service.getSatisfactionOrAbnormalDefenseCriteria(1));

    }
    //pass
    @Test
    void getSopOrHandsOnCriteria() {
        System.out.println( service.getSopOrHandsOnCriteria(2));

    }
}