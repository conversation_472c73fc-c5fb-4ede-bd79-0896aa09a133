package com.htks.domain.question;

import com.htks.domain.question.dto.PaperCondition;
import com.htks.domain.question.dto.PaperSaveCondition;
import com.htks.domain.question.dto.QuestionEntity;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class QuestionServiceTest {
@Resource
private QuestionService service;
    //pass
    @Test
    void queryPaperList() {
        PaperCondition condition=new PaperCondition();
        condition.setDepartment("WLP工程部");
        service.queryPaperList(condition);
    }

    //pass
    @Test
    void getDepartmentList() {
        service.getDepartmentList();
    }
    //pass
    @Test
    void categoryList() {
        service.categoryList();

    }
    //pass
    @Test
    void deletePaperQuestion() {
long id=662;
        service.deletePaperQuestion(id);

    }
    //pass
    @Test
    void getQuestionDetail() {
        long id=662;
        service.getQuestionDetail(id);
    }
}