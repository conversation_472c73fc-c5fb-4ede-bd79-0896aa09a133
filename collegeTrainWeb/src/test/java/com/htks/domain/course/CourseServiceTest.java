package com.htks.domain.course;

import com.htks.domain.course.dto.CourseCondition;
import com.htks.domain.course.dto.CourseEntity;
import com.htks.domain.course.dto.CourseSaveCondition;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class CourseServiceTest {
@Resource
private CourseService service;
    //pass
    @Test
    void queryCourseList() {

        CourseCondition condition=new CourseCondition();
        condition.setCategory("BU公共课");
        condition.setTitle("《WLP研磨工序介绍》");
        service.queryCourseList(condition);
    }
    //pass
    @Test
    void saveCourse() {
        CourseSaveCondition courseEntity=new CourseSaveCondition();
        long id=1;
        courseEntity.setAttachmentId(id);
        courseEntity.setCategory("BU");
        courseEntity.setCreator("31973");
        courseEntity.setTitle("测试");
        courseEntity.setDepartmentName("WLP工程部");
        service.saveCourse(courseEntity);

    }
    //pass
    @Test
    void deleteCourse() {
        long id=12222;
        service.deleteCourse(id);
    }
}