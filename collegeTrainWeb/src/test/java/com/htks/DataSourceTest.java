package com.htks;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.annotation.Resource;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = App.class)
@DisplayName("DataSourceTest")
public class DataSourceTest {

  /**
   * SpringBoot 默认已经配置好了数据源，直接注入使用
   */
  @Resource(name = "hanaDataSource")
  DataSource dataSource1;

  @Resource(name = "mysqlDataSource")
  DataSource dataSource2;

  @Test
  public void dbTest() throws SQLException {
    Connection connection1 = dataSource1.getConnection();
    Connection connection2 = dataSource2.getConnection();

    DatabaseMetaData metaData1 = connection1.getMetaData();
    DatabaseMetaData metaData2 = connection2.getMetaData();

    log.info("数据源:{}", dataSource1.getClass());
    log.info("连接:{}", connection1);
    log.info("连接地址:{}", connection1.getMetaData().getURL());
    log.info("驱动名称:{}", metaData1.getDriverName());
    log.info("驱动版本:{}", metaData1.getDriverVersion());
    log.info("数据库名称:{}", metaData1.getDatabaseProductName());
    log.info("数据库版本:{}", metaData1.getDatabaseProductVersion());
    log.info("连接用户名称:{}", metaData1.getUserName());
    Statement s1 = connection1.createStatement();
    ResultSet rs1 = s1.executeQuery("SELECT SEQ_COMMON.NEXTVAL FROM DUMMY");
    while (rs1.next()) {
      log.info("Hana查询结果:{}", rs1.getString(1));
    }
    rs1.close();
    s1.close();
    connection1.close();

    log.info("-----------------------分割线-----------------------");
    log.info("-----------------------分割线-----------------------");
    log.info("-----------------------分割线-----------------------");

    //数据源 class com.zaxxer.hikari.HikariDataSource
    log.info("数据源:{}", dataSource2.getClass());
    log.info("连接:{}", connection2);
    log.info("连接地址:{}", connection2.getMetaData().getURL());
    log.info("驱动名称:{}", metaData2.getDriverName());
    log.info("驱动版本:{}", metaData2.getDriverVersion());
    log.info("数据库名称:{}", metaData2.getDatabaseProductName());
    log.info("数据库版本:{}", metaData2.getDatabaseProductVersion());
    log.info("连接用户名称:{}", metaData2.getUserName());
    Statement s2 = connection2.createStatement();
    ResultSet rs2 = s2.executeQuery("SELECT NOW()");
    while (rs2.next()) {
      log.info("Mysql查询结果:{}", rs2.getString(1));
    }
    rs2.close();
    s2.close();
    connection2.close();

    log.info("-----------------------分割线-----------------------");
    log.info("-----------------------分割线-----------------------");
    log.info("-----------------------分割线-----------------------");

  }

}
