package com.htks.web.informationCount;

import com.htks.domain.ExamInformation.dto.ExamInformationReasonBody;
import com.htks.domain.pushCourse.impl.PushPostExamSchedule;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class InformationCountControllerTest {
@Resource
private PushPostExamSchedule pushPostExamSchedule;
    @Resource
    private InformationCountController informationCountController;

    @Test
    void examInformation() {
        ExamInformationReasonBody exam = new ExamInformationReasonBody();
        System.out.println(informationCountController.ExamInformation(exam).getData());
    }

@Test
    void asfdasdas(){
        pushPostExamSchedule.sendaaaa();
}
}