package com.htks.web.school.controller;

import com.htks.domain.school.request.CollegeTrainWeeklyReportRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class CollegeTrainWeeklyReportControllerTest {

    @Resource
    private CollegeTrainWeeklyReportController collegeTrainWeeklyReportController;

    @Test
    void getPageList() {
        CollegeTrainWeeklyReportRequest request = new CollegeTrainWeeklyReportRequest();
        System.out.println(collegeTrainWeeklyReportController.getPageList(request).getData());
    }
}