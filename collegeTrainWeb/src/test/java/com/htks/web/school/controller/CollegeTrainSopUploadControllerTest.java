package com.htks.web.school.controller;

import com.htks.domain.school.dto.CollegeSOPCondition;
import com.htks.web.JsonPagedVO;
import com.htks.web.Rest;
import com.htks.web.RestBody;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class CollegeTrainSopUploadControllerTest {

    @Resource
    private CollegeTrainSopUploadController collegeTrainSopUploadController;

    @Test
    void queryEvaluateDetail() {
        CollegeSOPCondition collegeSOPCondition = new CollegeSOPCondition();
        ArrayList<String> objects = new ArrayList<>();
        ArrayList<String> department = new ArrayList<>();
        department.add("Bumping工程部");
        objects.add("华天江苏");
        collegeSOPCondition.setFactoryType(objects);
        JsonPagedVO jsonPagedVO = collegeTrainSopUploadController.queryEvaluateDetail(collegeSOPCondition);
        System.out.println(jsonPagedVO.getData().toString());
    }

    @Test
    void querySopNames(){
        RestBody rest = (RestBody) collegeTrainSopUploadController.querySopNames();
        System.out.println(rest.getData());
    }

}