package com.htks.web.school.controller;

import com.htks.domain.school.request.CollegeTrainAppointRecordRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;


@SpringBootTest
class CollegeTrainAppointRecordControllerTest {

    @Resource
    private CollegeTrainAppointRecordController collegeTrainAppointRecordController;
    @Test
    void backAppointRecode() {
        collegeTrainAppointRecordController.backAppointRecode(78l);
    }

    @Test
    void getPageList(){
        CollegeTrainAppointRecordRequest request = new CollegeTrainAppointRecordRequest();
        System.out.println(collegeTrainAppointRecordController.getPageList(request).getData());
    }

}